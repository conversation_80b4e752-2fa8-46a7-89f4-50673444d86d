#!/bin/bash

# ═══════════════════════════════════════════════════════════════════════════════
# ⚙️ Notify Service API - Configuration Manager (Unix/Linux/macOS)
# ═══════════════════════════════════════════════════════════════════════════════
# Interactive configuration management for environment variables and secrets

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
GRAY='\033[0;37m'
DARK_GRAY='\033[1;30m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
ENV_FILE="$PROJECT_ROOT/.env"
ENV_TEMPLATE="$PROJECT_ROOT/.env.template"
SECRETS_FILE="$PROJECT_ROOT/secrets.json"

show_header() {
    local title=${1:-"Configuration Manager"}
    clear
    echo ""
    echo -e "${CYAN}    ⚙️⚙️⚙️ NOTIFY SERVICE API ⚙️⚙️⚙️${NC}"
    echo ""
    echo -e "${YELLOW}    🔧 $title${NC}"
    echo -e "${GRAY}    🔐 Environment Variables • Secrets • Docker Config${NC}"
    echo ""
    echo -e "${DARK_GRAY}═══════════════════════════════════════════════════════════════════════════════${NC}"
}

log_step() {
    local message=$1
    local status=${2:-"INFO"}
    local timestamp=$(date '+%H:%M:%S')
    
    case $status in
        "SUCCESS") echo -e "[$timestamp] ✅ $message" ;;
        "ERROR")   echo -e "[$timestamp] ❌ $message" ;;
        "WARNING") echo -e "[$timestamp] ⚠️  $message" ;;
        "INFO")    echo -e "[$timestamp] 🔄 $message" ;;
        "INPUT")   echo -e "[$timestamp] 📝 $message" ;;
    esac
}

log_section() {
    local title=$1
    echo ""
    echo -e "${DARK_GRAY}┌─────────────────────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${WHITE}│ $(printf "%-75s" "$title") │${NC}"
    echo -e "${DARK_GRAY}└─────────────────────────────────────────────────────────────────────────────┘${NC}"
}

get_input() {
    local prompt=$1
    local default_value=${2:-""}
    local is_secret=${3:-false}
    
    if [[ -n "$default_value" ]]; then
        if $is_secret; then
            echo -ne "${YELLOW}    $prompt [***]: ${NC}"
        else
            echo -ne "${YELLOW}    $prompt [$default_value]: ${NC}"
        fi
    else
        echo -ne "${YELLOW}    $prompt: ${NC}"
    fi
    
    if $is_secret; then
        read -s value
        echo ""
    else
        read value
    fi
    
    echo "${value:-$default_value}"
}

generate_secure_key() {
    openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64 | tr -d '\n'
}

initialize_config_files() {
    log_section "📁 Configuration File Setup"
    
    # Create .env from template if it doesn't exist
    if [[ ! -f "$ENV_FILE" ]]; then
        if [[ -f "$ENV_TEMPLATE" ]]; then
            log_step "Creating .env file from template..." "INFO"
            cp "$ENV_TEMPLATE" "$ENV_FILE"
            log_step ".env file created successfully" "SUCCESS"
        else
            log_step "Creating default .env file..." "INFO"
            create_default_env_file
            log_step "Default .env file created" "SUCCESS"
        fi
    else
        log_step ".env file already exists" "SUCCESS"
    fi
    
    # Create secrets.json if it doesn't exist
    if [[ ! -f "$SECRETS_FILE" ]]; then
        log_step "Creating secrets.json file..." "INFO"
        create_default_secrets_file
        log_step "secrets.json file created" "SUCCESS"
    else
        log_step "secrets.json file already exists" "SUCCESS"
    fi
}

create_default_env_file() {
    cat > "$ENV_FILE" << 'EOF'
# ═══════════════════════════════════════════════════════════════════════════════
# 🔧 Notify Service API - Environment Configuration
# ═══════════════════════════════════════════════════════════════════════════════

# Application Configuration
ASPNETCORE_ENVIRONMENT=Development
API_PORT=8080
ASPNETCORE_URLS=https://+:443;http://+:80

# Database Configuration
POSTGRES_PASSWORD=********
POSTGRES_USER=********
POSTGRES_DB=NotifyDb
POSTGRES_PORT=5432
POSTGRES_HOST=localhost

# Redis Configuration
REDIS_PASSWORD=
REDIS_PORT=6379
REDIS_HOST=localhost

# JWT Configuration (CHANGE IN PRODUCTION!)
JWT_SECRET_KEY=notify-service-development-key-change-in-production-32-chars-minimum

# Email Configuration
MAIL_FROM=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
MAIL_DISPLAY_NAME=Notify Service

# Docker Configuration
DOCKER_REGISTRY=notifyregistry.azurecr.io
IMAGE_NAME=notify-service-api

# Azure Configuration (for deployment)
AZURE_SUBSCRIPTION_ID=
AZURE_RESOURCE_GROUP=notify-service-rg
AZURE_WEBAPP_NAME=notify-service-api
AZURE_CONTAINER_REGISTRY=notifyregistry.azurecr.io

# Monitoring Configuration
SLACK_WEBHOOK_URL=
APPLICATION_INSIGHTS_KEY=

# Development Tools
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin123
EOF
}

create_default_secrets_file() {
    cat > "$SECRETS_FILE" << 'EOF'
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=NotifyDb;Username=********;Password=********",
    "IdentityConnection": "Host=localhost;Database=NotifyIdentityDb;Username=********;Password=********"
  },
  "JWTSettings": {
    "Key": "notify-service-development-key-change-in-production-32-chars-minimum",
    "Issuer": "NotifyServiceAPI",
    "Audience": "NotifyServiceUsers",
    "DurationInMinutes": 60
  },
  "MailSettings": {
    "EmailFrom": "<EMAIL>",
    "SmtpHost": "smtp.gmail.com",
    "SmtpPort": 587,
    "SmtpUser": "",
    "SmtpPass": "",
    "DisplayName": "Notify Service"
  },
  "RedisSettings": {
    "RedisConnectionString": "localhost:6379",
    "CacheTime": 30,
    "RedisDatabaseId": 0
  }
}
EOF
}

show_configuration_menu() {
    log_section "🎛️ Configuration Options"
    echo ""
    echo -e "${GREEN}    [1] 🚀 Quick Setup (Recommended)${NC}"
    echo "        └─ Interactive setup for development environment"
    echo ""
    echo -e "${CYAN}    [2] 🗄️  Database Configuration${NC}"
    echo "        └─ Configure PostgreSQL connection settings"
    echo ""
    echo -e "${RED}    [3] 🔴 Redis Configuration${NC}"
    echo "        └─ Configure Redis cache settings"
    echo ""
    echo -e "${BLUE}    [4] 📧 Email Configuration${NC}"
    echo "        └─ Configure SMTP settings for notifications"
    echo ""
    echo -e "${MAGENTA}    [5] 🔐 JWT & Security${NC}"
    echo "        └─ Configure authentication and security settings"
    echo ""
    echo -e "${WHITE}    [6] 📄 View Current Configuration${NC}"
    echo "        └─ Display current environment variables"
    echo ""
    echo -e "${GRAY}    [0] 🚪 Exit${NC}"
    echo ""
}

configure_quick_setup() {
    log_section "🚀 Quick Development Setup"
    
    echo -e "${GRAY}    This will configure basic settings for local development.${NC}"
    echo ""
    
    # Database settings
    echo -e "${CYAN}    🗄️ Database Configuration:${NC}"
    db_password=$(get_input "PostgreSQL Password" "********" true)
    db_host=$(get_input "PostgreSQL Host" "localhost")
    db_port=$(get_input "PostgreSQL Port" "5432")
    
    # Redis settings
    echo ""
    echo -e "${RED}    🔴 Redis Configuration:${NC}"
    redis_host=$(get_input "Redis Host" "localhost")
    redis_port=$(get_input "Redis Port" "6379")
    redis_password=$(get_input "Redis Password (optional)" "" true)
    
    # JWT settings
    echo ""
    echo -e "${MAGENTA}    🔐 Security Configuration:${NC}"
    jwt_key=$(get_input "JWT Secret Key (min 32 chars)" "$(generate_secure_key)" true)
    
    # Email settings
    echo ""
    echo -e "${BLUE}    📧 Email Configuration:${NC}"
    email_from=$(get_input "From Email Address" "<EMAIL>")
    smtp_host=$(get_input "SMTP Host" "smtp.gmail.com")
    smtp_port=$(get_input "SMTP Port" "587")
    smtp_user=$(get_input "SMTP Username (optional)" "")
    smtp_password=$(get_input "SMTP Password (optional)" "" true)
    
    # Update configuration files
    update_env_variable "POSTGRES_PASSWORD" "$db_password"
    update_env_variable "POSTGRES_HOST" "$db_host"
    update_env_variable "POSTGRES_PORT" "$db_port"
    update_env_variable "REDIS_HOST" "$redis_host"
    update_env_variable "REDIS_PORT" "$redis_port"
    update_env_variable "REDIS_PASSWORD" "$redis_password"
    update_env_variable "JWT_SECRET_KEY" "$jwt_key"
    update_env_variable "MAIL_FROM" "$email_from"
    update_env_variable "SMTP_HOST" "$smtp_host"
    update_env_variable "SMTP_PORT" "$smtp_port"
    update_env_variable "SMTP_USER" "$smtp_user"
    update_env_variable "SMTP_PASSWORD" "$smtp_password"
    
    log_step "Quick setup completed successfully!" "SUCCESS"
    echo ""
    echo -e "${WHITE}    📝 Next steps:${NC}"
    echo -e "${GRAY}    1. Review the generated .env file${NC}"
    echo -e "${GRAY}    2. Run: ./scripts/unix/start-dev-services.sh${NC}"
    echo ""
}

update_env_variable() {
    local key=$1
    local value=$2
    
    if [[ -f "$ENV_FILE" ]]; then
        if grep -q "^$key=" "$ENV_FILE"; then
            # Update existing variable
            if [[ "$OSTYPE" == "darwin"* ]]; then
                sed -i '' "s/^$key=.*/$key=$value/" "$ENV_FILE"
            else
                sed -i "s/^$key=.*/$key=$value/" "$ENV_FILE"
            fi
        else
            # Add new variable
            echo "$key=$value" >> "$ENV_FILE"
        fi
    fi
}

show_current_configuration() {
    log_section "📄 Current Configuration"
    
    if [[ -f "$ENV_FILE" ]]; then
        echo -e "${CYAN}    📁 Environment Variables (.env):${NC}"
        while IFS= read -r line; do
            if [[ ! "$line" =~ ^# ]] && [[ -n "$line" ]]; then
                if [[ "$line" =~ PASSWORD|SECRET|KEY ]]; then
                    key=$(echo "$line" | cut -d'=' -f1)
                    echo -e "${GRAY}    $key=${YELLOW}***${NC}"
                else
                    echo -e "${GRAY}    $line${NC}"
                fi
            fi
        done < "$ENV_FILE"
    else
        echo -e "${RED}    ❌ .env file not found${NC}"
    fi
    
    echo ""
    if [[ -f "$SECRETS_FILE" ]]; then
        echo -e "${MAGENTA}    🔐 Secrets Configuration:${NC}"
        echo -e "${GREEN}    ✅ secrets.json exists (contains sensitive data)${NC}"
    else
        echo -e "${RED}    ❌ secrets.json file not found${NC}"
    fi
}

# Main execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    initialize_config_files
    
    while true; do
        show_header
        show_configuration_menu
        
        echo -ne "${WHITE}    👉 Enter your choice (0-6): ${NC}"
        read choice
        
        case $choice in
            "1")
                configure_quick_setup
                read -p "Press Enter to continue..."
                ;;
            "2")
                echo -e "${CYAN}Database configuration not yet implemented in bash version${NC}"
                echo -e "${GRAY}Please use the PowerShell version: scripts/config-manager.ps1${NC}"
                read -p "Press Enter to continue..."
                ;;
            "3")
                echo -e "${RED}Redis configuration not yet implemented in bash version${NC}"
                echo -e "${GRAY}Please use the PowerShell version: scripts/config-manager.ps1${NC}"
                read -p "Press Enter to continue..."
                ;;
            "4")
                echo -e "${BLUE}Email configuration not yet implemented in bash version${NC}"
                echo -e "${GRAY}Please use the PowerShell version: scripts/config-manager.ps1${NC}"
                read -p "Press Enter to continue..."
                ;;
            "5")
                echo -e "${MAGENTA}Security configuration not yet implemented in bash version${NC}"
                echo -e "${GRAY}Please use the PowerShell version: scripts/config-manager.ps1${NC}"
                read -p "Press Enter to continue..."
                ;;
            "6")
                show_current_configuration
                read -p "Press Enter to continue..."
                ;;
            "0")
                echo -e "${GRAY}👋 Configuration saved!${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ Invalid choice${NC}"
                sleep 2
                ;;
        esac
    done
fi
