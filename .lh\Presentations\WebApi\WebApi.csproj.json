{"sourceFile": "Presentations/WebApi/WebApi.csproj", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751371782388, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751371782388, "name": "Commit-0", "content": "<Project Sdk=\"Microsoft.NET.Sdk.Web\">\n  <PropertyGroup>\n    <TargetFramework>net9.0</TargetFramework>\n    <UserSecretsId>603ea6aa-0a0a-4254-a847-85a3b447d0fb</UserSecretsId>\n    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>\n    <DockerfileContext>..\\..</DockerfileContext>\n  </PropertyGroup>\n  <ItemGroup>\n    <PackageReference Include=\"AspNetCore.HealthChecks.Redis\" Version=\"9.0.0\" />\n    <PackageReference Include=\"AspNetCore.HealthChecks.SqlServer\" Version=\"9.0.0\" />\n    <PackageReference Include=\"AspNetCore.HealthChecks.UI.Client\" Version=\"9.0.0\" />\n    <PackageReference Include=\"AutoMapper.Extensions.Microsoft.DependencyInjection\" Version=\"12.0.1\" />\n    <PackageReference Include=\"graphiql\" Version=\"2.0.0\" />\n    <PackageReference Include=\"GraphQL\" Version=\"8.5.0\" />\n    <PackageReference Include=\"Microsoft.VisualStudio.Azure.Containers.Tools.Targets\" Version=\"1.21.2\" />\n    <PackageReference Include=\"Serilog.AspNetCore\" Version=\"9.0.0\" />\n    <PackageReference Include=\"Serilog.Sinks.File\" Version=\"7.0.0\" />\n    <PackageReference Include=\"Serilog.Sinks.Seq\" Version=\"9.0.0\" />\n    <PackageReference Include=\"Microsoft.AspNetCore.Mvc.NewtonsoftJson\" Version=\"8.0.0\" />\n    <PackageReference Include=\"Microsoft.EntityFrameworkCore.Design\" Version=\"9.0.6\">\n      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>\n      <PrivateAssets>all</PrivateAssets>\n    </PackageReference>\n    <PackageReference Include=\"Microsoft.Extensions.Logging\" Version=\"9.0.6\" />\n    <PackageReference Include=\"Newtonsoft.Json\" Version=\"13.0.3\" />\n  </ItemGroup>\n  <ItemGroup>\n    <ProjectReference Include=\"..\\..\\Libraries\\Caching\\Caching.csproj\" />\n    <ProjectReference Include=\"..\\..\\Libraries\\Core\\Core.csproj\" />\n    <ProjectReference Include=\"..\\..\\Libraries\\Identity\\Identity.csproj\" />\n    <ProjectReference Include=\"..\\..\\Libraries\\Models\\Models.csproj\" />\n  </ItemGroup>\n  <ItemGroup>\n    <Compile Update=\"Controllers\\GraphQLController.cs\">\n      <DependentUpon>LogController.cs</DependentUpon>\n    </Compile>\n  </ItemGroup>\n</Project>\n"}]}