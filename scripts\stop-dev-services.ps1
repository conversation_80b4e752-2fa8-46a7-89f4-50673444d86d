# ═══════════════════════════════════════════════════════════════════════════════
# 🛑 Notify Service API - Development Environment Cleanup
# ═══════════════════════════════════════════════════════════════════════════════
# This script stops and optionally removes development containers

param(
    [switch]$Remove,
    [switch]$RemoveVolumes,
    [switch]$Force
)

# ASCII Art Header
Write-Host ""
Write-Host "    ███╗   ██╗ ██████╗ ████████╗██╗███████╗██╗   ██╗" -ForegroundColor Red
Write-Host "    ████╗  ██║██╔═══██╗╚══██╔══╝██║██╔════╝╚██╗ ██╔╝" -ForegroundColor Red
Write-Host "    ██╔██╗ ██║██║   ██║   ██║   ██║█████╗   ╚████╔╝ " -ForegroundColor Red
Write-Host "    ██║╚██╗██║██║   ██║   ██║   ██║██╔══╝    ╚██╔╝  " -ForegroundColor Red
Write-Host "    ██║ ╚████║╚██████╔╝   ██║   ██║██║        ██║   " -ForegroundColor Red
Write-Host "    ╚═╝  ╚═══╝ ╚═════╝    ╚═╝   ╚═╝╚═╝        ╚═╝   " -ForegroundColor Red
Write-Host ""
Write-Host "    🛑 Development Environment Cleanup" -ForegroundColor Yellow
Write-Host "    🐳 Stopping Docker Containers" -ForegroundColor Gray
Write-Host ""
Write-Host "═══════════════════════════════════════════════════════════════════════════════" -ForegroundColor DarkGray

# Configuration
$PostgresContainer = "notify-postgres"
$RedisContainer = "notify-redis"

function Write-Step {
    param([string]$Message, [string]$Status = "INFO")
    $timestamp = Get-Date -Format "HH:mm:ss"
    switch ($Status) {
        "SUCCESS" { Write-Host "[$timestamp] ✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "[$timestamp] ❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "[$timestamp] ⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "[$timestamp] 🔄 $Message" -ForegroundColor Cyan }
    }
}

function Write-Section {
    param([string]$Title)
    Write-Host ""
    Write-Host "┌─────────────────────────────────────────────────────────────────────────────┐" -ForegroundColor DarkGray
    Write-Host "│ $($Title.PadRight(75)) │" -ForegroundColor White
    Write-Host "└─────────────────────────────────────────────────────────────────────────────┘" -ForegroundColor DarkGray
}

function Stop-Container {
    param([string]$ContainerName, [string]$DisplayName)
    
    # Check if container exists
    $containerExists = docker ps -a --filter "name=$ContainerName" --format "{{.Names}}" | Select-String -Pattern "^$ContainerName$"
    
    if ($containerExists) {
        # Check if container is running
        $containerRunning = docker ps --filter "name=$ContainerName" --format "{{.Names}}" | Select-String -Pattern "^$ContainerName$"
        
        if ($containerRunning) {
            Write-Step "Stopping $DisplayName container..." "INFO"
            docker stop $ContainerName | Out-Null
            if ($LASTEXITCODE -eq 0) {
                Write-Step "$DisplayName container stopped successfully" "SUCCESS"
            } else {
                Write-Step "Failed to stop $DisplayName container" "ERROR"
            }
        } else {
            Write-Step "$DisplayName container is already stopped" "SUCCESS"
        }
        
        if ($Remove) {
            Write-Step "Removing $DisplayName container..." "INFO"
            docker rm $ContainerName | Out-Null
            if ($LASTEXITCODE -eq 0) {
                Write-Step "$DisplayName container removed successfully" "SUCCESS"
            } else {
                Write-Step "Failed to remove $DisplayName container" "ERROR"
            }
        }
    } else {
        Write-Step "$DisplayName container does not exist" "WARNING"
    }
}

# Show options
Write-Section "🔧 Cleanup Options"
if ($Remove) {
    Write-Step "Mode: Stop and REMOVE containers" "WARNING"
} else {
    Write-Step "Mode: Stop containers only (containers will be preserved)" "INFO"
}

if ($RemoveVolumes) {
    Write-Step "Volumes: Will be REMOVED (data will be lost!)" "WARNING"
} else {
    Write-Step "Volumes: Will be preserved" "INFO"
}

# Confirmation for destructive operations
if ($Remove -or $RemoveVolumes) {
    if (-not $Force) {
        Write-Host ""
        Write-Host "⚠️  WARNING: This operation will permanently delete data!" -ForegroundColor Red
        Write-Host "Are you sure you want to continue? (y/N): " -NoNewline -ForegroundColor Yellow
        $confirmation = Read-Host
        if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
            Write-Step "Operation cancelled by user" "INFO"
            exit 0
        }
    }
}

Write-Section "🛑 Stopping Services"

# Stop containers
Stop-Container -ContainerName $PostgresContainer -DisplayName "PostgreSQL"
Stop-Container -ContainerName $RedisContainer -DisplayName "Redis"

# Remove volumes if requested
if ($RemoveVolumes) {
    Write-Section "🗑️ Removing Volumes"
    
    Write-Step "Removing PostgreSQL volumes..." "INFO"
    $pgVolumes = docker volume ls --filter "name=postgres" --format "{{.Name}}"
    if ($pgVolumes) {
        docker volume rm $pgVolumes 2>$null | Out-Null
        Write-Step "PostgreSQL volumes removed" "SUCCESS"
    } else {
        Write-Step "No PostgreSQL volumes found" "INFO"
    }
    
    Write-Step "Removing Redis volumes..." "INFO"
    $redisVolumes = docker volume ls --filter "name=redis" --format "{{.Name}}"
    if ($redisVolumes) {
        docker volume rm $redisVolumes 2>$null | Out-Null
        Write-Step "Redis volumes removed" "SUCCESS"
    } else {
        Write-Step "No Redis volumes found" "INFO"
    }
}

Write-Section "🧹 Cleanup Summary"

# Show final status
$postgresStatus = docker ps -a --filter "name=$PostgresContainer" --format "{{.Status}}"
$redisStatus = docker ps -a --filter "name=$RedisContainer" --format "{{.Status}}"

if ($Remove) {
    if (-not $postgresStatus) {
        Write-Step "PostgreSQL container: Removed" "SUCCESS"
    } else {
        Write-Step "PostgreSQL container: Still exists" "WARNING"
    }
    
    if (-not $redisStatus) {
        Write-Step "Redis container: Removed" "SUCCESS"
    } else {
        Write-Step "Redis container: Still exists" "WARNING"
    }
} else {
    Write-Step "PostgreSQL container: $($postgresStatus -replace 'Exited.*', 'Stopped')" "SUCCESS"
    Write-Step "Redis container: $($redisStatus -replace 'Exited.*', 'Stopped')" "SUCCESS"
}

Write-Host ""
Write-Host "    🎉 Cleanup completed!" -ForegroundColor Green
Write-Host ""
Write-Host "    📝 Usage Examples:" -ForegroundColor White
Write-Host "    ├─ Stop only: " -NoNewline -ForegroundColor Gray
Write-Host ".\scripts\stop-dev-services.ps1" -ForegroundColor Cyan
Write-Host "    ├─ Stop & remove: " -NoNewline -ForegroundColor Gray
Write-Host ".\scripts\stop-dev-services.ps1 -Remove" -ForegroundColor Yellow
Write-Host "    └─ Full cleanup: " -NoNewline -ForegroundColor Gray
Write-Host ".\scripts\stop-dev-services.ps1 -Remove -RemoveVolumes -Force" -ForegroundColor Red
Write-Host ""
Write-Host "    🚀 To restart services:" -ForegroundColor White
Write-Host "    └─ " -NoNewline -ForegroundColor Gray
Write-Host ".\scripts\start-dev-services.ps1" -ForegroundColor Green
Write-Host ""
Write-Host "═══════════════════════════════════════════════════════════════════════════════" -ForegroundColor DarkGray
