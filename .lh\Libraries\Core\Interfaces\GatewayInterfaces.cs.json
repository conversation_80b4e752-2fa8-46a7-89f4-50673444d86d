{"sourceFile": "Libraries/Core/Interfaces/GatewayInterfaces.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751297124137, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751297124137, "name": "Commit-0", "content": "using System;\r\nusing System.Collections.Generic;\r\nusing Core.Interfaces;\r\nusing System.Threading;\r\nusing System.Threading.Tasks;\r\n\r\npublic interface IMessageService<\r\n    TPayload = object,\r\n    TResult = object,\r\n    TScheduleResult = object\r\n> : IMessageSender<TPayload, TResult>, IMessageScheduler<TPayload, TScheduleResult>\r\n{\r\n    Task<TResult> ResendAsync(string originalMessageId, CancellationToken cancellationToken = default);\r\n    Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);\r\n}\r\n\r\npublic interface IAdminMetricsService<\r\n    TConfiguration = object,\r\n    TStatusReport = object,\r\n    TUsageMetrics = object,\r\n    TErrorLog = object,\r\n    TPerformance = object,\r\n    TSla = object,\r\n    TLatency = object,\r\n    TTraffic = object,\r\n    TAnomaly = object,\r\n    TReport = object,\r\n    TRetry = object,\r\n    TImpact = object,\r\n    TOptions = object,\r\n    TGranularity = string\r\n>\r\n{\r\n    Task<IReadOnlyList<TConfiguration>> GetConfigurationsAsync(CancellationToken cancellationToken = default);\r\n    Task UpdateConfigurationAsync(TConfiguration setting, CancellationToken cancellationToken = default);\r\n    Task<TStatusReport> GetStatusReportAsync(CancellationToken cancellationToken = default);\r\n    Task<TUsageMetrics> GetUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);\r\n    // Task<IReadOnlyList<TErrorLog>> GetErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);\r\n    Task<TPerformance> GetPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken = default);\r\n    Task<TSla> GetSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);\r\n    Task<TLatency> GetLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);\r\n    Task<IReadOnlyList<TTraffic>> GetTrafficTrendsAsync(TGranularity granularity, CancellationToken cancellationToken = default);\r\n    Task<IReadOnlyList<TAnomaly>> GetAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);\r\n    Task<TReport> GenerateMetricsReportAsync(TOptions options, CancellationToken cancellationToken = default);\r\n    Task<IReadOnlyList<TRetry>> GetRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);\r\n    Task<IReadOnlyList<TImpact>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default);\r\n}\r\n"}]}