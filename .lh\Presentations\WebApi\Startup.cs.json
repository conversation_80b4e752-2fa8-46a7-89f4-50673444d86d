{"sourceFile": "Presentations/WebApi/Startup.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751296538904, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751296538904, "name": "Commit-0", "content": "using Caching;\nusing Core;\nusing Data.Mongo;\nusing GraphiQl;\nusing HealthChecks.UI.Client;\nusing Identity;\nusing Microsoft.AspNetCore.Builder;\nusing Microsoft.AspNetCore.Diagnostics.HealthChecks;\nusing Microsoft.AspNetCore.Hosting;\nusing Microsoft.Extensions.Configuration;\nusing Microsoft.Extensions.DependencyInjection;\nusing Microsoft.Extensions.Diagnostics.HealthChecks;\nusing Microsoft.Extensions.Hosting;\nusing Microsoft.Extensions.Logging;\nusing Serilog;\nusing Services.Interfaces;\nusing WebApi.Extensions;\nusing WebApi.GraphQL;\nusing WebApi.Helpers;\nusing WebApi.Services;\n\nnamespace WebApi\n{\n    public class Startup\n    {\n        public Startup(IConfiguration configuration)\n        {\n            Configuration = configuration;\n        }\n\n        public IConfiguration Configuration { get; }\n\n        // This method gets called by the runtime. Use this method to add services to the container.\n        public void ConfigureServices(IServiceCollection services)\n        {\n            services.AddMongo(Configuration);\n            services.AddLogging(o => o.AddSerilog());\n            services.AddRepoServices(Configuration); // Add repository services\n            services.AddIdentity(Configuration);\n            services.AddSharedServices(Configuration);\n            services.AddApplicationSqlServer(Configuration); // Ensure SQL Server is initialized\n            services.AddRepoServices(Configuration);\n            services.AddRedis(Configuration);\n            services.AddScoped<IAuthenticatedUserService, AuthenticatedUserService>();\n            services.AddAppServices(Configuration); // Add application services\n            services.AddAutoMapper(typeof(MappingProfiles));\n            services.AddCustomSwagger(Configuration);\n\n            services.AddControllers();\n            //.AddNewtonsoftJson(options =>\n            //options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore\n            //);\n\n            services.AddHealthChecks()\n\n                .AddRedis(Configuration.GetSection(\"RedisSettings:RedisConnectionString\").Value,\n                name: \"RedisHealt-check\",\n                failureStatus: HealthStatus.Unhealthy,\n                tags: new string[] { \"api\", \"Redis\" })\n\n                .AddSqlServer(Configuration.GetConnectionString(\"IdentityConnection\"),\n                name: \"identityDb-check\",\n                failureStatus: HealthStatus.Unhealthy,\n                tags: new string[] { \"api\", \"SqlDb\" })\n\n                .AddSqlServer(Configuration.GetConnectionString(\"DefaultConnection\"),\n                name: \"applicationDb-check\",\n                failureStatus: HealthStatus.Unhealthy,\n                tags: new string[] { \"api\", \"SqlDb\" });\n\n\n            services.AddAuthorization(options =>\n            {\n                options.AddPolicy(\"OnlyAdmins\", policy => policy.RequireRole(\"SuperAdmin\", \"Admin\"));\n            });\n\n        }\n\n        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.\n        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILoggerFactory loggerFactory)\n        {\n            if (env.IsDevelopment())\n            {\n                app.UseDeveloperExceptionPage();\n            }\n\n            //app.UseSerilogRequestLogging();\n            loggerFactory.AddSerilog();\n\n            app.UseHttpsRedirection();\n\n            app.UseRouting();\n            app.UseGraphiQl();\n            app.UseAuthentication();\n            app.UseAuthorization();\n\n            //error middleware\n            app.UseErrorHandlingMiddleware();\n\n            app.UseSwagger();\n            app.UseSwaggerUI(c => { c.SwaggerEndpoint(\"/swagger/v1/swagger.json\", \"Service Api V1\"); });\n\n            app.UseEndpoints(endpoints =>\n            {\n                endpoints.MapControllers();\n                endpoints.MapHealthChecks(\"/health\", new HealthCheckOptions()\n                {\n                    Predicate = _ => true,\n                    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse\n                });\n            });\n        }\n    }\n}\n"}]}