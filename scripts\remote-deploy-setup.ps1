# ═══════════════════════════════════════════════════════════════════════════════
# 🌐 Notify Service API - Remote Deployment Server Setup
# ═══════════════════════════════════════════════════════════════════════════════
# Configure and manage remote deployment servers with security and monitoring

param(
    [string]$Action = "menu",           # menu, setup, deploy, monitor, configure, ssh
    [string]$Environment = "",          # dev, staging, prod
    [string]$ServerName = "",           # Server identifier
    [string]$HostAddress = "",          # Server IP/hostname
    [string]$Username = "",             # SSH username
    [string]$KeyPath = "",              # SSH private key path
    [switch]$Force,                     # Force operations
    [switch]$Verbose,                   # Verbose output
    [switch]$DryRun,                    # Dry run mode
    [switch]$Json                       # JSON output
)

# Configuration
$ProjectRoot = Split-Path -Parent (Split-Path -Parent $MyInvocation.MyCommand.Path)
$ServersConfigFile = Join-Path $ProjectRoot "remote-servers.json"
$DeploymentConfigFile = Join-Path $ProjectRoot "deployment-config.json"
$LogsDir = Join-Path $ProjectRoot "logs"
$KeysDir = Join-Path $ProjectRoot ".ssh"

# Ensure directories exist
@($LogsDir, $KeysDir) | ForEach-Object {
    if (-not (Test-Path $_)) {
        New-Item -ItemType Directory -Path $_ | Out-Null
    }
}

# Colors
$Colors = @{
    Red = "Red"; Green = "Green"; Yellow = "Yellow"; Blue = "Blue"
    Cyan = "Cyan"; Magenta = "Magenta"; Gray = "Gray"; White = "White"
}

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    if (-not $Json) { Write-Host $Text -ForegroundColor $Colors[$Color] }
}

function Write-DeployLog {
    param([string]$Message, [string]$Level = "INFO", [string]$Server = "")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level]"
    
    if ($Server) { $logEntry += " [$Server]" }
    $logEntry += " $Message"
    
    # Write to console
    if (-not $Json) {
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            "INFO" { "Cyan" }
            default { "White" }
        }
        Write-ColorText $logEntry $color
    }
    
    # Write to log file
    $logFile = Join-Path $LogsDir "remote-deploy-$(Get-Date -Format 'yyyy-MM-dd').log"
    Add-Content -Path $logFile -Value $logEntry
}

function Show-Header {
    if ($Json) { return }
    
    Clear-Host
    Write-Host ""
    Write-ColorText "    🌐🌐🌐 REMOTE DEPLOYMENT SERVER SETUP 🌐🌐🌐" "Cyan"
    Write-Host ""
    Write-ColorText "    🚀 Configure • Deploy • Monitor • Secure" "Yellow"
    Write-ColorText "    🔒 SSH • Docker • Nginx • SSL • Monitoring" "Gray"
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "Gray"
}

function Initialize-ServerConfiguration {
    Write-DeployLog "Initializing server configuration..." "INFO"
    
    if (-not (Test-Path $ServersConfigFile)) {
        $defaultServers = @{
            "servers" = @{
                "dev" = @{
                    "name" = "Development Server"
                    "host" = "dev.notify-api.com"
                    "port" = 22
                    "username" = "deploy"
                    "keyPath" = ".ssh/dev-server-key"
                    "dockerRegistry" = "localhost:5000"
                    "environment" = "development"
                    "services" = @{
                        "nginx" = @{ "enabled" = $true; "port" = 80; "sslPort" = 443 }
                        "docker" = @{ "enabled" = $true; "composeFile" = "docker-compose.dev.yml" }
                        "monitoring" = @{ "enabled" = $true; "port" = 9090 }
                    }
                }
                "staging" = @{
                    "name" = "Staging Server"
                    "host" = "staging.notify-api.com"
                    "port" = 22
                    "username" = "deploy"
                    "keyPath" = ".ssh/staging-server-key"
                    "dockerRegistry" = "registry.notify-api.com"
                    "environment" = "staging"
                    "services" = @{
                        "nginx" = @{ "enabled" = $true; "port" = 80; "sslPort" = 443 }
                        "docker" = @{ "enabled" = $true; "composeFile" = "docker-compose.staging.yml" }
                        "monitoring" = @{ "enabled" = $true; "port" = 9090 }
                    }
                }
                "prod" = @{
                    "name" = "Production Server"
                    "host" = "prod.notify-api.com"
                    "port" = 22
                    "username" = "deploy"
                    "keyPath" = ".ssh/prod-server-key"
                    "dockerRegistry" = "registry.notify-api.com"
                    "environment" = "production"
                    "services" = @{
                        "nginx" = @{ "enabled" = $true; "port" = 80; "sslPort" = 443 }
                        "docker" = @{ "enabled" = $true; "composeFile" = "docker-compose.prod.yml" }
                        "monitoring" = @{ "enabled" = $true; "port" = 9090 }
                        "backup" = @{ "enabled" = $true; "schedule" = "0 2 * * *" }
                    }
                }
            }
            "security" = @{
                "sshKeyType" = "ed25519"
                "sshKeySize" = 4096
                "firewallEnabled" = $true
                "allowedPorts" = @(22, 80, 443, 9090)
                "failBanEnabled" = $true
                "automaticUpdates" = $true
            }
            "deployment" = @{
                "strategy" = "blue-green"  # blue-green, rolling, recreate
                "healthCheckUrl" = "/health"
                "healthCheckTimeout" = 30
                "rollbackOnFailure" = $true
                "backupBeforeDeploy" = $true
            }
        }
        
        $defaultServers | ConvertTo-Json -Depth 10 | Set-Content -Path $ServersConfigFile -Encoding UTF8
        Write-DeployLog "Default server configuration created" "SUCCESS"
    }
    
    return Get-Content $ServersConfigFile | ConvertFrom-Json
}

function Test-SSHConnection {
    param(
        [object]$ServerConfig,
        [string]$ServerName
    )
    
    Write-DeployLog "Testing SSH connection to $($ServerConfig.host)..." "INFO" $ServerName
    
    $keyPath = Join-Path $ProjectRoot $ServerConfig.keyPath
    
    if (-not (Test-Path $keyPath)) {
        Write-DeployLog "SSH key not found: $keyPath" "ERROR" $ServerName
        return $false
    }
    
    try {
        # Test SSH connection
        $sshCommand = "ssh -i `"$keyPath`" -o ConnectTimeout=10 -o StrictHostKeyChecking=no $($ServerConfig.username)@$($ServerConfig.host) 'echo SSH_CONNECTION_SUCCESS'"
        
        if ($DryRun) {
            Write-DeployLog "DRY RUN: Would test SSH with: $sshCommand" "INFO" $ServerName
            return $true
        }
        
        $result = Invoke-Expression $sshCommand 2>&1
        
        if ($result -like "*SSH_CONNECTION_SUCCESS*") {
            Write-DeployLog "SSH connection successful" "SUCCESS" $ServerName
            return $true
        } else {
            Write-DeployLog "SSH connection failed: $result" "ERROR" $ServerName
            return $false
        }
    } catch {
        Write-DeployLog "SSH connection error: $($_.Exception.Message)" "ERROR" $ServerName
        return $false
    }
}

function Invoke-RemoteCommand {
    param(
        [object]$ServerConfig,
        [string]$Command,
        [string]$ServerName,
        [switch]$Sudo
    )
    
    $keyPath = Join-Path $ProjectRoot $ServerConfig.keyPath
    $fullCommand = if ($Sudo) { "sudo $Command" } else { $Command }
    
    $sshCommand = "ssh -i `"$keyPath`" -o StrictHostKeyChecking=no $($ServerConfig.username)@$($ServerConfig.host) '$fullCommand'"
    
    if ($DryRun) {
        Write-DeployLog "DRY RUN: Would execute: $fullCommand" "INFO" $ServerName
        return @{ "success" = $true; "output" = "Dry run - not executed" }
    }
    
    if ($Verbose) {
        Write-DeployLog "Executing: $fullCommand" "INFO" $ServerName
    }
    
    try {
        $output = Invoke-Expression $sshCommand 2>&1
        $exitCode = $LASTEXITCODE
        
        if ($exitCode -eq 0) {
            Write-DeployLog "Command executed successfully" "SUCCESS" $ServerName
            return @{ "success" = $true; "output" = $output; "exitCode" = $exitCode }
        } else {
            Write-DeployLog "Command failed with exit code: $exitCode" "ERROR" $ServerName
            return @{ "success" = $false; "output" = $output; "exitCode" = $exitCode }
        }
    } catch {
        Write-DeployLog "Command execution error: $($_.Exception.Message)" "ERROR" $ServerName
        return @{ "success" = $false; "output" = $_.Exception.Message; "exception" = $_ }
    }
}

function Install-ServerDependencies {
    param(
        [object]$ServerConfig,
        [string]$ServerName
    )
    
    Write-DeployLog "Installing server dependencies..." "INFO" $ServerName
    
    $commands = @(
        "apt-get update",
        "apt-get install -y curl wget git unzip",
        "apt-get install -y docker.io docker-compose",
        "systemctl enable docker",
        "systemctl start docker",
        "usermod -aG docker $($ServerConfig.username)",
        "apt-get install -y nginx",
        "systemctl enable nginx",
        "apt-get install -y ufw fail2ban",
        "ufw --force enable"
    )
    
    foreach ($command in $commands) {
        $result = Invoke-RemoteCommand -ServerConfig $ServerConfig -Command $command -ServerName $ServerName -Sudo
        if (-not $result.success) {
            Write-DeployLog "Failed to execute: $command" "ERROR" $ServerName
            return $false
        }
    }
    
    Write-DeployLog "Server dependencies installed successfully" "SUCCESS" $ServerName
    return $true
}

function Configure-ServerSecurity {
    param(
        [object]$ServerConfig,
        [string]$ServerName,
        [object]$SecurityConfig
    )
    
    Write-DeployLog "Configuring server security..." "INFO" $ServerName
    
    # Configure UFW firewall
    $firewallCommands = @(
        "ufw --force reset",
        "ufw default deny incoming",
        "ufw default allow outgoing"
    )
    
    foreach ($port in $SecurityConfig.allowedPorts) {
        $firewallCommands += "ufw allow $port"
    }
    
    $firewallCommands += "ufw --force enable"
    
    foreach ($command in $firewallCommands) {
        $result = Invoke-RemoteCommand -ServerConfig $ServerConfig -Command $command -ServerName $ServerName -Sudo
        if (-not $result.success) {
            Write-DeployLog "Failed to configure firewall: $command" "ERROR" $ServerName
            return $false
        }
    }
    
    # Configure Fail2Ban
    $fail2banConfig = @"
[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600
"@
    
    $configCommand = "echo '$fail2banConfig' > /etc/fail2ban/jail.local && systemctl restart fail2ban"
    $result = Invoke-RemoteCommand -ServerConfig $ServerConfig -Command $configCommand -ServerName $ServerName -Sudo
    
    if ($result.success) {
        Write-DeployLog "Server security configured successfully" "SUCCESS" $ServerName
        return $true
    } else {
        Write-DeployLog "Failed to configure security" "ERROR" $ServerName
        return $false
    }
}

function Deploy-Application {
    param(
        [object]$ServerConfig,
        [string]$ServerName,
        [string]$ImageTag = "latest"
    )
    
    Write-DeployLog "Deploying application to $ServerName..." "INFO" $ServerName
    
    # Create deployment directory
    $deployDir = "/opt/notify-service"
    $result = Invoke-RemoteCommand -ServerConfig $ServerConfig -Command "mkdir -p $deployDir" -ServerName $ServerName -Sudo
    
    if (-not $result.success) {
        Write-DeployLog "Failed to create deployment directory" "ERROR" $ServerName
        return $false
    }
    
    # Copy docker-compose file
    $composeFile = $ServerConfig.services.docker.composeFile
    $localComposePath = Join-Path $ProjectRoot $composeFile
    
    if (Test-Path $localComposePath) {
        $scpCommand = "scp -i `"$(Join-Path $ProjectRoot $ServerConfig.keyPath)`" `"$localComposePath`" $($ServerConfig.username)@$($ServerConfig.host):$deployDir/docker-compose.yml"
        
        if ($DryRun) {
            Write-DeployLog "DRY RUN: Would copy compose file" "INFO" $ServerName
        } else {
            $result = Invoke-Expression $scpCommand 2>&1
            if ($LASTEXITCODE -ne 0) {
                Write-DeployLog "Failed to copy docker-compose file" "ERROR" $ServerName
                return $false
            }
        }
    }
    
    # Pull and deploy
    $deployCommands = @(
        "cd $deployDir",
        "docker-compose pull",
        "docker-compose down",
        "docker-compose up -d"
    )
    
    foreach ($command in $deployCommands) {
        $result = Invoke-RemoteCommand -ServerConfig $ServerConfig -Command $command -ServerName $ServerName
        if (-not $result.success) {
            Write-DeployLog "Deployment failed at: $command" "ERROR" $ServerName
            return $false
        }
    }
    
    # Health check
    Start-Sleep -Seconds 10
    $healthResult = Invoke-RemoteCommand -ServerConfig $ServerConfig -Command "curl -f http://localhost/health" -ServerName $ServerName
    
    if ($healthResult.success) {
        Write-DeployLog "Application deployed and health check passed" "SUCCESS" $ServerName
        return $true
    } else {
        Write-DeployLog "Deployment completed but health check failed" "WARN" $ServerName
        return $false
    }
}

function Show-RemoteMenu {
    Write-Host ""
    Write-ColorText "🌐 Remote Deployment Management Menu" "Cyan"
    Write-Host ""
    Write-ColorText "    [1] 🔧 Setup New Server" "Green"
    Write-ColorText "        └─ Configure a new deployment server"
    Write-Host ""
    Write-ColorText "    [2] 🚀 Deploy Application" "Blue"
    Write-ColorText "        └─ Deploy to existing server"
    Write-Host ""
    Write-ColorText "    [3] 📊 Monitor Servers" "Yellow"
    Write-ColorText "        └─ Check server status and health"
    Write-Host ""
    Write-ColorText "    [4] 🔒 Configure Security" "Magenta"
    Write-ColorText "        └─ Update security settings"
    Write-Host ""
    Write-ColorText "    [5] 🔑 Manage SSH Keys" "Cyan"
    Write-ColorText "        └─ Generate and manage SSH keys"
    Write-Host ""
    Write-ColorText "    [6] 📋 List Servers" "Gray"
    Write-ColorText "        └─ Show configured servers"
    Write-Host ""
    Write-ColorText "    [0] 🚪 Exit" "Gray"
    Write-Host ""
}

# Main execution
Show-Header

$config = Initialize-ServerConfiguration

if ($Action -eq "menu") {
    do {
        Show-RemoteMenu
        $choice = Read-Host "Enter your choice (0-6)"
        
        switch ($choice) {
            "1" {
                Write-ColorText "Available environments: dev, staging, prod" "INFO"
                $env = Read-Host "Enter environment"
                
                if ($env -and $config.servers.$env) {
                    $serverConfig = $config.servers.$env
                    
                    Write-DeployLog "Setting up server: $($serverConfig.name)" "INFO"
                    
                    # Test connection
                    if (Test-SSHConnection -ServerConfig $serverConfig -ServerName $env) {
                        # Install dependencies
                        Install-ServerDependencies -ServerConfig $serverConfig -ServerName $env
                        
                        # Configure security
                        Configure-ServerSecurity -ServerConfig $serverConfig -ServerName $env -SecurityConfig $config.security
                    }
                } else {
                    Write-DeployLog "Invalid environment: $env" "ERROR"
                }
                
                Read-Host "Press Enter to continue"
            }
            "2" {
                Write-ColorText "Available environments: dev, staging, prod" "INFO"
                $env = Read-Host "Enter environment"
                
                if ($env -and $config.servers.$env) {
                    $serverConfig = $config.servers.$env
                    Deploy-Application -ServerConfig $serverConfig -ServerName $env
                } else {
                    Write-DeployLog "Invalid environment: $env" "ERROR"
                }
                
                Read-Host "Press Enter to continue"
            }
            "3" {
                Write-DeployLog "Server monitoring functionality coming soon..." "INFO"
                Read-Host "Press Enter to continue"
            }
            "0" {
                Write-DeployLog "Goodbye!" "INFO"
                break
            }
            default {
                Write-DeployLog "Invalid choice. Please try again." "WARN"
                Read-Host "Press Enter to continue"
            }
        }
    } while ($choice -ne "0")
}
