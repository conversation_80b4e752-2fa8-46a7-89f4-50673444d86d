-- =========================
-- Database Initialization Script
-- Creates additional databases and configurations
-- =========================

-- Create Identity database
CREATE DATABASE "NotifyIdentityDb";

-- Create application user (optional)
-- CREATE USER notify_user WITH PASSWORD 'secure_password';
-- GRANT ALL PRIVILEGES ON DATABASE "NotifyDb" TO notify_user;
-- GRANT ALL PRIVILEGES ON DATABASE "NotifyIdentityDb" TO notify_user;

-- Connect to NotifyDb to create extensions
\c "NotifyDb";

-- Create useful extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Connect to NotifyIdentityDb to create extensions
\c "NotifyIdentityDb";

-- Create useful extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Back to default database
\c postgres;

-- Create indexes for better performance (will be created by EF migrations)
-- These are examples of what might be useful

-- Performance monitoring function
CREATE OR REPLACE FUNCTION log_slow_queries()
RETURNS event_trigger AS $$
BEGIN
    -- Log slow queries (this is just an example)
    RAISE NOTICE 'Database operation completed';
END;
$$ LANGUAGE plpgsql;

-- Set some performance parameters
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_min_duration_statement = 1000;

-- Reload configuration
SELECT pg_reload_conf();
