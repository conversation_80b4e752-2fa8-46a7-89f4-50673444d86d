# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 Notify Service API - Enhanced Local Development Setup
# ═══════════════════════════════════════════════════════════════════════════════
# Comprehensive setup script with auto-installation, environment management, and Docker support

param(
    [switch]$AutoInstall,      # Automatically install missing dependencies
    [switch]$SkipDocker,       # Skip Docker-based setup
    [switch]$UseLocal,         # Use local PostgreSQL/Redis instead of Docker
    [switch]$Force,            # Force reinstallation of existing components
    [switch]$Quiet,            # Minimal output
    [switch]$Help              # Show help
)

# Configuration
$ProjectRoot = Split-Path -Parent (Split-Path -Parent $MyInvocation.MyCommand.Path)
$EnvFile = Join-Path $ProjectRoot ".env"
$EnvTemplate = Join-Path $ProjectRoot ".env.template"

# Colors for better output
$Colors = @{
    Red = "Red"; Green = "Green"; Yellow = "Yellow"; Blue = "Blue"
    Cyan = "Cyan"; Magenta = "Magenta"; Gray = "Gray"; White = "White"
    DarkGray = "DarkGray"
}

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    if (-not $Quiet) { Write-Host $Text -ForegroundColor $Colors[$Color] }
}

function Write-Step {
    param([string]$Message, [string]$Status = "INFO")
    if ($Quiet -and $Status -ne "ERROR") { return }

    $timestamp = Get-Date -Format "HH:mm:ss"
    switch ($Status) {
        "SUCCESS" { Write-ColorText "[$timestamp] ✅ $Message" "Green" }
        "ERROR"   { Write-ColorText "[$timestamp] ❌ $Message" "Red" }
        "WARNING" { Write-ColorText "[$timestamp] ⚠️  $Message" "Yellow" }
        "INFO"    { Write-ColorText "[$timestamp] 🔄 $Message" "Cyan" }
        "INPUT"   { Write-ColorText "[$timestamp] 📝 $Message" "Magenta" }
    }
}

function Write-Section {
    param([string]$Title)
    if ($Quiet) { return }
    Write-Host ""
    Write-ColorText "┌─────────────────────────────────────────────────────────────────────────────┐" "DarkGray"
    Write-ColorText "│ $($Title.PadRight(75)) │" "White"
    Write-ColorText "└─────────────────────────────────────────────────────────────────────────────┘" "DarkGray"
}

function Show-Header {
    if ($Quiet) { return }
    Clear-Host
    Write-Host ""
    Write-ColorText "    ███╗   ██╗ ██████╗ ████████╗██╗███████╗██╗   ██╗" "Cyan"
    Write-ColorText "    ████╗  ██║██╔═══██╗╚══██╔══╝██║██╔════╝╚██╗ ██╔╝" "Cyan"
    Write-ColorText "    ██╔██╗ ██║██║   ██║   ██║   ██║█████╗   ╚████╔╝ " "Cyan"
    Write-ColorText "    ██║╚██╗██║██║   ██║   ██║   ██║██╔══╝    ╚██╔╝  " "Cyan"
    Write-ColorText "    ██║ ╚████║╚██████╔╝   ██║   ██║██║        ██║   " "Cyan"
    Write-ColorText "    ╚═╝  ╚═══╝ ╚═════╝    ╚═╝   ╚═╝╚═╝        ╚═╝   " "Cyan"
    Write-Host ""
    Write-ColorText "    🚀 Enhanced Local Development Setup" "Yellow"
    Write-ColorText "    🔧 Auto-Install • Environment • Docker • Validation" "Gray"
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "DarkGray"
}

function Show-Help {
    Write-ColorText "🎯 Enhanced Local Development Setup" "Yellow"
    Write-Host ""
    Write-ColorText "USAGE:" "White"
    Write-ColorText "  .\scripts\setup-local-dev.ps1 [OPTIONS]" "Gray"
    Write-Host ""
    Write-ColorText "OPTIONS:" "White"
    Write-ColorText "  -AutoInstall   Automatically install missing dependencies" "Gray"
    Write-ColorText "  -SkipDocker    Skip Docker-based setup (use local services)" "Gray"
    Write-ColorText "  -UseLocal      Use local PostgreSQL/Redis instead of Docker" "Gray"
    Write-ColorText "  -Force         Force reinstallation of existing components" "Gray"
    Write-ColorText "  -Quiet         Minimal output (errors only)" "Gray"
    Write-ColorText "  -Help          Show this help message" "Gray"
    Write-Host ""
    Write-ColorText "EXAMPLES:" "White"
    Write-ColorText "  .\scripts\setup-local-dev.ps1                    # Interactive setup" "Gray"
    Write-ColorText "  .\scripts\setup-local-dev.ps1 -AutoInstall       # Auto-install dependencies" "Gray"
    Write-ColorText "  .\scripts\setup-local-dev.ps1 -UseLocal          # Use local services" "Gray"
    Write-ColorText "  .\scripts\setup-local-dev.ps1 -Quiet             # Silent setup" "Gray"
    Write-Host ""
}

if ($Help) {
    Show-Header
    Show-Help
    exit 0
}

Show-Header
Write-Step "Initializing enhanced local development setup..." "INFO"

# Environment and dependency management functions
function Test-Dependency {
    param(
        [string]$Name,
        [string]$Command,
        [string]$InstallCommand = "",
        [string]$DownloadUrl = "",
        [bool]$Required = $true
    )

    Write-Step "Checking $Name..." "INFO"

    try {
        $result = Invoke-Expression "$Command 2>`$null"
        if ($LASTEXITCODE -eq 0) {
            Write-Step "$Name is installed and accessible" "SUCCESS"
            return $true
        }
    } catch {
        # Command failed
    }

    if ($Required) {
        Write-Step "$Name is not installed or not accessible" "ERROR"

        if ($AutoInstall -and $InstallCommand) {
            Write-Step "Attempting to install $Name automatically..." "INFO"
            try {
                Invoke-Expression $InstallCommand
                if ($LASTEXITCODE -eq 0) {
                    Write-Step "$Name installed successfully" "SUCCESS"
                    return $true
                } else {
                    Write-Step "Failed to install $Name automatically" "ERROR"
                }
            } catch {
                Write-Step "Failed to install $Name`: $($_.Exception.Message)" "ERROR"
            }
        }

        if ($DownloadUrl) {
            Write-ColorText "    📥 Manual installation required" "Yellow"
            Write-ColorText "    🔗 Download from: $DownloadUrl" "Cyan"
        }

        if (-not $AutoInstall -and $InstallCommand) {
            Write-ColorText "    💡 Auto-install option available" "Cyan"
            Write-ColorText "    🔄 Run with -AutoInstall to install automatically" "Gray"
        }

        return $false
    } else {
        Write-Step "$Name is not installed (optional)" "WARNING"
        return $true
    }
}

function Initialize-Environment {
    Write-Section "🔧 Environment Configuration"

    # Check if .env file exists
    if (-not (Test-Path $EnvFile)) {
        Write-Step "No .env file found, creating from template..." "INFO"

        if (Test-Path $EnvTemplate) {
            Copy-Item $EnvTemplate $EnvFile
            Write-Step ".env file created from template" "SUCCESS"
        } else {
            # Create basic .env file
            $defaultEnv = @"
# Notify Service API - Environment Configuration
# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=NotifyDb

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Application Configuration
ASPNETCORE_ENVIRONMENT=Development
ASPNETCORE_URLS=https://localhost:5001;http://localhost:5000

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ISSUER=NotifyServiceAPI
JWT_AUDIENCE=NotifyServiceAPI

# Logging
LOG_LEVEL=Information
"@
            Set-Content -Path $EnvFile -Value $defaultEnv -Encoding UTF8
            Write-Step "Default .env file created" "SUCCESS"
        }
    } else {
        Write-Step "Environment file found" "SUCCESS"
    }

    # Load environment variables
    if (Test-Path $EnvFile) {
        Write-Step "Loading environment variables..." "INFO"
        Get-Content $EnvFile | ForEach-Object {
            if ($_ -match '^([^#][^=]+)=(.*)$') {
                [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
            }
        }
        Write-Step "Environment variables loaded" "SUCCESS"
    }
}

function Test-DockerSetup {
    Write-Section "🐳 Docker Environment Check"

    if ($SkipDocker) {
        Write-Step "Skipping Docker setup (--SkipDocker specified)" "WARNING"
        return $false
    }

    # Check if Docker is installed and running
    if (-not (Test-Dependency "Docker" "docker --version" "winget install Docker.DockerDesktop" "https://www.docker.com/products/docker-desktop")) {
        return $false
    }

    # Check if Docker daemon is running
    Write-Step "Checking Docker daemon..." "INFO"
    try {
        docker info | Out-Null 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Step "Docker daemon is running" "SUCCESS"
            return $true
        } else {
            Write-Step "Docker daemon is not running" "ERROR"
            Write-ColorText "    🔄 Please start Docker Desktop and try again" "Yellow"
            return $false
        }
    } catch {
        Write-Step "Failed to connect to Docker daemon" "ERROR"
        return $false
    }
}

Write-Section "🔍 Dependency Check & Installation"

# Check dependencies
$dependenciesOk = $true

# Check .NET SDK
if (-not (Test-Dependency ".NET 8 SDK" "dotnet --version" "winget install Microsoft.DotNet.SDK.8" "https://dotnet.microsoft.com/download/dotnet/8.0")) {
    $dependenciesOk = $false
}

# Check Git (optional)
Test-Dependency "Git" "git --version" "winget install Git.Git" "https://git-scm.com/download/win" $false

if (-not $dependenciesOk) {
    Write-Step "Required dependencies are missing. Please install them and run this script again." "ERROR"
    exit 1
}

# Initialize environment
Initialize-Environment

# Setup Docker or local services
$useDocker = Test-DockerSetup

if ($UseLocal -or -not $useDocker) {
    Write-Section "🔧 Local Services Setup"

    # Check PostgreSQL
    if (Test-Dependency "PostgreSQL" "psql -U postgres -c `"SELECT version();`"" "choco install postgresql" "https://www.postgresql.org/download/") {
        Write-Step "Creating databases..." "INFO"
        $createDbScript = Join-Path (Split-Path -Parent $MyInvocation.MyCommand.Path) "create-databases.sql"
        if (Test-Path $createDbScript) {
            try {
                psql -U postgres -f $createDbScript 2>$null
                if ($LASTEXITCODE -eq 0) {
                    Write-Step "Databases created successfully" "SUCCESS"
                } else {
                    Write-Step "Database creation failed or databases already exist" "WARNING"
                }
            } catch {
                Write-Step "Failed to create databases: $($_.Exception.Message)" "ERROR"
            }
        } else {
            Write-Step "Database creation script not found" "WARNING"
        }
    }

    # Check Redis
    Test-Dependency "Redis" "redis-cli ping" "choco install redis-64" "https://redis.io/download"

} else {
    Write-Section "🐳 Docker Services Setup"

    Write-Step "Starting PostgreSQL container..." "INFO"
    try {
        $postgresPassword = [Environment]::GetEnvironmentVariable("POSTGRES_PASSWORD") ?? "postgres"
        docker run -d --name notify-postgres -e POSTGRES_PASSWORD=$postgresPassword -e POSTGRES_DB=postgres -p 5432:5432 postgres:15-alpine 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Step "PostgreSQL container started successfully" "SUCCESS"
        } else {
            Write-Step "PostgreSQL container already exists, attempting to start..." "WARNING"
            docker start notify-postgres 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Step "Existing PostgreSQL container started" "SUCCESS"
            } else {
                Write-Step "Failed to start PostgreSQL container" "ERROR"
            }
        }
    } catch {
        Write-Step "Failed to setup PostgreSQL container: $($_.Exception.Message)" "ERROR"
    }

    Write-Step "Starting Redis container..." "INFO"
    try {
        $redisPassword = [Environment]::GetEnvironmentVariable("REDIS_PASSWORD")
        $redisCmd = if ($redisPassword) { "redis-server --requirepass $redisPassword" } else { "" }

        docker run -d --name notify-redis -p 6379:6379 redis:7-alpine $redisCmd 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Step "Redis container started successfully" "SUCCESS"
        } else {
            Write-Step "Redis container already exists, attempting to start..." "WARNING"
            docker start notify-redis 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Step "Existing Redis container started" "SUCCESS"
            } else {
                Write-Step "Failed to start Redis container" "ERROR"
            }
        }
    } catch {
        Write-Step "Failed to setup Redis container: $($_.Exception.Message)" "ERROR"
    }

    # Wait for services to be ready
    Write-Step "Waiting for services to initialize..." "INFO"
    Start-Sleep -Seconds 5

    # Create databases in Docker PostgreSQL
    Write-Step "Creating databases in PostgreSQL container..." "INFO"
    $databases = @("NotifyDb", "NotifyIdentityDb")
    foreach ($db in $databases) {
        try {
            docker exec notify-postgres psql -U postgres -c "CREATE DATABASE `"$db`";" 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Step "Database '$db' created successfully" "SUCCESS"
            } else {
                Write-Step "Database '$db' already exists or creation failed" "WARNING"
            }
        } catch {
            Write-Step "Failed to create database '$db': $($_.Exception.Message)" "ERROR"
        }
    }
}

Write-Section "🧪 Service Validation"

# Test connections
if ($useDocker -and -not $UseLocal) {
    # Test Docker services
    Write-Step "Testing PostgreSQL connection..." "INFO"
    try {
        docker exec notify-postgres psql -U postgres -c "SELECT 1;" 2>$null | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Step "PostgreSQL connection successful" "SUCCESS"
        } else {
            Write-Step "PostgreSQL connection failed" "ERROR"
        }
    } catch {
        Write-Step "PostgreSQL connection test failed" "ERROR"
    }

    Write-Step "Testing Redis connection..." "INFO"
    try {
        $redisPing = docker exec notify-redis redis-cli ping 2>$null
        if ($redisPing -eq "PONG") {
            Write-Step "Redis connection successful" "SUCCESS"
        } else {
            Write-Step "Redis connection failed" "ERROR"
        }
    } catch {
        Write-Step "Redis connection test failed" "ERROR"
    }
} else {
    # Test local services
    Write-Step "Testing local PostgreSQL connection..." "INFO"
    try {
        psql -U postgres -c "SELECT 1;" 2>$null | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Step "Local PostgreSQL connection successful" "SUCCESS"
        } else {
            Write-Step "Local PostgreSQL connection failed" "ERROR"
        }
    } catch {
        Write-Step "Local PostgreSQL connection test failed" "ERROR"
    }

    Write-Step "Testing local Redis connection..." "INFO"
    try {
        $redisPing = redis-cli ping 2>$null
        if ($redisPing -eq "PONG") {
            Write-Step "Local Redis connection successful" "SUCCESS"
        } else {
            Write-Step "Local Redis connection failed" "ERROR"
        }
    } catch {
        Write-Step "Local Redis connection test failed" "ERROR"
    }
}

Write-Section "🎉 Setup Complete!"

Write-Host ""
Write-ColorText "    🚀 Your development environment is ready!" "Green"
Write-Host ""
Write-ColorText "    📊 Services Status:" "White"
if ($useDocker -and -not $UseLocal) {
    Write-ColorText "    ├─ 🗄️  PostgreSQL: Docker container (localhost:5432)" "Gray"
    Write-ColorText "    └─ 🔴 Redis: Docker container (localhost:6379)" "Gray"
} else {
    Write-ColorText "    ├─ 🗄️  PostgreSQL: Local service (localhost:5432)" "Gray"
    Write-ColorText "    └─ 🔴 Redis: Local service (localhost:6379)" "Gray"
}
Write-Host ""
Write-ColorText "    🏃‍♂️ Next Steps:" "White"
Write-ColorText "    ├─ Run the API: dotnet run --project Presentations\WebApi" "Cyan"
Write-ColorText "    ├─ API Docs: https://localhost:5001/scalar" "Cyan"
Write-ColorText "    └─ Health Check: https://localhost:5001/health" "Cyan"
Write-Host ""
if ($useDocker -and -not $UseLocal) {
    Write-ColorText "    🛑 To stop Docker services later:" "White"
    Write-ColorText "    └─ docker stop notify-postgres notify-redis" "Red"
}
Write-Host ""
Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "DarkGray"
