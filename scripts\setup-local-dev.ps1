# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 Notify Service API - Enhanced Local Development Setup
# ═══════════════════════════════════════════════════════════════════════════════
# Comprehensive setup script with auto-installation, environment management, and Docker support

param(
    [switch]$AutoInstall,      # Automatically install missing dependencies
    [switch]$SkipDocker,       # Skip Docker-based setup
    [switch]$UseLocal,         # Use local PostgreSQL/Redis instead of Docker
    [switch]$Force,            # Force reinstallation of existing components
    [switch]$Quiet,            # Minimal output
    [switch]$Help              # Show help
)

# Configuration
$ProjectRoot = Split-Path -Parent (Split-Path -Parent $MyInvocation.MyCommand.Path)
$EnvFile = Join-Path $ProjectRoot ".env"
$EnvTemplate = Join-Path $ProjectRoot ".env.template"

# Colors for better output
$Colors = @{
    Red = "Red"; Green = "Green"; Yellow = "Yellow"; Blue = "Blue"
    Cyan = "Cyan"; Magenta = "Magenta"; Gray = "Gray"; White = "White"
    DarkGray = "DarkGray"
}

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    if (-not $Quiet) { Write-Host $Text -ForegroundColor $Colors[$Color] }
}

function Write-Step {
    param([string]$Message, [string]$Status = "INFO")
    if ($Quiet -and $Status -ne "ERROR") { return }

    $timestamp = Get-Date -Format "HH:mm:ss"
    switch ($Status) {
        "SUCCESS" { Write-ColorText "[$timestamp] ✅ $Message" "Green" }
        "ERROR"   { Write-ColorText "[$timestamp] ❌ $Message" "Red" }
        "WARNING" { Write-ColorText "[$timestamp] ⚠️  $Message" "Yellow" }
        "INFO"    { Write-ColorText "[$timestamp] 🔄 $Message" "Cyan" }
        "INPUT"   { Write-ColorText "[$timestamp] 📝 $Message" "Magenta" }
    }
}

function Write-Section {
    param([string]$Title)
    if ($Quiet) { return }
    Write-Host ""
    Write-ColorText "┌─────────────────────────────────────────────────────────────────────────────┐" "DarkGray"
    Write-ColorText "│ $($Title.PadRight(75)) │" "White"
    Write-ColorText "└─────────────────────────────────────────────────────────────────────────────┘" "DarkGray"
}

function Show-Header {
    if ($Quiet) { return }
    Clear-Host
    Write-Host ""
    Write-ColorText "    ███╗   ██╗ ██████╗ ████████╗██╗███████╗██╗   ██╗" "Cyan"
    Write-ColorText "    ████╗  ██║██╔═══██╗╚══██╔══╝██║██╔════╝╚██╗ ██╔╝" "Cyan"
    Write-ColorText "    ██╔██╗ ██║██║   ██║   ██║   ██║█████╗   ╚████╔╝ " "Cyan"
    Write-ColorText "    ██║╚██╗██║██║   ██║   ██║   ██║██╔══╝    ╚██╔╝  " "Cyan"
    Write-ColorText "    ██║ ╚████║╚██████╔╝   ██║   ██║██║        ██║   " "Cyan"
    Write-ColorText "    ╚═╝  ╚═══╝ ╚═════╝    ╚═╝   ╚═╝╚═╝        ╚═╝   " "Cyan"
    Write-Host ""
    Write-ColorText "    🚀 Enhanced Local Development Setup" "Yellow"
    Write-ColorText "    🔧 Auto-Install • Environment • Docker • Validation" "Gray"
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "DarkGray"
}

function Show-Help {
    Write-ColorText "🎯 Enhanced Local Development Setup" "Yellow"
    Write-Host ""
    Write-ColorText "USAGE:" "White"
    Write-ColorText "  .\scripts\setup-local-dev.ps1 [OPTIONS]" "Gray"
    Write-Host ""
    Write-ColorText "OPTIONS:" "White"
    Write-ColorText "  -AutoInstall   Automatically install missing dependencies" "Gray"
    Write-ColorText "  -SkipDocker    Skip Docker-based setup (use local services)" "Gray"
    Write-ColorText "  -UseLocal      Use local PostgreSQL/Redis instead of Docker" "Gray"
    Write-ColorText "  -Force         Force reinstallation of existing components" "Gray"
    Write-ColorText "  -Quiet         Minimal output (errors only)" "Gray"
    Write-ColorText "  -Help          Show this help message" "Gray"
    Write-Host ""
    Write-ColorText "EXAMPLES:" "White"
    Write-ColorText "  .\scripts\setup-local-dev.ps1                    # Interactive setup" "Gray"
    Write-ColorText "  .\scripts\setup-local-dev.ps1 -AutoInstall       # Auto-install dependencies" "Gray"
    Write-ColorText "  .\scripts\setup-local-dev.ps1 -UseLocal          # Use local services" "Gray"
    Write-ColorText "  .\scripts\setup-local-dev.ps1 -Quiet             # Silent setup" "Gray"
    Write-Host ""
}

if ($Help) {
    Show-Header
    Show-Help
    exit 0
}

Show-Header
Write-Step "Initializing enhanced local development setup..." "INFO"

# Enhanced error tracking and reporting
$Global:ErrorLog = @()
$Global:InstallationResults = @{}

function Add-ErrorToLog {
    param(
        [string]$Component,
        [string]$ErrorMessage,
        [string]$ErrorCode = "UNKNOWN",
        [string]$Suggestion = ""
    )

    $errorEntry = @{
        Timestamp = Get-Date
        Component = $Component
        ErrorMessage = $ErrorMessage
        ErrorCode = $ErrorCode
        Suggestion = $Suggestion
        ExitCode = $LASTEXITCODE
    }

    $Global:ErrorLog += $errorEntry
    Write-Step "ERROR [$ErrorCode]: $Component - $ErrorMessage" "ERROR"

    if ($Suggestion) {
        Write-ColorText "    💡 Suggestion: $Suggestion" "Yellow"
    }
}

function Test-CommandAvailability {
    param(
        [string]$Command,
        [string]$ComponentName
    )

    try {
        $null = Get-Command $Command.Split(' ')[0] -ErrorAction Stop
        return $true
    } catch {
        Add-ErrorToLog -Component $ComponentName -ErrorMessage "Command '$Command' not found in PATH" -ErrorCode "CMD_NOT_FOUND" -Suggestion "Ensure $ComponentName is installed and added to PATH"
        return $false
    }
}

function Invoke-CommandWithErrorHandling {
    param(
        [string]$Command,
        [string]$ComponentName,
        [string]$SuccessMessage = "",
        [string]$ErrorMessage = "",
        [bool]$SuppressOutput = $true
    )

    try {
        Write-Step "Executing: $Command" "INFO"

        if ($SuppressOutput) {
            $result = Invoke-Expression "$Command 2>&1"
        } else {
            $result = Invoke-Expression $Command
        }

        if ($LASTEXITCODE -eq 0) {
            if ($SuccessMessage) {
                Write-Step $SuccessMessage "SUCCESS"
            }
            return @{ Success = $true; Output = $result; ExitCode = $LASTEXITCODE }
        } else {
            $errorMsg = if ($ErrorMessage) { $ErrorMessage } else { "Command failed with exit code $LASTEXITCODE" }
            Add-ErrorToLog -Component $ComponentName -ErrorMessage $errorMsg -ErrorCode "CMD_FAILED" -Suggestion "Check the command syntax and dependencies"
            return @{ Success = $false; Output = $result; ExitCode = $LASTEXITCODE }
        }
    } catch {
        $errorMsg = if ($ErrorMessage) { $ErrorMessage } else { $_.Exception.Message }
        Add-ErrorToLog -Component $ComponentName -ErrorMessage $errorMsg -ErrorCode "CMD_EXCEPTION" -Suggestion "Check if the command exists and has proper permissions"
        return @{ Success = $false; Output = $_.Exception.Message; ExitCode = -1 }
    }
}

# Environment and dependency management functions
function Test-Dependency {
    param(
        [string]$Name,
        [string]$Command,
        [string]$InstallCommand = "",
        [string]$DownloadUrl = "",
        [bool]$Required = $true
    )

    Write-Step "Checking $Name..." "INFO"
    $Global:InstallationResults[$Name] = @{ Status = "CHECKING"; Required = $Required }

    # First check if the command is available
    if (-not (Test-CommandAvailability -Command $Command -ComponentName $Name)) {
        $Global:InstallationResults[$Name].Status = "NOT_FOUND"

        if (-not $Required) {
            Write-Step "$Name is not installed (optional)" "WARNING"
            $Global:InstallationResults[$Name].Status = "OPTIONAL_MISSING"
            return $true
        }

        # Try to install if auto-install is enabled
        if ($AutoInstall -and $InstallCommand) {
            return Install-DependencyWithRetry -Name $Name -InstallCommand $InstallCommand -TestCommand $Command -DownloadUrl $DownloadUrl
        } else {
            Show-ManualInstallInstructions -Name $Name -InstallCommand $InstallCommand -DownloadUrl $DownloadUrl
            $Global:InstallationResults[$Name].Status = "FAILED"
            return $false
        }
    }

    # Test the actual command
    $testResult = Invoke-CommandWithErrorHandling -Command $Command -ComponentName $Name -SuccessMessage "$Name is installed and accessible"

    if ($testResult.Success) {
        $Global:InstallationResults[$Name].Status = "SUCCESS"
        $Global:InstallationResults[$Name].Version = $testResult.Output
        return $true
    } else {
        $Global:InstallationResults[$Name].Status = "FAILED"
        $Global:InstallationResults[$Name].Error = $testResult.Output

        if ($Required) {
            Add-ErrorToLog -Component $Name -ErrorMessage "Dependency test failed" -ErrorCode "DEP_TEST_FAILED" -Suggestion "Verify $Name installation and configuration"
            return $false
        } else {
            Write-Step "$Name test failed (optional)" "WARNING"
            return $true
        }
    }
}

function Install-DependencyWithRetry {
    param(
        [string]$Name,
        [string]$InstallCommand,
        [string]$TestCommand,
        [string]$DownloadUrl,
        [int]$MaxRetries = 2
    )

    for ($attempt = 1; $attempt -le $MaxRetries; $attempt++) {
        Write-Step "Attempting to install $Name (attempt $attempt/$MaxRetries)..." "INFO"

        $installResult = Invoke-CommandWithErrorHandling -Command $InstallCommand -ComponentName $Name -ErrorMessage "Installation failed"

        if ($installResult.Success) {
            # Wait a moment for installation to complete
            Start-Sleep -Seconds 3

            # Test if installation was successful
            $testResult = Invoke-CommandWithErrorHandling -Command $TestCommand -ComponentName $Name -SuppressOutput $true

            if ($testResult.Success) {
                Write-Step "$Name installed successfully" "SUCCESS"
                $Global:InstallationResults[$Name].Status = "INSTALLED"
                return $true
            } else {
                Add-ErrorToLog -Component $Name -ErrorMessage "Installation completed but verification failed" -ErrorCode "INSTALL_VERIFY_FAILED" -Suggestion "Try manual installation or check system requirements"
            }
        } else {
            Add-ErrorToLog -Component $Name -ErrorMessage "Installation command failed on attempt $attempt" -ErrorCode "INSTALL_CMD_FAILED"
        }

        if ($attempt -lt $MaxRetries) {
            Write-Step "Retrying installation in 5 seconds..." "WARNING"
            Start-Sleep -Seconds 5
        }
    }

    # All attempts failed
    Show-ManualInstallInstructions -Name $Name -InstallCommand $InstallCommand -DownloadUrl $DownloadUrl
    $Global:InstallationResults[$Name].Status = "INSTALL_FAILED"
    return $false
}

function Show-ManualInstallInstructions {
    param(
        [string]$Name,
        [string]$InstallCommand,
        [string]$DownloadUrl
    )

    Write-Step "$Name installation required" "ERROR"

    if ($DownloadUrl) {
        Write-ColorText "    📥 Manual installation required" "Yellow"
        Write-ColorText "    🔗 Download from: $DownloadUrl" "Cyan"
    }

    if (-not $AutoInstall -and $InstallCommand) {
        Write-ColorText "    💡 Auto-install option available" "Cyan"
        Write-ColorText "    🔄 Run with -AutoInstall to install automatically" "Gray"
        Write-ColorText "    📋 Manual command: $InstallCommand" "Gray"
    }
}

function Start-DockerContainer {
    param(
        [string]$Name,
        [string]$Image,
        [string]$Port,
        [hashtable]$EnvVars = @{},
        [string]$HealthCheck = "",
        [int]$HealthCheckRetries = 3,
        [int]$HealthCheckDelay = 5
    )

    Write-Step "Setting up Docker container: $Name" "INFO"

    # Build environment variables string
    $envString = ""
    foreach ($key in $EnvVars.Keys) {
        $envString += " -e `"$key=$($EnvVars[$key])`""
    }

    # Try to run the container
    $runCommand = "docker run -d --name $Name -p $Port$envString $Image"
    $runResult = Invoke-CommandWithErrorHandling -Command $runCommand -ComponentName $Name -SuppressOutput $true

    if (-not $runResult.Success) {
        # Container might already exist, try to start it
        Write-Step "Container $Name might already exist, attempting to start..." "WARNING"
        $startResult = Invoke-CommandWithErrorHandling -Command "docker start $Name" -ComponentName $Name -SuppressOutput $true

        if (-not $startResult.Success) {
            # Try to remove and recreate
            Write-Step "Attempting to remove and recreate container $Name..." "WARNING"
            Invoke-CommandWithErrorHandling -Command "docker rm -f $Name" -ComponentName $Name -SuppressOutput $true

            $recreateResult = Invoke-CommandWithErrorHandling -Command $runCommand -ComponentName $Name -SuppressOutput $true
            if (-not $recreateResult.Success) {
                Add-ErrorToLog -Component $Name -ErrorMessage "Failed to create Docker container after cleanup" -ErrorCode "DOCKER_CREATE_FAILED" -Suggestion "Check Docker daemon status and image availability"
                return @{ Success = $false; Error = "Container creation failed" }
            }
        }
    }

    # Wait for container to be ready
    Write-Step "Waiting for container $Name to be ready..." "INFO"
    Start-Sleep -Seconds 3

    # Perform health check if specified
    if ($HealthCheck) {
        for ($i = 1; $i -le $HealthCheckRetries; $i++) {
            Write-Step "Health check attempt $i/$HealthCheckRetries for $Name..." "INFO"

            $healthResult = Invoke-CommandWithErrorHandling -Command $HealthCheck -ComponentName $Name -SuppressOutput $true

            if ($healthResult.Success) {
                Write-Step "Container $Name is healthy and ready" "SUCCESS"
                return @{ Success = $true; ContainerName = $Name }
            }

            if ($i -lt $HealthCheckRetries) {
                Write-Step "Health check failed, retrying in $HealthCheckDelay seconds..." "WARNING"
                Start-Sleep -Seconds $HealthCheckDelay
            }
        }

        Add-ErrorToLog -Component $Name -ErrorMessage "Container health check failed after $HealthCheckRetries attempts" -ErrorCode "DOCKER_HEALTH_FAILED" -Suggestion "Check container logs with 'docker logs $Name'"
        return @{ Success = $false; Error = "Health check failed" }
    } else {
        # No health check, assume success if container is running
        $statusResult = Invoke-CommandWithErrorHandling -Command "docker ps --filter name=$Name --format '{{.Status}}'" -ComponentName $Name -SuppressOutput $true

        if ($statusResult.Success -and $statusResult.Output -like "*Up*") {
            Write-Step "Container $Name is running" "SUCCESS"
            return @{ Success = $true; ContainerName = $Name }
        } else {
            Add-ErrorToLog -Component $Name -ErrorMessage "Container is not running properly" -ErrorCode "DOCKER_NOT_RUNNING" -Suggestion "Check container logs with 'docker logs $Name'"
            return @{ Success = $false; Error = "Container not running" }
        }
    }
}

function Initialize-Environment {
    Write-Section "🔧 Environment Configuration"

    # Check if .env file exists
    if (-not (Test-Path $EnvFile)) {
        Write-Step "No .env file found, creating from template..." "INFO"

        if (Test-Path $EnvTemplate) {
            Copy-Item $EnvTemplate $EnvFile
            Write-Step ".env file created from template" "SUCCESS"
        } else {
            # Create basic .env file
            $defaultEnv = @"
# Notify Service API - Environment Configuration
# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=NotifyDb

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Application Configuration
ASPNETCORE_ENVIRONMENT=Development
ASPNETCORE_URLS=https://localhost:5001;http://localhost:5000

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ISSUER=NotifyServiceAPI
JWT_AUDIENCE=NotifyServiceAPI

# Logging
LOG_LEVEL=Information
"@
            Set-Content -Path $EnvFile -Value $defaultEnv -Encoding UTF8
            Write-Step "Default .env file created" "SUCCESS"
        }
    } else {
        Write-Step "Environment file found" "SUCCESS"
    }

    # Load environment variables
    if (Test-Path $EnvFile) {
        Write-Step "Loading environment variables..." "INFO"
        Get-Content $EnvFile | ForEach-Object {
            if ($_ -match '^([^#][^=]+)=(.*)$') {
                [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
            }
        }
        Write-Step "Environment variables loaded" "SUCCESS"
    }
}

function Test-DockerSetup {
    Write-Section "🐳 Docker Environment Check"

    if ($SkipDocker) {
        Write-Step "Skipping Docker setup (--SkipDocker specified)" "WARNING"
        return $false
    }

    # Check if Docker is installed and running
    if (-not (Test-Dependency "Docker" "docker --version" "winget install Docker.DockerDesktop" "https://www.docker.com/products/docker-desktop")) {
        return $false
    }

    # Check if Docker daemon is running
    Write-Step "Checking Docker daemon..." "INFO"
    try {
        docker info | Out-Null 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Step "Docker daemon is running" "SUCCESS"
            return $true
        } else {
            Write-Step "Docker daemon is not running" "ERROR"
            Write-ColorText "    🔄 Please start Docker Desktop and try again" "Yellow"
            return $false
        }
    } catch {
        Write-Step "Failed to connect to Docker daemon" "ERROR"
        return $false
    }
}

Write-Section "🔍 Dependency Check & Installation"

# Check dependencies with enhanced error tracking
Write-Step "Checking core dependencies..." "INFO"

# Check .NET SDK (required)
Test-Dependency ".NET 8 SDK" "dotnet --version" "winget install Microsoft.DotNet.SDK.8" "https://dotnet.microsoft.com/download/dotnet/8.0" $true

# Check Docker (required for container setup)
Test-Dependency "Docker" "docker --version" "winget install Docker.DockerDesktop" "https://www.docker.com/products/docker-desktop" $true

# Check Git (optional but recommended)
Test-Dependency "Git" "git --version" "winget install Git.Git" "https://git-scm.com/download/win" $false

# Check additional tools
Test-Dependency "PowerShell Core" "pwsh --version" "winget install Microsoft.PowerShell" "https://github.com/PowerShell/PowerShell/releases" $false

# Run database compatibility check
Write-Step "Running database compatibility check..." "INFO"
$compatibilityResult = & "$PSScriptRoot\database-compatibility-check.ps1" -Json | ConvertFrom-Json -ErrorAction SilentlyContinue

if ($compatibilityResult) {
    if ($compatibilityResult.Summary.ErrorCount -gt 0) {
        Write-Step "Database compatibility issues detected ($($compatibilityResult.Summary.ErrorCount) errors)" "WARNING"
        $Global:InstallationResults["Database Compatibility"] = @{ Status = "FAILED"; Required = $true; Error = "Compatibility issues found" }

        if ($AutoInstall) {
            Write-Step "Attempting to fix database compatibility issues..." "INFO"
            & "$PSScriptRoot\database-compatibility-check.ps1" -Fix | Out-Null
        }
    } else {
        Write-Step "Database compatibility check passed" "SUCCESS"
        $Global:InstallationResults["Database Compatibility"] = @{ Status = "SUCCESS"; Required = $true }
    }
} else {
    Write-Step "Database compatibility check completed" "SUCCESS"
    $Global:InstallationResults["Database Compatibility"] = @{ Status = "SUCCESS"; Required = $true }
}

# Initialize environment
Initialize-Environment

# Setup Docker or local services
$useDocker = Test-DockerSetup

if ($UseLocal -or -not $useDocker) {
    Write-Section "🔧 Local Services Setup"

    # Check PostgreSQL
    if (Test-Dependency "PostgreSQL" "psql -U postgres -c `"SELECT version();`"" "choco install postgresql" "https://www.postgresql.org/download/") {
        Write-Step "Creating databases..." "INFO"
        $createDbScript = Join-Path (Split-Path -Parent $MyInvocation.MyCommand.Path) "create-databases.sql"
        if (Test-Path $createDbScript) {
            try {
                psql -U postgres -f $createDbScript 2>$null
                if ($LASTEXITCODE -eq 0) {
                    Write-Step "Databases created successfully" "SUCCESS"
                } else {
                    Write-Step "Database creation failed or databases already exist" "WARNING"
                }
            } catch {
                Write-Step "Failed to create databases: $($_.Exception.Message)" "ERROR"
            }
        } else {
            Write-Step "Database creation script not found" "WARNING"
        }
    }

    # Check Redis
    Test-Dependency "Redis" "redis-cli ping" "choco install redis-64" "https://redis.io/download"

} else {
    Write-Section "🐳 Docker Services Setup"

    # Setup PostgreSQL container with enhanced error handling
    $postgresResult = Start-DockerContainer -Name "notify-postgres" -Image "postgres:15-alpine" -Port "5432:5432" -EnvVars @{
        "POSTGRES_PASSWORD" = ([Environment]::GetEnvironmentVariable("POSTGRES_PASSWORD") ?? "postgres")
        "POSTGRES_DB" = "NotifyDb"
        "POSTGRES_USER" = "postgres"
    } -HealthCheck "docker exec notify-postgres pg_isready -U postgres"

    if ($postgresResult.Success) {
        $Global:InstallationResults["PostgreSQL Container"] = @{ Status = "SUCCESS"; Required = $true }
    } else {
        $Global:InstallationResults["PostgreSQL Container"] = @{ Status = "FAILED"; Required = $true; Error = $postgresResult.Error }
    }

    # Setup Redis container with enhanced error handling
    $redisEnvVars = @{}
    $redisPassword = [Environment]::GetEnvironmentVariable("REDIS_PASSWORD")
    if ($redisPassword) {
        $redisEnvVars["REDIS_PASSWORD"] = $redisPassword
    }

    $redisResult = Start-DockerContainer -Name "notify-redis" -Image "redis:7-alpine" -Port "6379:6379" -EnvVars $redisEnvVars -HealthCheck "docker exec notify-redis redis-cli ping"

    if ($redisResult.Success) {
        $Global:InstallationResults["Redis Container"] = @{ Status = "SUCCESS"; Required = $true }
    } else {
        $Global:InstallationResults["Redis Container"] = @{ Status = "FAILED"; Required = $true; Error = $redisResult.Error }
    }

    # Wait for services to be ready
    Write-Step "Waiting for services to initialize..." "INFO"
    Start-Sleep -Seconds 5

    # Create databases in Docker PostgreSQL
    Write-Step "Creating databases in PostgreSQL container..." "INFO"
    $databases = @("NotifyDb", "NotifyIdentityDb")
    foreach ($db in $databases) {
        try {
            docker exec notify-postgres psql -U postgres -c "CREATE DATABASE `"$db`";" 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Step "Database '$db' created successfully" "SUCCESS"
            } else {
                Write-Step "Database '$db' already exists or creation failed" "WARNING"
            }
        } catch {
            Write-Step "Failed to create database '$db': $($_.Exception.Message)" "ERROR"
        }
    }
}

Write-Section "🧪 Service Validation"

# Test connections
if ($useDocker -and -not $UseLocal) {
    # Test Docker services
    Write-Step "Testing PostgreSQL connection..." "INFO"
    try {
        docker exec notify-postgres psql -U postgres -c "SELECT 1;" 2>$null | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Step "PostgreSQL connection successful" "SUCCESS"
        } else {
            Write-Step "PostgreSQL connection failed" "ERROR"
        }
    } catch {
        Write-Step "PostgreSQL connection test failed" "ERROR"
    }

    Write-Step "Testing Redis connection..." "INFO"
    try {
        $redisPing = docker exec notify-redis redis-cli ping 2>$null
        if ($redisPing -eq "PONG") {
            Write-Step "Redis connection successful" "SUCCESS"
        } else {
            Write-Step "Redis connection failed" "ERROR"
        }
    } catch {
        Write-Step "Redis connection test failed" "ERROR"
    }
} else {
    # Test local services
    Write-Step "Testing local PostgreSQL connection..." "INFO"
    try {
        psql -U postgres -c "SELECT 1;" 2>$null | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Step "Local PostgreSQL connection successful" "SUCCESS"
        } else {
            Write-Step "Local PostgreSQL connection failed" "ERROR"
        }
    } catch {
        Write-Step "Local PostgreSQL connection test failed" "ERROR"
    }

    Write-Step "Testing local Redis connection..." "INFO"
    try {
        $redisPing = redis-cli ping 2>$null
        if ($redisPing -eq "PONG") {
            Write-Step "Local Redis connection successful" "SUCCESS"
        } else {
            Write-Step "Local Redis connection failed" "ERROR"
        }
    } catch {
        Write-Step "Local Redis connection test failed" "ERROR"
    }
}

Write-Section "🎉 Setup Complete!"

Write-Host ""
Write-ColorText "    🚀 Your development environment is ready!" "Green"
Write-Host ""
Write-ColorText "    📊 Services Status:" "White"
if ($useDocker -and -not $UseLocal) {
    Write-ColorText "    ├─ 🗄️  PostgreSQL: Docker container (localhost:5432)" "Gray"
    Write-ColorText "    └─ 🔴 Redis: Docker container (localhost:6379)" "Gray"
} else {
    Write-ColorText "    ├─ 🗄️  PostgreSQL: Local service (localhost:5432)" "Gray"
    Write-ColorText "    └─ 🔴 Redis: Local service (localhost:6379)" "Gray"
}
# Generate comprehensive setup report
Show-SetupSummary

function Show-SetupSummary {
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "DarkGray"
    Write-ColorText "📊 SETUP SUMMARY REPORT" "Yellow"
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "DarkGray"

    # Installation Results Summary
    Write-Host ""
    Write-ColorText "🔧 Component Installation Results:" "White"

    $successCount = 0
    $failureCount = 0
    $optionalCount = 0

    foreach ($component in $Global:InstallationResults.Keys) {
        $result = $Global:InstallationResults[$component]
        $status = $result.Status

        switch ($status) {
            "SUCCESS" {
                Write-ColorText "    ✅ $component - Installed and verified" "Green"
                $successCount++
            }
            "INSTALLED" {
                Write-ColorText "    ✅ $component - Newly installed" "Green"
                $successCount++
            }
            "OPTIONAL_MISSING" {
                Write-ColorText "    ⚠️  $component - Not installed (optional)" "Yellow"
                $optionalCount++
            }
            "FAILED" {
                Write-ColorText "    ❌ $component - Failed" "Red"
                if ($result.Error) {
                    Write-ColorText "       Error: $($result.Error)" "Gray"
                }
                $failureCount++
            }
            "INSTALL_FAILED" {
                Write-ColorText "    ❌ $component - Installation failed" "Red"
                $failureCount++
            }
            "NOT_FOUND" {
                Write-ColorText "    ❌ $component - Not found" "Red"
                $failureCount++
            }
            default {
                Write-ColorText "    ❓ $component - Unknown status: $status" "Gray"
            }
        }
    }

    # Error Summary
    if ($Global:ErrorLog.Count -gt 0) {
        Write-Host ""
        Write-ColorText "🚨 Error Summary ($($Global:ErrorLog.Count) errors detected):" "Red"

        $errorGroups = $Global:ErrorLog | Group-Object -Property ErrorCode
        foreach ($group in $errorGroups) {
            Write-ColorText "    [$($group.Name)] - $($group.Count) occurrence(s)" "Red"
            foreach ($errorEntry in $group.Group | Select-Object -First 3) {
                Write-ColorText "      • $($errorEntry.Component): $($errorEntry.ErrorMessage)" "Gray"
                if ($errorEntry.Suggestion) {
                    Write-ColorText "        💡 $($errorEntry.Suggestion)" "Yellow"
                }
            }
            if ($group.Count -gt 3) {
                Write-ColorText "      ... and $($group.Count - 3) more" "Gray"
            }
        }

        # Generate error log file
        $errorLogPath = Join-Path $ProjectRoot "setup-errors-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
        $Global:ErrorLog | ConvertTo-Json -Depth 3 | Set-Content -Path $errorLogPath -Encoding UTF8
        Write-ColorText "    📄 Detailed error log saved to: $errorLogPath" "Cyan"
    }

    # Overall Status
    Write-Host ""
    Write-ColorText "📈 Overall Setup Status:" "White"
    Write-ColorText "    ✅ Successful: $successCount" "Green"
    Write-ColorText "    ❌ Failed: $failureCount" "Red"
    Write-ColorText "    ⚠️  Optional Missing: $optionalCount" "Yellow"

    $totalRequired = ($Global:InstallationResults.Values | Where-Object { $_.Required }).Count
    $successfulRequired = ($Global:InstallationResults.Values | Where-Object { $_.Required -and $_.Status -in @("SUCCESS", "INSTALLED") }).Count

    if ($successfulRequired -eq $totalRequired) {
        Write-ColorText "    🎉 All required components are ready!" "Green"
        $setupSuccess = $true
    } else {
        Write-ColorText "    ⚠️  $($totalRequired - $successfulRequired) required component(s) failed" "Red"
        $setupSuccess = $false
    }

    # Next Steps
    Write-Host ""
    if ($setupSuccess) {
        Write-ColorText "🏃‍♂️ Next Steps:" "White"
        Write-ColorText "    ├─ Run the API: dotnet run --project Presentations\WebApi" "Cyan"
        Write-ColorText "    ├─ API Docs: https://localhost:5001/scalar" "Cyan"
        Write-ColorText "    └─ Health Check: https://localhost:5001/health" "Cyan"

        if ($useDocker -and -not $UseLocal) {
            Write-Host ""
            Write-ColorText "🛑 To stop Docker services later:" "White"
            Write-ColorText "    └─ docker stop notify-postgres notify-redis" "Red"
        }
    } else {
        Write-ColorText "🔧 Required Actions:" "Red"
        Write-ColorText "    ├─ Fix the failed components listed above" "Yellow"
        Write-ColorText "    ├─ Re-run this script with -AutoInstall for automatic fixes" "Yellow"
        Write-ColorText "    └─ Check the error log for detailed troubleshooting" "Yellow"
    }

    # Troubleshooting Tips
    Write-Host ""
    Write-ColorText "💡 Troubleshooting Tips:" "White"
    Write-ColorText "    ├─ Run with -AutoInstall to automatically install missing dependencies" "Cyan"
    Write-ColorText "    ├─ Use -Force to reinstall existing components" "Cyan"
    Write-ColorText "    ├─ Check Windows PATH environment variable for missing tools" "Cyan"
    Write-ColorText "    ├─ Ensure you're running PowerShell as Administrator for installations" "Cyan"
    Write-ColorText "    └─ Restart your terminal after installing new tools" "Cyan"

    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "DarkGray"

    # Exit with appropriate code
    if (-not $setupSuccess) {
        Write-Host ""
        Write-ColorText "❌ Setup completed with errors. Please address the issues above." "Red"
        exit 1
    } else {
        Write-Host ""
        Write-ColorText "✅ Setup completed successfully!" "Green"
        exit 0
    }
}
