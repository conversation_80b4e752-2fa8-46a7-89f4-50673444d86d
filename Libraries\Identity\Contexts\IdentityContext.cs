﻿using Identity.Models;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Identity.Contexts;

public class IdentityContext : IdentityDbContext<
    ApplicationUser, ApplicationRole, int,
    ApplicationUserClaim, ApplicationUserRole, ApplicationUserLogin,
    ApplicationRoleClaim, ApplicationUserToken>
{
    public IdentityContext(DbContextOptions<IdentityContext> options) : base(options) { }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        builder.HasDefaultSchema("Identity");

        builder.ApplyConfiguration(new ApplicationUserConfig());
        builder.ApplyConfiguration(new ApplicationRoleConfig());
        builder.ApplyConfiguration(new ApplicationUserRoleConfig());
        builder.ApplyConfiguration(new ApplicationUserClaimConfig());
        builder.ApplyConfiguration(new ApplicationUserLoginConfig());
        builder.ApplyConfiguration(new ApplicationRoleClaimConfig());
        builder.ApplyConfiguration(new ApplicationUserTokenConfig());
    }

    private sealed record ApplicationUserConfig : IEntityTypeConfiguration<ApplicationUser>
    {
        public void Configure(EntityTypeBuilder<ApplicationUser> b)
        {
            b.ToTable("User");
        }
    }

    private sealed record ApplicationRoleConfig : IEntityTypeConfiguration<ApplicationRole>
    {
        public void Configure(EntityTypeBuilder<ApplicationRole> b)
        {
            b.ToTable("Role");
        }
    }

    private sealed record ApplicationUserRoleConfig : IEntityTypeConfiguration<ApplicationUserRole>
    {
        public void Configure(EntityTypeBuilder<ApplicationUserRole> b)
        {
            b.ToTable("UserRoles");

            b.HasKey(x => new { x.UserId, x.RoleId });

            b.HasOne(x => x.Role)
             .WithMany(r => r.UserRoles)
             .HasForeignKey(x => x.RoleId)
             .IsRequired();

            b.HasOne(x => x.User)
             .WithMany(u => u.UserRoles)
             .HasForeignKey(x => x.UserId)
             .IsRequired();
        }
    }

    private sealed record ApplicationUserClaimConfig : IEntityTypeConfiguration<ApplicationUserClaim>
    {
        public void Configure(EntityTypeBuilder<ApplicationUserClaim> b)
        {
            b.ToTable("UserClaims");
        }
    }

    private sealed record ApplicationUserLoginConfig : IEntityTypeConfiguration<ApplicationUserLogin>
    {
        public void Configure(EntityTypeBuilder<ApplicationUserLogin> b)
        {
            b.ToTable("UserLogins");
        }
    }

    private sealed record ApplicationRoleClaimConfig : IEntityTypeConfiguration<ApplicationRoleClaim>
    {
        public void Configure(EntityTypeBuilder<ApplicationRoleClaim> b)
        {
            b.ToTable("RoleClaims");
        }
    }

    private sealed record ApplicationUserTokenConfig : IEntityTypeConfiguration<ApplicationUserToken>
    {
        public void Configure(EntityTypeBuilder<ApplicationUserToken> b)
        {
            b.ToTable("UserTokens");
        }
    }
}
