#!/bin/bash

# ═══════════════════════════════════════════════════════════════════════════════
# 🎛️ Notify Service API - Development Menu (Unix/Linux/macOS)
# ═══════════════════════════════════════════════════════════════════════════════
# Interactive menu for development environment management

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
GRAY='\033[0;37m'
DARK_GRAY='\033[1;30m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

# Functions
show_header() {
    clear
    echo ""
    echo -e "${CYAN}    ███╗   ██╗ ██████╗ ████████╗██╗███████╗██╗   ██╗${NC}"
    echo -e "${CYAN}    ████╗  ██║██╔═══██╗╚══██╔══╝██║██╔════╝╚██╗ ██╔╝${NC}"
    echo -e "${CYAN}    ██╔██╗ ██║██║   ██║   ██║   ██║█████╗   ╚████╔╝ ${NC}"
    echo -e "${CYAN}    ██║╚██╗██║██║   ██║   ██║   ██║██╔══╝    ╚██╔╝  ${NC}"
    echo -e "${CYAN}    ██║ ╚████║╚██████╔╝   ██║   ██║██║        ██║   ${NC}"
    echo -e "${CYAN}    ╚═╝  ╚═══╝ ╚═════╝    ╚═╝   ╚═╝╚═╝        ╚═╝   ${NC}"
    echo ""
    echo -e "${YELLOW}    🎛️ Development Environment Manager${NC}"
    echo -e "${GRAY}    🚀 Quick Setup • 🔧 Migrations • 🛑 Cleanup${NC}"
    echo ""
    echo -e "${DARK_GRAY}═══════════════════════════════════════════════════════════════════════════════${NC}"
}

show_status() {
    echo ""
    echo -e "${WHITE}    📊 Current Status:${NC}"
    
    # Check Docker
    if command -v docker &> /dev/null && docker version &> /dev/null; then
        echo -e "${GRAY}    ├─ 🐳 Docker: ${GREEN}Running${NC}"
    else
        echo -e "${GRAY}    ├─ 🐳 Docker: ${RED}Not Available${NC}"
    fi
    
    # Check PostgreSQL container
    if docker ps --filter "name=notify-postgres" --format "{{.Status}}" 2>/dev/null | grep -q "Up"; then
        echo -e "${GRAY}    ├─ 🗄️  PostgreSQL: ${GREEN}Running${NC}"
    elif docker ps -a --filter "name=notify-postgres" --format "{{.Status}}" 2>/dev/null | grep -q "Exited"; then
        echo -e "${GRAY}    ├─ 🗄️  PostgreSQL: ${YELLOW}Stopped${NC}"
    else
        echo -e "${GRAY}    ├─ 🗄️  PostgreSQL: ${RED}Not Created${NC}"
    fi
    
    # Check Redis container
    if docker ps --filter "name=notify-redis" --format "{{.Status}}" 2>/dev/null | grep -q "Up"; then
        echo -e "${GRAY}    └─ 🔴 Redis: ${GREEN}Running${NC}"
    elif docker ps -a --filter "name=notify-redis" --format "{{.Status}}" 2>/dev/null | grep -q "Exited"; then
        echo -e "${GRAY}    └─ 🔴 Redis: ${YELLOW}Stopped${NC}"
    else
        echo -e "${GRAY}    └─ 🔴 Redis: ${RED}Not Created${NC}"
    fi
}

show_menu() {
    echo ""
    echo -e "${WHITE}    🎯 Available Actions:${NC}"
    echo ""
    echo -e "${GREEN}    [1] 🚀 Start Development Environment${NC}"
    echo "        └─ Start PostgreSQL + Redis + Apply Migrations"
    echo ""
    echo -e "${CYAN}    [2] 🔄 Restart Services${NC}"
    echo "        └─ Stop and start containers (preserve data)"
    echo ""
    echo -e "${YELLOW}    [3] 🛑 Stop Services${NC}"
    echo "        └─ Stop containers (preserve containers and data)"
    echo ""
    echo -e "${RED}    [4] 🗑️  Remove Containers${NC}"
    echo "        └─ Stop and remove containers (preserve data)"
    echo ""
    echo -e "${MAGENTA}    [5] 💥 Full Cleanup${NC}"
    echo "        └─ Remove containers and volumes (⚠️ DATA LOSS!)"
    echo ""
    echo -e "${BLUE}    [6] 🔧 Run Migrations Only${NC}"
    echo "        └─ Apply Entity Framework migrations"
    echo ""
    echo -e "${GREEN}    [7] 🏃‍♂️ Run API${NC}"
    echo "        └─ Start the Notify Service API"
    echo ""
    echo -e "${BLUE}    [8] ⚙️  Configuration Manager${NC}"
    echo "        └─ Manage environment variables and secrets"
    echo ""
    echo -e "${CYAN}    [9] 🌐 Open Documentation${NC}"
    echo "        └─ Open Scalar UI in browser"
    echo ""
    echo -e "${GRAY}    [0] 🚪 Exit${NC}"
    echo ""
}

execute_action() {
    local choice=$1
    
    case $choice in
        "1")
            echo -e "${GREEN}🚀 Starting development environment...${NC}"
            "$SCRIPT_DIR/start-dev-services.sh"
            read -p "Press Enter to continue..."
            ;;
        "2")
            echo -e "${CYAN}🔄 Restarting services...${NC}"
            "$SCRIPT_DIR/stop-dev-services.sh"
            sleep 2
            "$SCRIPT_DIR/start-dev-services.sh"
            read -p "Press Enter to continue..."
            ;;
        "3")
            echo -e "${YELLOW}🛑 Stopping services...${NC}"
            "$SCRIPT_DIR/stop-dev-services.sh"
            read -p "Press Enter to continue..."
            ;;
        "4")
            echo -e "${RED}🗑️ Removing containers...${NC}"
            "$SCRIPT_DIR/stop-dev-services.sh" --remove
            read -p "Press Enter to continue..."
            ;;
        "5")
            echo -e "${MAGENTA}💥 Full cleanup...${NC}"
            "$SCRIPT_DIR/stop-dev-services.sh" --remove --remove-volumes
            read -p "Press Enter to continue..."
            ;;
        "6")
            echo -e "${BLUE}🔧 Running migrations...${NC}"
            cd "$PROJECT_ROOT"
            
            echo -e "${CYAN}Applying Data migrations...${NC}"
            dotnet ef database update --project Libraries/Data --startup-project Presentations/WebApi --context ApplicationDbContext
            
            echo -e "${CYAN}Applying Identity migrations...${NC}"
            dotnet ef database update --project Libraries/Identity --startup-project Presentations/WebApi --context IdentityContext
            
            read -p "Press Enter to continue..."
            ;;
        "7")
            echo -e "${GREEN}🏃‍♂️ Starting API...${NC}"
            cd "$PROJECT_ROOT"
            dotnet run --project Presentations/WebApi
            ;;
        "8")
            echo -e "${BLUE}⚙️ Opening configuration manager...${NC}"
            "$SCRIPT_DIR/config-manager.sh"
            read -p "Press Enter to continue..."
            ;;
        "9")
            echo -e "${CYAN}🌐 Opening documentation...${NC}"
            if command -v xdg-open &> /dev/null; then
                xdg-open "https://localhost:5001/scalar"
            elif command -v open &> /dev/null; then
                open "https://localhost:5001/scalar"
            else
                echo "Please open https://localhost:5001/scalar in your browser"
            fi
            sleep 1
            ;;
        "0")
            echo -e "${GRAY}👋 Goodbye!${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ Invalid choice. Please try again.${NC}"
            sleep 2
            ;;
    esac
}

# Main loop
while true; do
    show_header
    show_status
    show_menu
    
    echo -ne "${WHITE}    👉 Enter your choice (0-9): ${NC}"
    read choice
    
    execute_action "$choice"
done
