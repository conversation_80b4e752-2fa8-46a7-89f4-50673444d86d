# ═══════════════════════════════════════════════════════════════════════════════
# 🏥 Notify Service API - Comprehensive Health Check
# ═══════════════════════════════════════════════════════════════════════════════
# Validates all services and dependencies for the development environment

param(
    [switch]$Detailed,     # Show detailed information
    [switch]$Fix,          # Attempt to fix issues automatically
    [switch]$Quiet,        # Minimal output
    [switch]$Json          # Output results in JSON format
)

# Configuration
$ProjectRoot = Split-Path -Parent (Split-Path -Parent $MyInvocation.MyCommand.Path)
$EnvFile = Join-Path $ProjectRoot ".env"

# Health check results
$HealthResults = @{
    "Overall" = "UNKNOWN"
    "Services" = @{}
    "Dependencies" = @{}
    "Configuration" = @{}
    "Recommendations" = @()
}

# Colors
$Colors = @{
    Red = "Red"; Green = "Green"; Yellow = "Yellow"; Blue = "Blue"
    Cyan = "Cyan"; Magenta = "Magenta"; Gray = "Gray"; White = "White"
}

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    if (-not $Quiet -and -not $Json) { Write-Host $Text -ForegroundColor $Colors[$Color] }
}

function Write-HealthStatus {
    param([string]$Component, [string]$Status, [string]$Message = "", [string]$Details = "")
    
    $statusIcon = switch ($Status) {
        "HEALTHY" { "✅" }
        "WARNING" { "⚠️" }
        "ERROR" { "❌" }
        "UNKNOWN" { "❓" }
        default { "❓" }
    }
    
    if (-not $Json) {
        $output = "[$statusIcon] $Component`: $Status"
        if ($Message) { $output += " - $Message" }
        
        $color = switch ($Status) {
            "HEALTHY" { "Green" }
            "WARNING" { "Yellow" }
            "ERROR" { "Red" }
            default { "Gray" }
        }
        
        Write-ColorText $output $color
        
        if ($Detailed -and $Details) {
            Write-ColorText "    └─ $Details" "Gray"
        }
    }
    
    # Store result
    $HealthResults.Services[$Component] = @{
        "Status" = $Status
        "Message" = $Message
        "Details" = $Details
    }
}

function Test-ServiceHealth {
    param([string]$ServiceName, [string]$TestCommand, [string]$ExpectedOutput = "", [string]$FixCommand = "")
    
    try {
        $result = Invoke-Expression "$TestCommand 2>`$null"
        $exitCode = $LASTEXITCODE
        
        if ($exitCode -eq 0) {
            if ($ExpectedOutput -and $result -notlike "*$ExpectedOutput*") {
                Write-HealthStatus $ServiceName "WARNING" "Service responding but unexpected output" $result
                return $false
            } else {
                Write-HealthStatus $ServiceName "HEALTHY" "Service is running correctly" $result
                return $true
            }
        } else {
            Write-HealthStatus $ServiceName "ERROR" "Service not responding" "Exit code: $exitCode"
            
            if ($Fix -and $FixCommand) {
                Write-ColorText "    🔧 Attempting to fix $ServiceName..." "Yellow"
                try {
                    Invoke-Expression $FixCommand
                    Write-ColorText "    ✅ Fix command executed" "Green"
                } catch {
                    Write-ColorText "    ❌ Fix failed: $($_.Exception.Message)" "Red"
                }
            }
            return $false
        }
    } catch {
        Write-HealthStatus $ServiceName "ERROR" "Failed to test service" $_.Exception.Message
        return $false
    }
}

function Test-DependencyHealth {
    param([string]$DependencyName, [string]$TestCommand, [string]$InstallCommand = "")
    
    try {
        $result = Invoke-Expression "$TestCommand 2>`$null"
        if ($LASTEXITCODE -eq 0) {
            Write-HealthStatus $DependencyName "HEALTHY" "Available" $result
            $HealthResults.Dependencies[$DependencyName] = "HEALTHY"
            return $true
        } else {
            Write-HealthStatus $DependencyName "ERROR" "Not available"
            $HealthResults.Dependencies[$DependencyName] = "ERROR"
            
            if ($Fix -and $InstallCommand) {
                Write-ColorText "    🔧 Attempting to install $DependencyName..." "Yellow"
                try {
                    Invoke-Expression $InstallCommand
                    Write-ColorText "    ✅ Installation command executed" "Green"
                } catch {
                    Write-ColorText "    ❌ Installation failed: $($_.Exception.Message)" "Red"
                }
            }
            return $false
        }
    } catch {
        Write-HealthStatus $DependencyName "ERROR" "Failed to check dependency" $_.Exception.Message
        $HealthResults.Dependencies[$DependencyName] = "ERROR"
        return $false
    }
}

function Test-ConfigurationHealth {
    Write-ColorText "`n🔧 Configuration Health Check" "Cyan"
    
    # Check .env file
    if (Test-Path $EnvFile) {
        Write-HealthStatus "Environment File" "HEALTHY" ".env file exists"
        
        # Load and validate environment variables
        $envContent = Get-Content $EnvFile
        $requiredVars = @("POSTGRES_PASSWORD", "JWT_SECRET_KEY", "ASPNETCORE_ENVIRONMENT")
        $missingVars = @()
        
        foreach ($var in $requiredVars) {
            $found = $envContent | Where-Object { $_ -like "$var=*" }
            if (-not $found) {
                $missingVars += $var
            }
        }
        
        if ($missingVars.Count -gt 0) {
            Write-HealthStatus "Environment Variables" "WARNING" "Missing required variables" ($missingVars -join ", ")
            $HealthResults.Recommendations += "Add missing environment variables: $($missingVars -join ', ')"
        } else {
            Write-HealthStatus "Environment Variables" "HEALTHY" "All required variables present"
        }
    } else {
        Write-HealthStatus "Environment File" "ERROR" ".env file not found"
        $HealthResults.Recommendations += "Run .\scripts\config-manager.ps1 to create configuration"
    }
    
    # Check project files
    $projectFile = Join-Path $ProjectRoot "Presentations\WebApi\WebApi.csproj"
    if (Test-Path $projectFile) {
        Write-HealthStatus "Project Files" "HEALTHY" "WebApi project found"
    } else {
        Write-HealthStatus "Project Files" "ERROR" "WebApi project not found"
    }
}

function Show-Header {
    if ($Json) { return }
    
    Clear-Host
    Write-Host ""
    Write-ColorText "    🏥🏥🏥 NOTIFY SERVICE API - HEALTH CHECK 🏥🏥🏥" "Cyan"
    Write-Host ""
    Write-ColorText "    🔍 Comprehensive System Validation" "Yellow"
    Write-ColorText "    📊 Services • Dependencies • Configuration" "Gray"
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "Gray"
}

function Show-Summary {
    if ($Json) {
        # Calculate overall health
        $errorCount = ($HealthResults.Services.Values | Where-Object { $_.Status -eq "ERROR" }).Count
        $warningCount = ($HealthResults.Services.Values | Where-Object { $_.Status -eq "WARNING" }).Count
        
        if ($errorCount -gt 0) {
            $HealthResults.Overall = "ERROR"
        } elseif ($warningCount -gt 0) {
            $HealthResults.Overall = "WARNING"
        } else {
            $HealthResults.Overall = "HEALTHY"
        }
        
        $HealthResults | ConvertTo-Json -Depth 10
        return
    }
    
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "Gray"
    Write-ColorText "📊 Health Check Summary" "White"
    Write-Host ""
    
    $healthyCount = ($HealthResults.Services.Values | Where-Object { $_.Status -eq "HEALTHY" }).Count
    $warningCount = ($HealthResults.Services.Values | Where-Object { $_.Status -eq "WARNING" }).Count
    $errorCount = ($HealthResults.Services.Values | Where-Object { $_.Status -eq "ERROR" }).Count
    
    Write-ColorText "✅ Healthy: $healthyCount" "Green"
    Write-ColorText "⚠️ Warnings: $warningCount" "Yellow"
    Write-ColorText "❌ Errors: $errorCount" "Red"
    
    if ($HealthResults.Recommendations.Count -gt 0) {
        Write-Host ""
        Write-ColorText "💡 Recommendations:" "Yellow"
        foreach ($rec in $HealthResults.Recommendations) {
            Write-ColorText "   • $rec" "Gray"
        }
    }
    
    Write-Host ""
    if ($errorCount -eq 0 -and $warningCount -eq 0) {
        Write-ColorText "🎉 All systems are healthy! Ready to develop." "Green"
    } elseif ($errorCount -eq 0) {
        Write-ColorText "⚠️ System is mostly healthy with minor warnings." "Yellow"
    } else {
        Write-ColorText "❌ Critical issues detected. Please address errors before proceeding." "Red"
    }
    Write-Host ""
}

# Main execution
Show-Header

# Check dependencies
Write-ColorText "🔍 Dependency Health Check" "Cyan"
Test-DependencyHealth ".NET SDK" "dotnet --version" "winget install Microsoft.DotNet.SDK.8"
Test-DependencyHealth "Docker" "docker --version" "winget install Docker.DockerDesktop"
Test-DependencyHealth "Git" "git --version" "winget install Git.Git"

# Check services
Write-ColorText "`n🚀 Service Health Check" "Cyan"

# Check if using Docker or local services
$useDocker = $false
try {
    docker info | Out-Null 2>&1
    if ($LASTEXITCODE -eq 0) {
        $useDocker = $true
        # Check Docker containers
        Test-ServiceHealth "PostgreSQL (Docker)" "docker exec notify-postgres psql -U postgres -c 'SELECT 1;'" "" "docker start notify-postgres"
        Test-ServiceHealth "Redis (Docker)" "docker exec notify-redis redis-cli ping" "PONG" "docker start notify-redis"
    }
} catch {
    # Docker not available, check local services
}

if (-not $useDocker) {
    # Check local services
    Test-ServiceHealth "PostgreSQL (Local)" "psql -U postgres -c 'SELECT 1;'" "" ""
    Test-ServiceHealth "Redis (Local)" "redis-cli ping" "PONG" ""
}

# Check configuration
Test-ConfigurationHealth

# Show summary
Show-Summary
