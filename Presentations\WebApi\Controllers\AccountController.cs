﻿using Models.DbEntities;
using Identity.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Models.DTOs.Account;
using Services.Interfaces;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace WebApi.Controllers;

[Route("api/[controller]")]
[ApiController]
public class AccountController : ControllerBase
{
    private readonly IAccountService _accountService;
    private readonly ILoginLogService _loginLogService;
    public AccountController(IAccountService accountService, ILoginLogService loginLogService)
    {
        _accountService = accountService;
        _loginLogService = loginLogService;
    }

    [HttpPost("authenticate")]
    public async Task<IActionResult> AuthenticateAsync(AuthenticationRequest request)
    {
        //auth
        var result = await _accountService.AuthenticateAsync(request);
        if (result.Errors == null || !result.Errors.Any())
        {
            //mongo usage example
            var log = new LoginLog()
            {
                LoginTime = DateTime.Now,
                UserEmail = request.Email
            };
            await _loginLogService.Add(log);
        }
        return Ok(result);
    }

    [HttpPost("register")]
    public async Task<IActionResult> RegisterAsync(RegisterRequest request)
    {
        var uri = $"{Request.Scheme}://{Request.Host.Value}";
        return Ok(await _accountService.RegisterAsync(request, uri));
    }

    [HttpGet("confirm-email")]
    public async Task<IActionResult> ConfirmEmailAsync([FromQuery] string userId, [FromQuery] string code)
    {
        return Ok(await _accountService.ConfirmEmailAsync(userId, code));
    }

    [HttpPost("forgot-password")]
    public async Task<IActionResult> ForgotPasswordAsync(ForgotPasswordRequest request)
    {
        var uri = $"{Request.Scheme}://{Request.Host.Value}";
        await _accountService.ForgotPasswordAsync(request, uri);
        return Ok();
    }

    [HttpPost("reset-password")]
    public async Task<IActionResult> ResetPasswordAsync(ResetPasswordRequest request)
    {
        return Ok(await _accountService.ResetPasswordAsync(request));
    }

    [HttpPost("refreshtoken")]
    public async Task<IActionResult> RefreshTokenAsync(RefreshTokenRequest request)
    {
        return Ok(await _accountService.RefreshTokenAsync(request));
    }

    [HttpGet("logout")]
    public async Task<IActionResult> LogoutAsync(string userEmail)
    {
        return Ok(await _accountService.LogoutAsync(userEmail));
    }

    private string GenerateIPAddress()
    {
        if (Request.Headers.ContainsKey("X-Forwarded-For"))
            return Request.Headers["X-Forwarded-For"];
        else
            return HttpContext.Connection.RemoteIpAddress.MapToIPv4().ToString();
    }
}