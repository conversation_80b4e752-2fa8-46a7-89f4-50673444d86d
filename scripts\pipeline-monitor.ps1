# ═══════════════════════════════════════════════════════════════════════════════
# 📊 Notify Service API - Pipeline Monitoring & Notifications
# ═══════════════════════════════════════════════════════════════════════════════
# Monitor pipeline status, deployment health, and send notifications

param(
    [string]$Action = "menu",           # menu, monitor, notify, dashboard, alerts
    [string]$Environment = "",          # dev, staging, prod, all
    [string]$BuildId = "",              # Specific build to monitor
    [int]$RefreshInterval = 30,         # Refresh interval in seconds
    [switch]$Continuous,                # Continuous monitoring
    [switch]$Alerts,                    # Enable alerts
    [switch]$Dashboard,                 # Show dashboard
    [switch]$Json                       # JSON output
)

# Configuration
$ProjectRoot = Split-Path -Parent (Split-Path -Parent $MyInvocation.MyCommand.Path)
$MonitorConfigFile = Join-Path $ProjectRoot "monitor-config.json"
$AlertsConfigFile = Join-Path $ProjectRoot "alerts-config.json"
$LogsDir = Join-Path $ProjectRoot "logs"
$MetricsDir = Join-Path $ProjectRoot "metrics"

# Ensure directories exist
@($LogsDir, $MetricsDir) | ForEach-Object {
    if (-not (Test-Path $_)) {
        New-Item -ItemType Directory -Path $_ | Out-Null
    }
}

# Colors
$Colors = @{
    Red = "Red"; Green = "Green"; Yellow = "Yellow"; Blue = "Blue"
    Cyan = "Cyan"; Magenta = "Magenta"; Gray = "Gray"; White = "White"
}

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    if (-not $Json) { Write-Host $Text -ForegroundColor $Colors[$Color] }
}

function Write-MonitorLog {
    param([string]$Message, [string]$Level = "INFO", [string]$Component = "")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level]"
    
    if ($Component) { $logEntry += " [$Component]" }
    $logEntry += " $Message"
    
    # Write to console
    if (-not $Json) {
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            "ALERT" { "Magenta" }
            default { "Cyan" }
        }
        Write-ColorText $logEntry $color
    }
    
    # Write to log file
    $logFile = Join-Path $LogsDir "monitor-$(Get-Date -Format 'yyyy-MM-dd').log"
    Add-Content -Path $logFile -Value $logEntry
}

function Show-Header {
    if ($Json) { return }
    
    Clear-Host
    Write-Host ""
    Write-ColorText "    📊📊📊 PIPELINE MONITORING & NOTIFICATIONS 📊📊📊" "Cyan"
    Write-Host ""
    Write-ColorText "    🔍 Real-time Monitoring • Alerts • Dashboard" "Yellow"
    Write-ColorText "    📈 Metrics • Health Checks • Notifications" "Gray"
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "Gray"
}

function Initialize-MonitorConfiguration {
    Write-MonitorLog "Initializing monitoring configuration..." "INFO"
    
    if (-not (Test-Path $MonitorConfigFile)) {
        $defaultConfig = @{
            "monitoring" = @{
                "refreshInterval" = 30
                "healthCheckTimeout" = 10
                "retryAttempts" = 3
                "environments" = @{
                    "dev" = @{
                        "enabled" = $true
                        "healthUrl" = "https://notify-service-api-dev.azurewebsites.net/health"
                        "metricsUrl" = "https://notify-service-api-dev.azurewebsites.net/metrics"
                        "alertThresholds" = @{
                            "responseTime" = 5000
                            "errorRate" = 5
                            "availability" = 95
                        }
                    }
                    "staging" = @{
                        "enabled" = $true
                        "healthUrl" = "https://notify-service-api-staging.azurewebsites.net/health"
                        "metricsUrl" = "https://notify-service-api-staging.azurewebsites.net/metrics"
                        "alertThresholds" = @{
                            "responseTime" = 3000
                            "errorRate" = 2
                            "availability" = 98
                        }
                    }
                    "prod" = @{
                        "enabled" = $true
                        "healthUrl" = "https://notify-service-api-prod.azurewebsites.net/health"
                        "metricsUrl" = "https://notify-service-api-prod.azurewebsites.net/metrics"
                        "alertThresholds" = @{
                            "responseTime" = 2000
                            "errorRate" = 1
                            "availability" = 99.5
                        }
                    }
                }
            }
            "notifications" = @{
                "slack" = @{
                    "enabled" = $false
                    "webhookUrl" = ""
                    "channel" = "#alerts"
                    "username" = "Pipeline Monitor"
                    "iconEmoji" = ":robot_face:"
                }
                "teams" = @{
                    "enabled" = $false
                    "webhookUrl" = ""
                }
                "email" = @{
                    "enabled" = $false
                    "smtpServer" = "smtp.gmail.com"
                    "smtpPort" = 587
                    "username" = ""
                    "password" = ""
                    "recipients" = @()
                }
            }
            "dashboard" = @{
                "enabled" = $true
                "port" = 8080
                "refreshInterval" = 10
                "historyDays" = 7
            }
        }
        
        $defaultConfig | ConvertTo-Json -Depth 10 | Set-Content -Path $MonitorConfigFile -Encoding UTF8
        Write-MonitorLog "Default monitoring configuration created" "SUCCESS"
    }
    
    return Get-Content $MonitorConfigFile | ConvertFrom-Json
}

function Test-ServiceHealth {
    param(
        [string]$HealthUrl,
        [string]$Environment,
        [int]$Timeout = 10
    )
    
    try {
        $startTime = Get-Date
        $response = Invoke-WebRequest -Uri $HealthUrl -TimeoutSec $Timeout -UseBasicParsing
        $responseTime = (Get-Date) - $startTime
        
        $healthData = @{
            "environment" = $Environment
            "status" = if ($response.StatusCode -eq 200) { "healthy" } else { "unhealthy" }
            "statusCode" = $response.StatusCode
            "responseTime" = $responseTime.TotalMilliseconds
            "timestamp" = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
            "url" = $HealthUrl
        }
        
        if ($response.StatusCode -eq 200) {
            try {
                $healthContent = $response.Content | ConvertFrom-Json
                $healthData.details = $healthContent
            } catch {
                $healthData.details = $response.Content
            }
        }
        
        return $healthData
    } catch {
        return @{
            "environment" = $Environment
            "status" = "error"
            "error" = $_.Exception.Message
            "timestamp" = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
            "url" = $HealthUrl
        }
    }
}

function Send-SlackNotification {
    param(
        [string]$WebhookUrl,
        [string]$Message,
        [string]$Color = "good",
        [string]$Channel = "#alerts"
    )
    
    $payload = @{
        "channel" = $Channel
        "username" = "Pipeline Monitor"
        "icon_emoji" = ":robot_face:"
        "attachments" = @(
            @{
                "color" = $Color
                "text" = $Message
                "ts" = [int][double]::Parse((Get-Date -UFormat %s))
            }
        )
    }
    
    try {
        $body = $payload | ConvertTo-Json -Depth 10
        Invoke-RestMethod -Uri $WebhookUrl -Method POST -Body $body -ContentType "application/json"
        Write-MonitorLog "Slack notification sent successfully" "SUCCESS" "Notifications"
    } catch {
        Write-MonitorLog "Failed to send Slack notification: $($_.Exception.Message)" "ERROR" "Notifications"
    }
}

function Send-TeamsNotification {
    param(
        [string]$WebhookUrl,
        [string]$Title,
        [string]$Message,
        [string]$Color = "Good"
    )
    
    $payload = @{
        "@type" = "MessageCard"
        "@context" = "http://schema.org/extensions"
        "themeColor" = $Color
        "summary" = $Title
        "sections" = @(
            @{
                "activityTitle" = $Title
                "activitySubtitle" = "Pipeline Monitor Alert"
                "text" = $Message
                "markdown" = $true
            }
        )
    }
    
    try {
        $body = $payload | ConvertTo-Json -Depth 10
        Invoke-RestMethod -Uri $WebhookUrl -Method POST -Body $body -ContentType "application/json"
        Write-MonitorLog "Teams notification sent successfully" "SUCCESS" "Notifications"
    } catch {
        Write-MonitorLog "Failed to send Teams notification: $($_.Exception.Message)" "ERROR" "Notifications"
    }
}

function Check-AlertThresholds {
    param(
        [object]$HealthData,
        [object]$Thresholds,
        [object]$NotificationConfig
    )
    
    $alerts = @()
    
    # Check response time
    if ($HealthData.responseTime -gt $Thresholds.responseTime) {
        $alerts += @{
            "type" = "responseTime"
            "severity" = "warning"
            "message" = "Response time ($($HealthData.responseTime)ms) exceeds threshold ($($Thresholds.responseTime)ms)"
            "environment" = $HealthData.environment
        }
    }
    
    # Check if service is unhealthy
    if ($HealthData.status -ne "healthy") {
        $alerts += @{
            "type" = "health"
            "severity" = "critical"
            "message" = "Service health check failed: $($HealthData.status)"
            "environment" = $HealthData.environment
        }
    }
    
    # Send notifications for alerts
    foreach ($alert in $alerts) {
        $alertMessage = "🚨 ALERT: [$($alert.environment.ToUpper())] $($alert.message)"
        $color = if ($alert.severity -eq "critical") { "danger" } else { "warning" }
        
        Write-MonitorLog $alertMessage "ALERT" $alert.environment
        
        # Send Slack notification
        if ($NotificationConfig.slack.enabled -and $NotificationConfig.slack.webhookUrl) {
            Send-SlackNotification -WebhookUrl $NotificationConfig.slack.webhookUrl -Message $alertMessage -Color $color -Channel $NotificationConfig.slack.channel
        }
        
        # Send Teams notification
        if ($NotificationConfig.teams.enabled -and $NotificationConfig.teams.webhookUrl) {
            Send-TeamsNotification -WebhookUrl $NotificationConfig.teams.webhookUrl -Title "Pipeline Alert" -Message $alertMessage -Color $color
        }
    }
    
    return $alerts
}

function Save-Metrics {
    param([object]$HealthData)
    
    $metricsFile = Join-Path $MetricsDir "health-metrics-$(Get-Date -Format 'yyyy-MM-dd').json"
    
    $metrics = @()
    if (Test-Path $metricsFile) {
        $metrics = Get-Content $metricsFile | ConvertFrom-Json
    }
    
    $metrics += $HealthData
    $metrics | ConvertTo-Json -Depth 10 | Set-Content -Path $metricsFile -Encoding UTF8
}

function Show-Dashboard {
    param([object]$Config, [object]$HealthResults)
    
    if ($Json) {
        $HealthResults | ConvertTo-Json -Depth 10
        return
    }
    
    Clear-Host
    Show-Header
    
    Write-Host ""
    Write-ColorText "📊 Real-time Dashboard" "Cyan"
    Write-ColorText "Last Updated: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "Gray"
    Write-Host ""
    
    foreach ($env in $HealthResults.Keys) {
        $health = $HealthResults[$env]
        $statusIcon = switch ($health.status) {
            "healthy" { "✅" }
            "unhealthy" { "⚠️" }
            "error" { "❌" }
            default { "❓" }
        }
        
        $statusColor = switch ($health.status) {
            "healthy" { "Green" }
            "unhealthy" { "Yellow" }
            "error" { "Red" }
            default { "Gray" }
        }
        
        Write-ColorText "[$statusIcon] $($env.ToUpper())" $statusColor
        
        if ($health.responseTime) {
            Write-ColorText "    Response Time: $([math]::Round($health.responseTime, 2))ms" "White"
        }
        
        if ($health.error) {
            Write-ColorText "    Error: $($health.error)" "Red"
        }
        
        Write-Host ""
    }
    
    Write-ColorText "Press Ctrl+C to stop monitoring" "Gray"
}

function Start-ContinuousMonitoring {
    param([object]$Config)
    
    Write-MonitorLog "Starting continuous monitoring..." "INFO"
    
    try {
        while ($true) {
            $healthResults = @{}
            
            foreach ($env in $Config.monitoring.environments.PSObject.Properties.Name) {
                $envConfig = $Config.monitoring.environments.$env
                
                if ($envConfig.enabled) {
                    $health = Test-ServiceHealth -HealthUrl $envConfig.healthUrl -Environment $env -Timeout $Config.monitoring.healthCheckTimeout
                    $healthResults[$env] = $health
                    
                    # Save metrics
                    Save-Metrics -HealthData $health
                    
                    # Check alerts
                    if ($Alerts) {
                        Check-AlertThresholds -HealthData $health -Thresholds $envConfig.alertThresholds -NotificationConfig $Config.notifications
                    }
                }
            }
            
            if ($Dashboard) {
                Show-Dashboard -Config $Config -HealthResults $healthResults
            }
            
            Start-Sleep -Seconds $RefreshInterval
        }
    } catch [System.Management.Automation.PipelineStoppedException] {
        Write-MonitorLog "Monitoring stopped by user" "INFO"
    } catch {
        Write-MonitorLog "Monitoring error: $($_.Exception.Message)" "ERROR"
    }
}

function Show-MonitorMenu {
    Write-Host ""
    Write-ColorText "📊 Pipeline Monitoring Menu" "Cyan"
    Write-Host ""
    Write-ColorText "    [1] 🔍 Health Check" "Green"
    Write-ColorText "        └─ Check service health across environments"
    Write-Host ""
    Write-ColorText "    [2] 📈 Start Monitoring" "Blue"
    Write-ColorText "        └─ Continuous monitoring with dashboard"
    Write-Host ""
    Write-ColorText "    [3] 🚨 Configure Alerts" "Yellow"
    Write-ColorText "        └─ Set up notification thresholds"
    Write-Host ""
    Write-ColorText "    [4] 📊 View Metrics" "Magenta"
    Write-ColorText "        └─ Historical performance data"
    Write-Host ""
    Write-ColorText "    [5] 🔔 Test Notifications" "Cyan"
    Write-ColorText "        └─ Test Slack/Teams notifications"
    Write-Host ""
    Write-ColorText "    [0] 🚪 Exit" "Gray"
    Write-Host ""
}

# Main execution
Show-Header

$config = Initialize-MonitorConfiguration

if ($Action -eq "menu") {
    do {
        Show-MonitorMenu
        $choice = Read-Host "Enter your choice (0-5)"
        
        switch ($choice) {
            "1" {
                Write-MonitorLog "Running health checks..." "INFO"
                
                $healthResults = @{}
                foreach ($env in $config.monitoring.environments.PSObject.Properties.Name) {
                    $envConfig = $config.monitoring.environments.$env
                    
                    if ($envConfig.enabled) {
                        Write-MonitorLog "Checking $env environment..." "INFO"
                        $health = Test-ServiceHealth -HealthUrl $envConfig.healthUrl -Environment $env
                        $healthResults[$env] = $health
                        
                        $statusIcon = if ($health.status -eq "healthy") { "✅" } else { "❌" }
                        Write-MonitorLog "$statusIcon $env`: $($health.status)" "INFO"
                    }
                }
                
                Read-Host "Press Enter to continue"
            }
            "2" {
                $Dashboard = $true
                $Alerts = $true
                Start-ContinuousMonitoring -Config $config
                Read-Host "Press Enter to continue"
            }
            "3" {
                Write-MonitorLog "Alert configuration functionality coming soon..." "INFO"
                Read-Host "Press Enter to continue"
            }
            "4" {
                Write-MonitorLog "Metrics viewing functionality coming soon..." "INFO"
                Read-Host "Press Enter to continue"
            }
            "5" {
                Write-MonitorLog "Notification testing functionality coming soon..." "INFO"
                Read-Host "Press Enter to continue"
            }
            "0" {
                Write-MonitorLog "Goodbye!" "INFO"
                break
            }
            default {
                Write-MonitorLog "Invalid choice. Please try again." "WARN"
                Read-Host "Press Enter to continue"
            }
        }
    } while ($choice -ne "0")
} elseif ($Action -eq "monitor" -and $Continuous) {
    $Dashboard = $true
    $Alerts = $true
    Start-ContinuousMonitoring -Config $config
}
