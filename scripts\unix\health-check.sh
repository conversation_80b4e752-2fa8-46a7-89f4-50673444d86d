#!/bin/bash

# ═══════════════════════════════════════════════════════════════════════════════
# 🏥 Notify Service API - Comprehensive Health Check (Unix/Linux/macOS)
# ═══════════════════════════════════════════════════════════════════════════════
# Validates all services and dependencies for the development environment

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
ENV_FILE="$PROJECT_ROOT/.env"

# Default options
DETAILED=false
FIX=false
QUIET=false
JSON=false

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
GRAY='\033[0;37m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Health check results
declare -A HEALTH_RESULTS
HEALTHY_COUNT=0
WARNING_COUNT=0
ERROR_COUNT=0
RECOMMENDATIONS=()

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --detailed)
            DETAILED=true
            shift
            ;;
        --fix)
            FIX=true
            shift
            ;;
        --quiet)
            QUIET=true
            shift
            ;;
        --json)
            JSON=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "OPTIONS:"
            echo "  --detailed    Show detailed information"
            echo "  --fix         Attempt to fix issues automatically"
            echo "  --quiet       Minimal output"
            echo "  --json        Output results in JSON format"
            echo "  --help        Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

write_color_text() {
    local text=$1
    local color=${2:-$WHITE}
    if [[ "$QUIET" != "true" && "$JSON" != "true" ]]; then
        echo -e "${color}${text}${NC}"
    fi
}

write_health_status() {
    local component=$1
    local status=$2
    local message=${3:-""}
    local details=${4:-""}
    
    local status_icon
    case $status in
        "HEALTHY") status_icon="✅"; ((HEALTHY_COUNT++)) ;;
        "WARNING") status_icon="⚠️"; ((WARNING_COUNT++)) ;;
        "ERROR") status_icon="❌"; ((ERROR_COUNT++)) ;;
        *) status_icon="❓" ;;
    esac
    
    if [[ "$JSON" != "true" ]]; then
        local output="[$status_icon] $component: $status"
        if [[ -n "$message" ]]; then
            output+=" - $message"
        fi
        
        local color
        case $status in
            "HEALTHY") color=$GREEN ;;
            "WARNING") color=$YELLOW ;;
            "ERROR") color=$RED ;;
            *) color=$GRAY ;;
        esac
        
        write_color_text "$output" "$color"
        
        if [[ "$DETAILED" == "true" && -n "$details" ]]; then
            write_color_text "    └─ $details" "$GRAY"
        fi
    fi
    
    # Store result
    HEALTH_RESULTS["$component"]="$status|$message|$details"
}

test_service_health() {
    local service_name=$1
    local test_command=$2
    local expected_output=${3:-""}
    local fix_command=${4:-""}
    
    if eval "$test_command" &> /dev/null; then
        if [[ -n "$expected_output" ]]; then
            local result
            result=$(eval "$test_command" 2>/dev/null)
            if [[ "$result" == *"$expected_output"* ]]; then
                write_health_status "$service_name" "HEALTHY" "Service is running correctly" "$result"
                return 0
            else
                write_health_status "$service_name" "WARNING" "Service responding but unexpected output" "$result"
                return 1
            fi
        else
            write_health_status "$service_name" "HEALTHY" "Service is running correctly"
            return 0
        fi
    else
        write_health_status "$service_name" "ERROR" "Service not responding"
        
        if [[ "$FIX" == "true" && -n "$fix_command" ]]; then
            write_color_text "    🔧 Attempting to fix $service_name..." "$YELLOW"
            if eval "$fix_command" &> /dev/null; then
                write_color_text "    ✅ Fix command executed" "$GREEN"
            else
                write_color_text "    ❌ Fix failed" "$RED"
            fi
        fi
        return 1
    fi
}

test_dependency_health() {
    local dependency_name=$1
    local test_command=$2
    local install_command=${3:-""}
    
    if command -v ${test_command%% *} &> /dev/null && eval "$test_command" &> /dev/null; then
        local result
        result=$(eval "$test_command" 2>/dev/null)
        write_health_status "$dependency_name" "HEALTHY" "Available" "$result"
        return 0
    else
        write_health_status "$dependency_name" "ERROR" "Not available"
        
        if [[ "$FIX" == "true" && -n "$install_command" ]]; then
            write_color_text "    🔧 Attempting to install $dependency_name..." "$YELLOW"
            if eval "$install_command" &> /dev/null; then
                write_color_text "    ✅ Installation command executed" "$GREEN"
            else
                write_color_text "    ❌ Installation failed" "$RED"
            fi
        fi
        return 1
    fi
}

test_configuration_health() {
    write_color_text "" 
    write_color_text "🔧 Configuration Health Check" "$CYAN"
    
    # Check .env file
    if [[ -f "$ENV_FILE" ]]; then
        write_health_status "Environment File" "HEALTHY" ".env file exists"
        
        # Load and validate environment variables
        local required_vars=("POSTGRES_PASSWORD" "JWT_SECRET_KEY" "ASPNETCORE_ENVIRONMENT")
        local missing_vars=()
        
        for var in "${required_vars[@]}"; do
            if ! grep -q "^$var=" "$ENV_FILE" 2>/dev/null; then
                missing_vars+=("$var")
            fi
        done
        
        if [[ ${#missing_vars[@]} -gt 0 ]]; then
            write_health_status "Environment Variables" "WARNING" "Missing required variables" "${missing_vars[*]}"
            RECOMMENDATIONS+=("Add missing environment variables: ${missing_vars[*]}")
        else
            write_health_status "Environment Variables" "HEALTHY" "All required variables present"
        fi
    else
        write_health_status "Environment File" "ERROR" ".env file not found"
        RECOMMENDATIONS+=("Run ./scripts/unix/config-manager.sh to create configuration")
    fi
    
    # Check project files
    local project_file="$PROJECT_ROOT/Presentations/WebApi/WebApi.csproj"
    if [[ -f "$project_file" ]]; then
        write_health_status "Project Files" "HEALTHY" "WebApi project found"
    else
        write_health_status "Project Files" "ERROR" "WebApi project not found"
    fi
}

show_header() {
    if [[ "$JSON" == "true" ]]; then
        return
    fi
    
    clear
    echo ""
    write_color_text "    🏥🏥🏥 NOTIFY SERVICE API - HEALTH CHECK 🏥🏥🏥" "$CYAN"
    echo ""
    write_color_text "    🔍 Comprehensive System Validation" "$YELLOW"
    write_color_text "    📊 Services • Dependencies • Configuration" "$GRAY"
    echo ""
    write_color_text "═══════════════════════════════════════════════════════════════════════════════" "$GRAY"
}

show_summary() {
    if [[ "$JSON" == "true" ]]; then
        # Output JSON format
        echo "{"
        echo "  \"overall\": \"$([ $ERROR_COUNT -eq 0 ] && [ $WARNING_COUNT -eq 0 ] && echo "HEALTHY" || [ $ERROR_COUNT -eq 0 ] && echo "WARNING" || echo "ERROR")\","
        echo "  \"healthy\": $HEALTHY_COUNT,"
        echo "  \"warnings\": $WARNING_COUNT,"
        echo "  \"errors\": $ERROR_COUNT,"
        echo "  \"recommendations\": ["
        for i in "${!RECOMMENDATIONS[@]}"; do
            echo "    \"${RECOMMENDATIONS[$i]}\"$([ $i -lt $((${#RECOMMENDATIONS[@]} - 1)) ] && echo ",")"
        done
        echo "  ]"
        echo "}"
        return
    fi
    
    echo ""
    write_color_text "═══════════════════════════════════════════════════════════════════════════════" "$GRAY"
    write_color_text "📊 Health Check Summary" "$WHITE"
    echo ""
    
    write_color_text "✅ Healthy: $HEALTHY_COUNT" "$GREEN"
    write_color_text "⚠️ Warnings: $WARNING_COUNT" "$YELLOW"
    write_color_text "❌ Errors: $ERROR_COUNT" "$RED"
    
    if [[ ${#RECOMMENDATIONS[@]} -gt 0 ]]; then
        echo ""
        write_color_text "💡 Recommendations:" "$YELLOW"
        for rec in "${RECOMMENDATIONS[@]}"; do
            write_color_text "   • $rec" "$GRAY"
        done
    fi
    
    echo ""
    if [[ $ERROR_COUNT -eq 0 && $WARNING_COUNT -eq 0 ]]; then
        write_color_text "🎉 All systems are healthy! Ready to develop." "$GREEN"
    elif [[ $ERROR_COUNT -eq 0 ]]; then
        write_color_text "⚠️ System is mostly healthy with minor warnings." "$YELLOW"
    else
        write_color_text "❌ Critical issues detected. Please address errors before proceeding." "$RED"
    fi
    echo ""
}

# Main execution
show_header

# Check dependencies
write_color_text "🔍 Dependency Health Check" "$CYAN"

# Determine install commands based on OS
dotnet_install=""
docker_install=""
git_install=""

if [[ "$OSTYPE" == "darwin"* ]]; then
    dotnet_install="brew install --cask dotnet"
    docker_install="brew install --cask docker"
    git_install="brew install git"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    if command -v apt-get &> /dev/null; then
        dotnet_install="wget https://dot.net/v1/dotnet-install.sh -O dotnet-install.sh && chmod +x dotnet-install.sh && ./dotnet-install.sh --channel 8.0"
        docker_install="sudo apt-get update && sudo apt-get install -y docker.io"
        git_install="sudo apt-get update && sudo apt-get install -y git"
    elif command -v yum &> /dev/null; then
        dotnet_install="wget https://dot.net/v1/dotnet-install.sh -O dotnet-install.sh && chmod +x dotnet-install.sh && ./dotnet-install.sh --channel 8.0"
        docker_install="sudo yum install -y docker"
        git_install="sudo yum install -y git"
    fi
fi

test_dependency_health ".NET SDK" "dotnet --version" "$dotnet_install"
test_dependency_health "Docker" "docker --version" "$docker_install"
test_dependency_health "Git" "git --version" "$git_install"

# Check services
write_color_text "" 
write_color_text "🚀 Service Health Check" "$CYAN"

# Check if using Docker or local services
use_docker=false
if docker info &> /dev/null; then
    use_docker=true
    # Check Docker containers
    test_service_health "PostgreSQL (Docker)" "docker exec notify-postgres psql -U postgres -c 'SELECT 1;'" "" "docker start notify-postgres"
    test_service_health "Redis (Docker)" "docker exec notify-redis redis-cli ping" "PONG" "docker start notify-redis"
else
    # Check local services
    test_service_health "PostgreSQL (Local)" "psql -U postgres -c 'SELECT 1;'" "" ""
    test_service_health "Redis (Local)" "redis-cli ping" "PONG" ""
fi

# Check configuration
test_configuration_health

# Show summary
show_summary
