#!/bin/bash

# ═══════════════════════════════════════════════════════════════════════════════
# 🏥 Notify Service API - Comprehensive Health Check (Unix/Linux/macOS)
# ═══════════════════════════════════════════════════════════════════════════════
# Validates all services and dependencies for the development environment

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
ENV_FILE="$PROJECT_ROOT/.env"

# Default options
DETAILED=false
FIX=false
QUIET=false
JSON=false

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
GRAY='\033[0;37m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Health check results
declare -A HEALTH_RESULTS
HEALTHY_COUNT=0
WARNING_COUNT=0
ERROR_COUNT=0
RECOMMENDATIONS=()

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --detailed)
            DETAILED=true
            shift
            ;;
        --fix)
            FIX=true
            shift
            ;;
        --quiet)
            QUIET=true
            shift
            ;;
        --json)
            JSON=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "OPTIONS:"
            echo "  --detailed    Show detailed information"
            echo "  --fix         Attempt to fix issues automatically"
            echo "  --quiet       Minimal output"
            echo "  --json        Output results in JSON format"
            echo "  --help        Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

write_color_text() {
    local text=$1
    local color=${2:-$WHITE}
    if [[ "$QUIET" != "true" && "$JSON" != "true" ]]; then
        echo -e "${color}${text}${NC}"
    fi
}

write_health_status() {
    local component=$1
    local status=$2
    local message=${3:-""}
    local details=${4:-""}
    
    local status_icon
    case $status in
        "HEALTHY") status_icon="✅"; ((HEALTHY_COUNT++)) ;;
        "WARNING") status_icon="⚠️"; ((WARNING_COUNT++)) ;;
        "ERROR") status_icon="❌"; ((ERROR_COUNT++)) ;;
        *) status_icon="❓" ;;
    esac
    
    if [[ "$JSON" != "true" ]]; then
        local output="[$status_icon] $component: $status"
        if [[ -n "$message" ]]; then
            output+=" - $message"
        fi
        
        local color
        case $status in
            "HEALTHY") color=$GREEN ;;
            "WARNING") color=$YELLOW ;;
            "ERROR") color=$RED ;;
            *) color=$GRAY ;;
        esac
        
        write_color_text "$output" "$color"
        
        if [[ "$DETAILED" == "true" && -n "$details" ]]; then
            write_color_text "    └─ $details" "$GRAY"
        fi
    fi
    
    # Store result
    HEALTH_RESULTS["$component"]="$status|$message|$details"
}

test_service_health() {
    local service_name=$1
    local test_command=$2
    local expected_output=${3:-""}
    local fix_command=${4:-""}
    
    if eval "$test_command" &> /dev/null; then
        if [[ -n "$expected_output" ]]; then
            local result
            result=$(eval "$test_command" 2>/dev/null)
            if [[ "$result" == *"$expected_output"* ]]; then
                write_health_status "$service_name" "HEALTHY" "Service is running correctly" "$result"
                return 0
            else
                write_health_status "$service_name" "WARNING" "Service responding but unexpected output" "$result"
                return 1
            fi
        else
            write_health_status "$service_name" "HEALTHY" "Service is running correctly"
            return 0
        fi
    else
        write_health_status "$service_name" "ERROR" "Service not responding"
        
        if [[ "$FIX" == "true" && -n "$fix_command" ]]; then
            write_color_text "    🔧 Attempting to fix $service_name..." "$YELLOW"
            if eval "$fix_command" &> /dev/null; then
                write_color_text "    ✅ Fix command executed" "$GREEN"
            else
                write_color_text "    ❌ Fix failed" "$RED"
            fi
        fi
        return 1
    fi
}

# Enhanced error tracking for shell scripts
ERROR_LOG=()
INSTALLATION_RESULTS=()

add_error_to_log() {
    local component="$1"
    local error_message="$2"
    local error_code="${3:-UNKNOWN}"
    local suggestion="${4:-}"

    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local error_entry="[$timestamp] [$error_code] $component: $error_message"

    ERROR_LOG+=("$error_entry")
    write_color_text "ERROR [$error_code]: $component - $error_message" "$RED"

    if [[ -n "$suggestion" ]]; then
        write_color_text "    💡 Suggestion: $suggestion" "$YELLOW"
    fi
}

test_command_availability() {
    local command="$1"
    local component_name="$2"

    local cmd_name="${command%% *}"
    if ! command -v "$cmd_name" &> /dev/null; then
        add_error_to_log "$component_name" "Command '$command' not found in PATH" "CMD_NOT_FOUND" "Ensure $component_name is installed and added to PATH"
        return 1
    fi
    return 0
}

execute_command_with_error_handling() {
    local command="$1"
    local component_name="$2"
    local success_message="${3:-}"
    local error_message="${4:-}"
    local suppress_output="${5:-true}"

    write_color_text "Executing: $command" "$CYAN"

    local result
    local exit_code

    if [[ "$suppress_output" == "true" ]]; then
        result=$(eval "$command" 2>&1)
        exit_code=$?
    else
        eval "$command"
        exit_code=$?
        result=""
    fi

    if [[ $exit_code -eq 0 ]]; then
        if [[ -n "$success_message" ]]; then
            write_color_text "$success_message" "$GREEN"
        fi
        echo "SUCCESS|$result|$exit_code"
        return 0
    else
        local error_msg="${error_message:-Command failed with exit code $exit_code}"
        add_error_to_log "$component_name" "$error_msg" "CMD_FAILED" "Check the command syntax and dependencies"
        echo "FAILED|$result|$exit_code"
        return 1
    fi
}

test_dependency_health() {
    local dependency_name=$1
    local test_command=$2
    local install_command=${3:-""}
    local required=${4:-true}

    write_color_text "Checking $dependency_name..." "$CYAN"
    INSTALLATION_RESULTS+=("$dependency_name:CHECKING:$required")

    # First check if the command is available
    if ! test_command_availability "$test_command" "$dependency_name"; then
        update_installation_result "$dependency_name" "NOT_FOUND"

        if [[ "$required" == "false" ]]; then
            write_color_text "$dependency_name is not installed (optional)" "$YELLOW"
            update_installation_result "$dependency_name" "OPTIONAL_MISSING"
            return 0
        fi

        # Try to install if auto-install is enabled
        if [[ "$FIX" == "true" && -n "$install_command" ]]; then
            install_dependency_with_retry "$dependency_name" "$install_command" "$test_command"
            return $?
        else
            show_manual_install_instructions "$dependency_name" "$install_command"
            update_installation_result "$dependency_name" "FAILED"
            return 1
        fi
    fi

    # Test the actual command
    local test_result
    test_result=$(execute_command_with_error_handling "$test_command" "$dependency_name" "$dependency_name is installed and accessible")

    if [[ "$test_result" == SUCCESS* ]]; then
        local version="${test_result#SUCCESS|}"
        version="${version%|*}"
        update_installation_result "$dependency_name" "SUCCESS" "$version"
        return 0
    else
        local error_output="${test_result#FAILED|}"
        error_output="${error_output%|*}"
        update_installation_result "$dependency_name" "FAILED" "" "$error_output"

        if [[ "$required" == "true" ]]; then
            add_error_to_log "$dependency_name" "Dependency test failed" "DEP_TEST_FAILED" "Verify $dependency_name installation and configuration"
            return 1
        else
            write_color_text "$dependency_name test failed (optional)" "$YELLOW"
            return 0
        fi
    fi
}

update_installation_result() {
    local component="$1"
    local status="$2"
    local version="${3:-}"
    local error="${4:-}"

    # Remove old entry and add new one
    local new_results=()
    for result in "${INSTALLATION_RESULTS[@]}"; do
        if [[ "${result%%:*}" != "$component" ]]; then
            new_results+=("$result")
        fi
    done
    new_results+=("$component:$status:$version:$error")
    INSTALLATION_RESULTS=("${new_results[@]}")
}

install_dependency_with_retry() {
    local name="$1"
    local install_command="$2"
    local test_command="$3"
    local max_retries=2

    for ((attempt=1; attempt<=max_retries; attempt++)); do
        write_color_text "Attempting to install $name (attempt $attempt/$max_retries)..." "$CYAN"

        local install_result
        install_result=$(execute_command_with_error_handling "$install_command" "$name" "" "Installation failed")

        if [[ "$install_result" == SUCCESS* ]]; then
            # Wait a moment for installation to complete
            sleep 3

            # Test if installation was successful
            local test_result
            test_result=$(execute_command_with_error_handling "$test_command" "$name" "" "" "true")

            if [[ "$test_result" == SUCCESS* ]]; then
                write_color_text "$name installed successfully" "$GREEN"
                update_installation_result "$name" "INSTALLED"
                return 0
            else
                add_error_to_log "$name" "Installation completed but verification failed" "INSTALL_VERIFY_FAILED" "Try manual installation or check system requirements"
            fi
        else
            add_error_to_log "$name" "Installation command failed on attempt $attempt" "INSTALL_CMD_FAILED"
        fi

        if [[ $attempt -lt $max_retries ]]; then
            write_color_text "Retrying installation in 5 seconds..." "$YELLOW"
            sleep 5
        fi
    done

    # All attempts failed
    show_manual_install_instructions "$name" "$install_command"
    update_installation_result "$name" "INSTALL_FAILED"
    return 1
}

show_manual_install_instructions() {
    local name="$1"
    local install_command="$2"

    write_color_text "$name installation required" "$RED"

    if [[ "$FIX" != "true" && -n "$install_command" ]]; then
        write_color_text "    💡 Auto-install option available" "$CYAN"
        write_color_text "    🔄 Run with --fix to install automatically" "$WHITE"
        write_color_text "    📋 Manual command: $install_command" "$WHITE"
    fi
}

test_configuration_health() {
    write_color_text "" 
    write_color_text "🔧 Configuration Health Check" "$CYAN"
    
    # Check .env file
    if [[ -f "$ENV_FILE" ]]; then
        write_health_status "Environment File" "HEALTHY" ".env file exists"
        
        # Load and validate environment variables
        local required_vars=("POSTGRES_PASSWORD" "JWT_SECRET_KEY" "ASPNETCORE_ENVIRONMENT")
        local missing_vars=()
        
        for var in "${required_vars[@]}"; do
            if ! grep -q "^$var=" "$ENV_FILE" 2>/dev/null; then
                missing_vars+=("$var")
            fi
        done
        
        if [[ ${#missing_vars[@]} -gt 0 ]]; then
            write_health_status "Environment Variables" "WARNING" "Missing required variables" "${missing_vars[*]}"
            RECOMMENDATIONS+=("Add missing environment variables: ${missing_vars[*]}")
        else
            write_health_status "Environment Variables" "HEALTHY" "All required variables present"
        fi
    else
        write_health_status "Environment File" "ERROR" ".env file not found"
        RECOMMENDATIONS+=("Run ./scripts/unix/config-manager.sh to create configuration")
    fi
    
    # Check project files
    local project_file="$PROJECT_ROOT/Presentations/WebApi/WebApi.csproj"
    if [[ -f "$project_file" ]]; then
        write_health_status "Project Files" "HEALTHY" "WebApi project found"
    else
        write_health_status "Project Files" "ERROR" "WebApi project not found"
    fi
}

show_header() {
    if [[ "$JSON" == "true" ]]; then
        return
    fi
    
    clear
    echo ""
    write_color_text "    🏥🏥🏥 NOTIFY SERVICE API - HEALTH CHECK 🏥🏥🏥" "$CYAN"
    echo ""
    write_color_text "    🔍 Comprehensive System Validation" "$YELLOW"
    write_color_text "    📊 Services • Dependencies • Configuration" "$GRAY"
    echo ""
    write_color_text "═══════════════════════════════════════════════════════════════════════════════" "$GRAY"
}

show_summary() {
    if [[ "$JSON" == "true" ]]; then
        # Output JSON format
        echo "{"
        echo "  \"overall\": \"$([ $ERROR_COUNT -eq 0 ] && [ $WARNING_COUNT -eq 0 ] && echo "HEALTHY" || [ $ERROR_COUNT -eq 0 ] && echo "WARNING" || echo "ERROR")\","
        echo "  \"healthy\": $HEALTHY_COUNT,"
        echo "  \"warnings\": $WARNING_COUNT,"
        echo "  \"errors\": $ERROR_COUNT,"
        echo "  \"recommendations\": ["
        for i in "${!RECOMMENDATIONS[@]}"; do
            echo "    \"${RECOMMENDATIONS[$i]}\"$([ $i -lt $((${#RECOMMENDATIONS[@]} - 1)) ] && echo ",")"
        done
        echo "  ]"
        echo "}"
        return
    fi
    
    echo ""
    write_color_text "═══════════════════════════════════════════════════════════════════════════════" "$GRAY"
    write_color_text "📊 Health Check Summary" "$WHITE"
    echo ""
    
    write_color_text "✅ Healthy: $HEALTHY_COUNT" "$GREEN"
    write_color_text "⚠️ Warnings: $WARNING_COUNT" "$YELLOW"
    write_color_text "❌ Errors: $ERROR_COUNT" "$RED"
    
    if [[ ${#RECOMMENDATIONS[@]} -gt 0 ]]; then
        echo ""
        write_color_text "💡 Recommendations:" "$YELLOW"
        for rec in "${RECOMMENDATIONS[@]}"; do
            write_color_text "   • $rec" "$GRAY"
        done
    fi
    
    echo ""
    if [[ $ERROR_COUNT -eq 0 && $WARNING_COUNT -eq 0 ]]; then
        write_color_text "🎉 All systems are healthy! Ready to develop." "$GREEN"
    elif [[ $ERROR_COUNT -eq 0 ]]; then
        write_color_text "⚠️ System is mostly healthy with minor warnings." "$YELLOW"
    else
        write_color_text "❌ Critical issues detected. Please address errors before proceeding." "$RED"
    fi
    echo ""
}

# Main execution
show_header

# Check dependencies
write_color_text "🔍 Dependency Health Check" "$CYAN"

# Determine install commands based on OS
dotnet_install=""
docker_install=""
git_install=""

if [[ "$OSTYPE" == "darwin"* ]]; then
    dotnet_install="brew install --cask dotnet"
    docker_install="brew install --cask docker"
    git_install="brew install git"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    if command -v apt-get &> /dev/null; then
        dotnet_install="wget https://dot.net/v1/dotnet-install.sh -O dotnet-install.sh && chmod +x dotnet-install.sh && ./dotnet-install.sh --channel 8.0"
        docker_install="sudo apt-get update && sudo apt-get install -y docker.io"
        git_install="sudo apt-get update && sudo apt-get install -y git"
    elif command -v yum &> /dev/null; then
        dotnet_install="wget https://dot.net/v1/dotnet-install.sh -O dotnet-install.sh && chmod +x dotnet-install.sh && ./dotnet-install.sh --channel 8.0"
        docker_install="sudo yum install -y docker"
        git_install="sudo yum install -y git"
    fi
fi

test_dependency_health ".NET SDK" "dotnet --version" "$dotnet_install"
test_dependency_health "Docker" "docker --version" "$docker_install"
test_dependency_health "Git" "git --version" "$git_install"

# Check services
write_color_text "" 
write_color_text "🚀 Service Health Check" "$CYAN"

# Check if using Docker or local services
use_docker=false
if docker info &> /dev/null; then
    use_docker=true
    # Check Docker containers
    test_service_health "PostgreSQL (Docker)" "docker exec notify-postgres psql -U postgres -c 'SELECT 1;'" "" "docker start notify-postgres"
    test_service_health "Redis (Docker)" "docker exec notify-redis redis-cli ping" "PONG" "docker start notify-redis"
else
    # Check local services
    test_service_health "PostgreSQL (Local)" "psql -U postgres -c 'SELECT 1;'" "" ""
    test_service_health "Redis (Local)" "redis-cli ping" "PONG" ""
fi

# Check configuration
test_configuration_health

# Show summary
show_summary
