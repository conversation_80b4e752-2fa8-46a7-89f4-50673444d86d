{"sliSloConfiguration": {"service": "notify-service-api", "version": "1.0", "description": "Service Level Indicators and Objectives for Notify Service API", "owner": "platform-team", "contact": "<EMAIL>", "documentation": "https://docs.company.com/notify-service/sli-slo", "slis": {"availability": {"name": "API Availability", "description": "Percentage of successful HTTP requests", "type": "availability", "query": {"prometheus": "sum(rate(http_requests_total{service=\"notify-service-api\",code!~\"5..\"}[5m])) / sum(rate(http_requests_total{service=\"notify-service-api\"}[5m]))", "unit": "percentage", "good_events": "http_requests_total{service=\"notify-service-api\",code!~\"5..\"}", "total_events": "http_requests_total{service=\"notify-service-api\"}"}, "thresholds": {"target": 99.9, "warning": 99.5, "critical": 99.0}, "windows": {"short": "5m", "medium": "1h", "long": "24h"}}, "latency": {"name": "API Response Time", "description": "95th percentile response time for API requests", "type": "latency", "query": {"prometheus": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{service=\"notify-service-api\"}[5m])) by (le))", "unit": "seconds", "threshold": 0.5}, "thresholds": {"target": 0.2, "warning": 0.5, "critical": 1.0}, "windows": {"short": "5m", "medium": "1h", "long": "24h"}}, "throughput": {"name": "Request Throughput", "description": "Number of requests per second", "type": "throughput", "query": {"prometheus": "sum(rate(http_requests_total{service=\"notify-service-api\"}[5m]))", "unit": "requests_per_second"}, "thresholds": {"minimum": 10, "target": 100, "maximum": 1000}, "windows": {"short": "5m", "medium": "1h", "long": "24h"}}, "error_rate": {"name": "Error Rate", "description": "Percentage of HTTP 5xx errors", "type": "error_rate", "query": {"prometheus": "sum(rate(http_requests_total{service=\"notify-service-api\",code=~\"5..\"}[5m])) / sum(rate(http_requests_total{service=\"notify-service-api\"}[5m]))", "unit": "percentage", "error_events": "http_requests_total{service=\"notify-service-api\",code=~\"5..\"}", "total_events": "http_requests_total{service=\"notify-service-api\"}"}, "thresholds": {"target": 0.001, "warning": 0.01, "critical": 0.05}, "windows": {"short": "5m", "medium": "1h", "long": "24h"}}, "notification_delivery": {"name": "Notification Delivery Success Rate", "description": "Percentage of successfully delivered notifications", "type": "business_metric", "query": {"prometheus": "sum(rate(notifications_delivered_total{service=\"notify-service-api\",status=\"success\"}[5m])) / sum(rate(notifications_delivered_total{service=\"notify-service-api\"}[5m]))", "unit": "percentage", "good_events": "notifications_delivered_total{service=\"notify-service-api\",status=\"success\"}", "total_events": "notifications_delivered_total{service=\"notify-service-api\"}"}, "thresholds": {"target": 99.5, "warning": 99.0, "critical": 98.0}, "windows": {"short": "5m", "medium": "1h", "long": "24h"}}, "database_availability": {"name": "Database Availability", "description": "Database connection success rate", "type": "dependency", "query": {"prometheus": "sum(rate(database_connections_total{service=\"notify-service-api\",status=\"success\"}[5m])) / sum(rate(database_connections_total{service=\"notify-service-api\"}[5m]))", "unit": "percentage"}, "thresholds": {"target": 99.9, "warning": 99.5, "critical": 99.0}, "windows": {"short": "5m", "medium": "1h", "long": "24h"}}}, "slos": {"monthly": {"period": "30d", "objectives": [{"sli": "availability", "target": 99.9, "error_budget": 0.1, "description": "99.9% availability over 30 days allows for 43.2 minutes of downtime"}, {"sli": "latency", "target": 0.2, "description": "95% of requests should complete within 200ms"}, {"sli": "error_rate", "target": 0.001, "error_budget": 0.1, "description": "Error rate should not exceed 0.1%"}, {"sli": "notification_delivery", "target": 99.5, "error_budget": 0.5, "description": "99.5% of notifications should be delivered successfully"}]}, "weekly": {"period": "7d", "objectives": [{"sli": "availability", "target": 99.95, "error_budget": 0.05, "description": "99.95% availability over 7 days allows for 5.04 minutes of downtime"}, {"sli": "latency", "target": 0.15, "description": "95% of requests should complete within 150ms"}]}, "daily": {"period": "24h", "objectives": [{"sli": "availability", "target": 99.99, "error_budget": 0.01, "description": "99.99% availability over 24 hours allows for 8.64 seconds of downtime"}]}}, "error_budgets": {"calculation": {"method": "time_based", "burn_rate_thresholds": {"fast": {"threshold": 14.4, "window": "1h", "description": "Consuming error budget 14.4x faster than sustainable rate"}, "slow": {"threshold": 6, "window": "6h", "description": "Consuming error budget 6x faster than sustainable rate"}}}, "policies": {"freeze_deployments": {"enabled": true, "threshold": 0.1, "description": "Freeze deployments when error budget drops below 10%"}, "escalation": {"enabled": true, "levels": [{"threshold": 0.5, "action": "alert_team", "description": "Alert on-call team when error budget drops below 50%"}, {"threshold": 0.25, "action": "page_manager", "description": "Page engineering manager when error budget drops below 25%"}, {"threshold": 0.1, "action": "incident_response", "description": "Trigger incident response when error budget drops below 10%"}]}}}, "alerting": {"rules": [{"name": "HighErrorRate", "condition": "error_rate > 0.01", "duration": "5m", "severity": "warning", "description": "Error rate is above 1% for 5 minutes", "runbook": "https://runbooks.company.com/notify-service/high-error-rate"}, {"name": "CriticalErrorRate", "condition": "error_rate > 0.05", "duration": "2m", "severity": "critical", "description": "Error rate is above 5% for 2 minutes", "runbook": "https://runbooks.company.com/notify-service/critical-error-rate"}, {"name": "HighLatency", "condition": "latency > 0.5", "duration": "5m", "severity": "warning", "description": "95th percentile latency is above 500ms for 5 minutes", "runbook": "https://runbooks.company.com/notify-service/high-latency"}, {"name": "ServiceDown", "condition": "availability < 0.99", "duration": "1m", "severity": "critical", "description": "Service availability is below 99% for 1 minute", "runbook": "https://runbooks.company.com/notify-service/service-down"}, {"name": "ErrorBudgetBurnRate", "condition": "error_budget_burn_rate > 14.4", "duration": "2m", "severity": "critical", "description": "Error budget is burning too fast", "runbook": "https://runbooks.company.com/notify-service/error-budget-burn"}, {"name": "NotificationDeliveryFailure", "condition": "notification_delivery < 0.99", "duration": "5m", "severity": "warning", "description": "Notification delivery success rate is below 99%", "runbook": "https://runbooks.company.com/notify-service/delivery-failure"}], "notification_channels": [{"name": "slack-alerts", "type": "slack", "webhook_url": "${SLACK_WEBHOOK_URL}", "channel": "#alerts", "severity_levels": ["warning", "critical"]}, {"name": "pagerduty-critical", "type": "<PERSON><PERSON><PERSON><PERSON>", "integration_key": "${PAGERDUTY_INTEGRATION_KEY}", "severity_levels": ["critical"]}, {"name": "email-team", "type": "email", "recipients": ["<EMAIL>"], "severity_levels": ["warning", "critical"]}]}, "dashboards": {"sli_overview": {"title": "SLI Overview Dashboard", "description": "High-level view of all SLIs", "panels": [{"title": "Availability", "type": "stat", "query": "availability_sli", "thresholds": [99.0, 99.5, 99.9]}, {"title": "Latency (95th percentile)", "type": "stat", "query": "latency_sli", "unit": "seconds", "thresholds": [1.0, 0.5, 0.2]}, {"title": "Error Rate", "type": "stat", "query": "error_rate_sli", "unit": "percentage", "thresholds": [0.05, 0.01, 0.001]}, {"title": "Throughput", "type": "graph", "query": "throughput_sli", "unit": "requests_per_second"}]}, "error_budget": {"title": "Error Budget Dashboard", "description": "Error budget consumption and burn rate", "panels": [{"title": "Error Budget Remaining", "type": "gauge", "query": "error_budget_remaining", "unit": "percentage", "thresholds": [10, 25, 50]}, {"title": "Error Budget Burn Rate", "type": "graph", "query": "error_budget_burn_rate"}, {"title": "Time to Exhaustion", "type": "stat", "query": "error_budget_time_to_exhaustion", "unit": "hours"}]}}, "reporting": {"enabled": true, "schedule": "weekly", "recipients": ["<EMAIL>", "<EMAIL>"], "format": "html", "include_sections": ["sli_summary", "slo_compliance", "error_budget_status", "incidents_impact", "recommendations"]}, "environments": {"production": {"enabled": true, "all_slis": true, "strict_thresholds": true}, "staging": {"enabled": true, "slis": ["availability", "latency", "error_rate"], "relaxed_thresholds": true, "threshold_multiplier": 1.5}, "development": {"enabled": false, "slis": ["availability"], "monitoring_only": true}}}}