# ═══════════════════════════════════════════════════════════════════════════════
# 🗄️ Database Compatibility Check - PostgreSQL Migration Validator
# ═══════════════════════════════════════════════════════════════════════════════
# Comprehensive validation script to detect and report database compatibility issues

param(
    [switch]$Fix,              # Attempt to fix issues automatically
    [switch]$Detailed,         # Show detailed analysis
    [switch]$Json,             # Output results in JSON format
    [string]$Provider = "PostgreSQL"  # Database provider to check against
)

# Enhanced error tracking
$Global:CompatibilityErrors = @()
$Global:CompatibilityWarnings = @()
$Global:FixedIssues = @()

function Write-CompatibilityLog {
    param(
        [string]$Message,
        [string]$Level = "INFO",
        [string]$Component = "General"
    )
    
    $timestamp = Get-Date -Format "HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARNING" { "Yellow" }
        "SUCCESS" { "Green" }
        "INFO" { "Cyan" }
        default { "White" }
    }
    
    Write-Host "[$timestamp] " -NoNewline -ForegroundColor Gray
    Write-Host "[$Level] " -NoNewline -ForegroundColor $color
    Write-Host "$Component - $Message" -ForegroundColor $color
}

function Add-CompatibilityIssue {
    param(
        [string]$Type,
        [string]$File,
        [int]$Line,
        [string]$Issue,
        [string]$Suggestion,
        [string]$Severity = "ERROR"
    )
    
    $issueEntry = @{
        Type = $Type
        File = $File
        Line = $Line
        Issue = $Issue
        Suggestion = $Suggestion
        Severity = $Severity
        Timestamp = Get-Date
    }
    
    if ($Severity -eq "ERROR") {
        $Global:CompatibilityErrors += $issueEntry
    } else {
        $Global:CompatibilityWarnings += $issueEntry
    }
    
    Write-CompatibilityLog "$File:$Line - $Issue" $Severity "DB_COMPAT"
    if ($Detailed) {
        Write-Host "    💡 Suggestion: $Suggestion" -ForegroundColor Yellow
    }
}

function Test-SqlServerSyntax {
    param([string]$FilePath)
    
    Write-CompatibilityLog "Checking SQL Server syntax in: $FilePath" "INFO" "SYNTAX_CHECK"
    
    $content = Get-Content $FilePath -Raw
    $lineNumber = 0
    
    Get-Content $FilePath | ForEach-Object {
        $lineNumber++
        $line = $_
        
        # Check for SQL Server specific syntax
        if ($line -match "SqlServer:Identity|SqlServer:ValueGenerationStrategy") {
            Add-CompatibilityIssue -Type "SQL_SERVER_SYNTAX" -File $FilePath -Line $lineNumber -Issue "SQL Server specific annotation found" -Suggestion "Remove SQL Server annotations for PostgreSQL compatibility"
        }
        
        if ($line -match "datetime2|nvarchar|bit\b") {
            Add-CompatibilityIssue -Type "SQL_SERVER_TYPES" -File $FilePath -Line $lineNumber -Issue "SQL Server data type found" -Suggestion "Use PostgreSQL compatible types (timestamp, varchar, boolean)"
        }
        
        if ($line -match "GetUtcDate\(\)") {
            Add-CompatibilityIssue -Type "SQL_SERVER_FUNCTION" -File $FilePath -Line $lineNumber -Issue "SQL Server function GetUtcDate() found" -Suggestion "Replace with NOW() for PostgreSQL"
        }
        
        if ($line -match "DateTime\.Now") {
            Add-CompatibilityIssue -Type "LOCAL_DATETIME" -File $FilePath -Line $lineNumber -Issue "DateTime.Now creates local time" -Suggestion "Use DateTime.UtcNow for PostgreSQL compatibility"
        }
        
        if ($line -match "HasColumnType\(`"DateTime`"\)") {
            Add-CompatibilityIssue -Type "DATETIME_TYPE" -File $FilePath -Line $lineNumber -Issue "DateTime column type not PostgreSQL compatible" -Suggestion "Use 'timestamp with time zone' for PostgreSQL"
        }
    }
}

function Test-EntityFrameworkConfiguration {
    Write-CompatibilityLog "Checking Entity Framework configurations..." "INFO" "EF_CONFIG"
    
    # Check mapping files
    $mappingFiles = Get-ChildItem -Path "Libraries\Data\Mapping\*.cs" -Recurse -ErrorAction SilentlyContinue
    foreach ($file in $mappingFiles) {
        Test-SqlServerSyntax $file.FullName
    }
    
    # Check context files
    $contextFiles = Get-ChildItem -Path "Libraries\*\Contexts\*.cs" -Recurse -ErrorAction SilentlyContinue
    foreach ($file in $contextFiles) {
        Test-SqlServerSyntax $file.FullName
    }
    
    # Check migration files
    $migrationFiles = Get-ChildItem -Path "Libraries\*\Migrations\*.cs" -Recurse -ErrorAction SilentlyContinue
    foreach ($file in $migrationFiles) {
        Test-SqlServerSyntax $file.FullName
    }
    
    # Check seed files
    $seedFiles = Get-ChildItem -Path "Libraries\*\Seeds\*.cs" -Recurse -ErrorAction SilentlyContinue
    foreach ($file in $seedFiles) {
        Test-SqlServerSyntax $file.FullName
    }
}

function Test-ConnectionStrings {
    Write-CompatibilityLog "Checking connection strings..." "INFO" "CONNECTION"
    
    $configFiles = @(
        "appsettings.json",
        "appsettings.Development.json",
        "appsettings.Production.json"
    )
    
    foreach ($configFile in $configFiles) {
        if (Test-Path $configFile) {
            $config = Get-Content $configFile | ConvertFrom-Json -ErrorAction SilentlyContinue
            
            if ($config.ConnectionStrings) {
                foreach ($connName in $config.ConnectionStrings.PSObject.Properties.Name) {
                    $connString = $config.ConnectionStrings.$connName
                    
                    if ($connString -match "Server=|Data Source=|Initial Catalog=") {
                        Add-CompatibilityIssue -Type "SQL_SERVER_CONNECTION" -File $configFile -Line 0 -Issue "SQL Server connection string format detected" -Suggestion "Use PostgreSQL connection string format (Host=, Database=, Username=, Password=)"
                    }
                    
                    if ($connString -notmatch "Host=|Server=" -and $connString -match "=") {
                        Add-CompatibilityIssue -Type "INVALID_CONNECTION" -File $configFile -Line 0 -Issue "Invalid connection string format" -Suggestion "Ensure connection string follows PostgreSQL format" -Severity "WARNING"
                    }
                }
            }
        }
    }
}

function Fix-CommonIssues {
    if (-not $Fix) { return }
    
    Write-CompatibilityLog "Attempting to fix common compatibility issues..." "INFO" "AUTO_FIX"
    
    foreach ($error in $Global:CompatibilityErrors) {
        switch ($error.Type) {
            "LOCAL_DATETIME" {
                if ($error.File -and (Test-Path $error.File)) {
                    $content = Get-Content $error.File -Raw
                    $newContent = $content -replace "DateTime\.Now", "DateTime.UtcNow"
                    
                    if ($content -ne $newContent) {
                        Set-Content -Path $error.File -Value $newContent -Encoding UTF8
                        $Global:FixedIssues += "Fixed DateTime.Now in $($error.File)"
                        Write-CompatibilityLog "Fixed DateTime.Now usage in $($error.File)" "SUCCESS" "AUTO_FIX"
                    }
                }
            }
            "DATETIME_TYPE" {
                if ($error.File -and (Test-Path $error.File)) {
                    $content = Get-Content $error.File -Raw
                    $newContent = $content -replace 'HasColumnType\("DateTime"\)', 'HasColumnType("timestamp with time zone")'
                    
                    if ($content -ne $newContent) {
                        Set-Content -Path $error.File -Value $newContent -Encoding UTF8
                        $Global:FixedIssues += "Fixed DateTime column type in $($error.File)"
                        Write-CompatibilityLog "Fixed DateTime column type in $($error.File)" "SUCCESS" "AUTO_FIX"
                    }
                }
            }
            "SQL_SERVER_FUNCTION" {
                if ($error.File -and (Test-Path $error.File)) {
                    $content = Get-Content $error.File -Raw
                    $newContent = $content -replace "GetUtcDate\(\)", "NOW()"
                    
                    if ($content -ne $newContent) {
                        Set-Content -Path $error.File -Value $newContent -Encoding UTF8
                        $Global:FixedIssues += "Fixed GetUtcDate() function in $($error.File)"
                        Write-CompatibilityLog "Fixed GetUtcDate() function in $($error.File)" "SUCCESS" "AUTO_FIX"
                    }
                }
            }
        }
    }
}

function Show-CompatibilityReport {
    Write-Host ""
    Write-Host "═══════════════════════════════════════════════════════════════════════════════" -ForegroundColor DarkGray
    Write-Host "🗄️ DATABASE COMPATIBILITY REPORT" -ForegroundColor Yellow
    Write-Host "═══════════════════════════════════════════════════════════════════════════════" -ForegroundColor DarkGray
    
    Write-Host ""
    Write-Host "📊 Summary:" -ForegroundColor White
    Write-Host "    ❌ Errors: $($Global:CompatibilityErrors.Count)" -ForegroundColor Red
    Write-Host "    ⚠️  Warnings: $($Global:CompatibilityWarnings.Count)" -ForegroundColor Yellow
    Write-Host "    ✅ Fixed: $($Global:FixedIssues.Count)" -ForegroundColor Green
    
    if ($Global:CompatibilityErrors.Count -gt 0) {
        Write-Host ""
        Write-Host "🚨 Critical Issues:" -ForegroundColor Red
        
        $errorGroups = $Global:CompatibilityErrors | Group-Object -Property Type
        foreach ($group in $errorGroups) {
            Write-Host "    [$($group.Name)] - $($group.Count) occurrence(s)" -ForegroundColor Red
            
            if ($Detailed) {
                foreach ($error in $group.Group | Select-Object -First 3) {
                    Write-Host "      • $($error.File):$($error.Line) - $($error.Issue)" -ForegroundColor Gray
                    Write-Host "        💡 $($error.Suggestion)" -ForegroundColor Yellow
                }
                if ($group.Count -gt 3) {
                    Write-Host "      ... and $($group.Count - 3) more" -ForegroundColor Gray
                }
            }
        }
    }
    
    if ($Global:CompatibilityWarnings.Count -gt 0) {
        Write-Host ""
        Write-Host "⚠️  Warnings:" -ForegroundColor Yellow
        
        $warningGroups = $Global:CompatibilityWarnings | Group-Object -Property Type
        foreach ($group in $warningGroups) {
            Write-Host "    [$($group.Name)] - $($group.Count) occurrence(s)" -ForegroundColor Yellow
        }
    }
    
    if ($Global:FixedIssues.Count -gt 0) {
        Write-Host ""
        Write-Host "✅ Auto-Fixed Issues:" -ForegroundColor Green
        foreach ($fix in $Global:FixedIssues) {
            Write-Host "    • $fix" -ForegroundColor Green
        }
    }
    
    # Recommendations
    Write-Host ""
    Write-Host "💡 Recommendations:" -ForegroundColor White
    
    if ($Global:CompatibilityErrors.Count -gt 0) {
        Write-Host "    ├─ Run with -Fix to automatically resolve common issues" -ForegroundColor Cyan
        Write-Host "    ├─ Remove existing migrations and create new PostgreSQL-compatible ones" -ForegroundColor Cyan
        Write-Host "    ├─ Update connection strings to use PostgreSQL format" -ForegroundColor Cyan
        Write-Host "    └─ Test database operations after fixes" -ForegroundColor Cyan
    } else {
        Write-Host "    🎉 No critical compatibility issues found!" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "═══════════════════════════════════════════════════════════════════════════════" -ForegroundColor DarkGray
    
    # Save detailed report
    if ($Global:CompatibilityErrors.Count -gt 0 -or $Global:CompatibilityWarnings.Count -gt 0) {
        $reportPath = "database-compatibility-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
        $report = @{
            Timestamp = Get-Date
            Provider = $Provider
            Errors = $Global:CompatibilityErrors
            Warnings = $Global:CompatibilityWarnings
            FixedIssues = $Global:FixedIssues
        }
        
        $report | ConvertTo-Json -Depth 3 | Set-Content -Path $reportPath -Encoding UTF8
        Write-Host "📄 Detailed report saved to: $reportPath" -ForegroundColor Cyan
    }
    
    # Return exit code
    if ($Global:CompatibilityErrors.Count -gt 0) {
        exit 1
    } else {
        exit 0
    }
}

# Main execution
Write-Host "🗄️ Database Compatibility Check for $Provider" -ForegroundColor Yellow
Write-Host "═══════════════════════════════════════════════════════════════════════════════" -ForegroundColor DarkGray

Test-EntityFrameworkConfiguration
Test-ConnectionStrings
Fix-CommonIssues

if ($Json) {
    $result = @{
        Provider = $Provider
        Errors = $Global:CompatibilityErrors
        Warnings = $Global:CompatibilityWarnings
        FixedIssues = $Global:FixedIssues
        Summary = @{
            ErrorCount = $Global:CompatibilityErrors.Count
            WarningCount = $Global:CompatibilityWarnings.Count
            FixedCount = $Global:FixedIssues.Count
        }
    }
    $result | ConvertTo-Json -Depth 3
} else {
    Show-CompatibilityReport
}
