#!/usr/bin/env python3
"""
Canary Deployment Monitoring Script
Monitors canary deployments and triggers rollback if thresholds are exceeded
"""

import json
import sys
import time
import argparse
import requests
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import statistics

@dataclass
class CanaryMetrics:
    """Canary deployment metrics"""
    timestamp: datetime
    error_rate: float
    response_time: float
    throughput: float
    cpu_usage: float
    memory_usage: float
    active_connections: int

@dataclass
class CanaryThresholds:
    """Canary deployment thresholds"""
    error_rate_threshold: float
    response_time_threshold: float
    cpu_threshold: float
    memory_threshold: float
    min_throughput: float

class CanaryMonitor:
    """Monitors canary deployment health and performance"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.metrics_history: List[CanaryMetrics] = []
        self.baseline_metrics: Optional[CanaryMetrics] = None
        self.logger = self._setup_logging()
        
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('canary-monitor.log')
            ]
        )
        return logging.getLogger(__name__)
    
    def collect_metrics(self, environment: str = "canary") -> CanaryMetrics:
        """Collect current metrics from the canary environment"""
        try:
            # Health check endpoint
            health_url = f"{self.config['base_url']}/health"
            metrics_url = f"{self.config['base_url']}/metrics"
            
            # Collect application metrics
            health_response = requests.get(health_url, timeout=10)
            metrics_response = requests.get(metrics_url, timeout=10)
            
            # Parse metrics (assuming Prometheus format)
            metrics_data = self._parse_prometheus_metrics(metrics_response.text)
            
            # Calculate error rate from recent requests
            error_rate = self._calculate_error_rate()
            
            # Get response time metrics
            response_time = self._get_response_time_percentile(95)
            
            # Collect system metrics
            cpu_usage = metrics_data.get('cpu_usage_percent', 0)
            memory_usage = metrics_data.get('memory_usage_percent', 0)
            throughput = metrics_data.get('requests_per_second', 0)
            active_connections = metrics_data.get('active_connections', 0)
            
            metrics = CanaryMetrics(
                timestamp=datetime.now(),
                error_rate=error_rate,
                response_time=response_time,
                throughput=throughput,
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                active_connections=active_connections
            )
            
            self.metrics_history.append(metrics)
            self.logger.info(f"Collected metrics: Error Rate: {error_rate:.2%}, "
                           f"Response Time: {response_time:.2f}ms, "
                           f"Throughput: {throughput:.2f} req/s")
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Failed to collect metrics: {e}")
            raise
    
    def _parse_prometheus_metrics(self, metrics_text: str) -> Dict[str, float]:
        """Parse Prometheus metrics format"""
        metrics = {}
        for line in metrics_text.split('\n'):
            if line.startswith('#') or not line.strip():
                continue
            
            try:
                parts = line.split(' ')
                if len(parts) >= 2:
                    metric_name = parts[0]
                    metric_value = float(parts[1])
                    metrics[metric_name] = metric_value
            except (ValueError, IndexError):
                continue
        
        return metrics
    
    def _calculate_error_rate(self) -> float:
        """Calculate error rate from application logs or metrics"""
        try:
            # Query application metrics for error rate
            # This would typically query your monitoring system (Prometheus, etc.)
            
            # Simulated error rate calculation
            # In real implementation, query your metrics backend
            total_requests = 1000  # Get from metrics
            error_requests = 5     # Get from metrics
            
            return error_requests / total_requests if total_requests > 0 else 0
            
        except Exception as e:
            self.logger.warning(f"Could not calculate error rate: {e}")
            return 0
    
    def _get_response_time_percentile(self, percentile: int) -> float:
        """Get response time percentile from metrics"""
        try:
            # Query response time metrics
            # This would typically query your monitoring system
            
            # Simulated response time calculation
            # In real implementation, query your metrics backend
            response_times = [100, 150, 200, 250, 300]  # Get from metrics
            
            if response_times:
                sorted_times = sorted(response_times)
                index = int((percentile / 100) * len(sorted_times))
                return sorted_times[min(index, len(sorted_times) - 1)]
            
            return 0
            
        except Exception as e:
            self.logger.warning(f"Could not get response time percentile: {e}")
            return 0
    
    def validate_thresholds(self, metrics: CanaryMetrics, thresholds: CanaryThresholds) -> bool:
        """Validate metrics against thresholds"""
        violations = []
        
        # Check error rate
        if metrics.error_rate > thresholds.error_rate_threshold:
            violations.append(f"Error rate {metrics.error_rate:.2%} exceeds threshold {thresholds.error_rate_threshold:.2%}")
        
        # Check response time
        if metrics.response_time > thresholds.response_time_threshold:
            violations.append(f"Response time {metrics.response_time:.2f}ms exceeds threshold {thresholds.response_time_threshold}ms")
        
        # Check CPU usage
        if metrics.cpu_usage > thresholds.cpu_threshold:
            violations.append(f"CPU usage {metrics.cpu_usage:.1f}% exceeds threshold {thresholds.cpu_threshold}%")
        
        # Check memory usage
        if metrics.memory_usage > thresholds.memory_threshold:
            violations.append(f"Memory usage {metrics.memory_usage:.1f}% exceeds threshold {thresholds.memory_threshold}%")
        
        # Check minimum throughput
        if metrics.throughput < thresholds.min_throughput:
            violations.append(f"Throughput {metrics.throughput:.2f} req/s below minimum {thresholds.min_throughput} req/s")
        
        if violations:
            self.logger.error("Threshold violations detected:")
            for violation in violations:
                self.logger.error(f"  - {violation}")
            return False
        
        self.logger.info("All thresholds passed")
        return True
    
    def compare_with_baseline(self, current_metrics: CanaryMetrics) -> bool:
        """Compare current metrics with baseline (production)"""
        if not self.baseline_metrics:
            self.logger.warning("No baseline metrics available for comparison")
            return True
        
        baseline = self.baseline_metrics
        
        # Calculate percentage changes
        error_rate_change = ((current_metrics.error_rate - baseline.error_rate) / baseline.error_rate * 100) if baseline.error_rate > 0 else 0
        response_time_change = ((current_metrics.response_time - baseline.response_time) / baseline.response_time * 100) if baseline.response_time > 0 else 0
        
        # Define acceptable regression thresholds
        max_error_rate_increase = 50  # 50% increase in error rate
        max_response_time_increase = 20  # 20% increase in response time
        
        regressions = []
        
        if error_rate_change > max_error_rate_increase:
            regressions.append(f"Error rate increased by {error_rate_change:.1f}% compared to baseline")
        
        if response_time_change > max_response_time_increase:
            regressions.append(f"Response time increased by {response_time_change:.1f}% compared to baseline")
        
        if regressions:
            self.logger.error("Performance regressions detected:")
            for regression in regressions:
                self.logger.error(f"  - {regression}")
            return False
        
        self.logger.info("No significant regressions compared to baseline")
        return True
    
    def analyze_trends(self, window_minutes: int = 5) -> bool:
        """Analyze metric trends over time window"""
        if len(self.metrics_history) < 3:
            self.logger.info("Insufficient data for trend analysis")
            return True
        
        # Get metrics from the last window
        cutoff_time = datetime.now() - timedelta(minutes=window_minutes)
        recent_metrics = [m for m in self.metrics_history if m.timestamp >= cutoff_time]
        
        if len(recent_metrics) < 2:
            return True
        
        # Analyze error rate trend
        error_rates = [m.error_rate for m in recent_metrics]
        response_times = [m.response_time for m in recent_metrics]
        
        # Check if error rate is consistently increasing
        if len(error_rates) >= 3:
            if all(error_rates[i] <= error_rates[i+1] for i in range(len(error_rates)-1)):
                if error_rates[-1] > error_rates[0] * 1.5:  # 50% increase
                    self.logger.error("Error rate is consistently increasing")
                    return False
        
        # Check if response time is consistently increasing
        if len(response_times) >= 3:
            if all(response_times[i] <= response_times[i+1] for i in range(len(response_times)-1)):
                if response_times[-1] > response_times[0] * 1.3:  # 30% increase
                    self.logger.error("Response time is consistently increasing")
                    return False
        
        self.logger.info("Metric trends are stable")
        return True
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate monitoring report"""
        if not self.metrics_history:
            return {"status": "no_data", "message": "No metrics collected"}
        
        latest_metrics = self.metrics_history[-1]
        
        # Calculate averages over monitoring period
        avg_error_rate = statistics.mean([m.error_rate for m in self.metrics_history])
        avg_response_time = statistics.mean([m.response_time for m in self.metrics_history])
        avg_throughput = statistics.mean([m.throughput for m in self.metrics_history])
        
        report = {
            "monitoring_duration": len(self.metrics_history),
            "latest_metrics": {
                "timestamp": latest_metrics.timestamp.isoformat(),
                "error_rate": latest_metrics.error_rate,
                "response_time": latest_metrics.response_time,
                "throughput": latest_metrics.throughput,
                "cpu_usage": latest_metrics.cpu_usage,
                "memory_usage": latest_metrics.memory_usage
            },
            "averages": {
                "error_rate": avg_error_rate,
                "response_time": avg_response_time,
                "throughput": avg_throughput
            },
            "total_samples": len(self.metrics_history)
        }
        
        return report
    
    def save_metrics(self, filename: str):
        """Save collected metrics to file"""
        metrics_data = {
            "monitoring_session": {
                "start_time": self.metrics_history[0].timestamp.isoformat() if self.metrics_history else None,
                "end_time": self.metrics_history[-1].timestamp.isoformat() if self.metrics_history else None,
                "total_samples": len(self.metrics_history)
            },
            "metrics": [
                {
                    "timestamp": m.timestamp.isoformat(),
                    "error_rate": m.error_rate,
                    "response_time": m.response_time,
                    "throughput": m.throughput,
                    "cpu_usage": m.cpu_usage,
                    "memory_usage": m.memory_usage,
                    "active_connections": m.active_connections
                }
                for m in self.metrics_history
            ]
        }
        
        with open(filename, 'w') as f:
            json.dump(metrics_data, f, indent=2)
        
        self.logger.info(f"Metrics saved to {filename}")

def main():
    parser = argparse.ArgumentParser(description="Monitor canary deployment")
    parser.add_argument("--duration", default="30m", help="Monitoring duration (e.g., 30m, 1h)")
    parser.add_argument("--interval", type=int, default=30, help="Monitoring interval in seconds")
    parser.add_argument("--error-threshold", type=float, default=0.05, help="Error rate threshold (0.05 = 5%)")
    parser.add_argument("--response-time-threshold", type=float, default=1000, help="Response time threshold in ms")
    parser.add_argument("--base-url", default="http://localhost", help="Base URL for monitoring")
    parser.add_argument("--output", help="Output file for metrics")
    parser.add_argument("--baseline-file", help="Baseline metrics file for comparison")
    
    args = parser.parse_args()
    
    # Parse duration
    duration_str = args.duration.lower()
    if duration_str.endswith('m'):
        duration_seconds = int(duration_str[:-1]) * 60
    elif duration_str.endswith('h'):
        duration_seconds = int(duration_str[:-1]) * 3600
    else:
        duration_seconds = int(duration_str)
    
    # Setup configuration
    config = {
        "base_url": args.base_url,
        "monitoring_interval": args.interval,
        "duration": duration_seconds
    }
    
    # Setup thresholds
    thresholds = CanaryThresholds(
        error_rate_threshold=args.error_threshold,
        response_time_threshold=args.response_time_threshold,
        cpu_threshold=80.0,
        memory_threshold=80.0,
        min_throughput=10.0
    )
    
    # Initialize monitor
    monitor = CanaryMonitor(config)
    
    # Load baseline if provided
    if args.baseline_file:
        try:
            with open(args.baseline_file, 'r') as f:
                baseline_data = json.load(f)
                # Load the most recent baseline metrics
                if baseline_data.get('metrics'):
                    latest_baseline = baseline_data['metrics'][-1]
                    monitor.baseline_metrics = CanaryMetrics(
                        timestamp=datetime.fromisoformat(latest_baseline['timestamp']),
                        error_rate=latest_baseline['error_rate'],
                        response_time=latest_baseline['response_time'],
                        throughput=latest_baseline['throughput'],
                        cpu_usage=latest_baseline['cpu_usage'],
                        memory_usage=latest_baseline['memory_usage'],
                        active_connections=latest_baseline['active_connections']
                    )
                    print(f"✅ Loaded baseline metrics from {args.baseline_file}")
        except Exception as e:
            print(f"⚠️ Could not load baseline metrics: {e}")
    
    print(f"🔍 Starting canary monitoring for {args.duration}")
    print(f"📊 Monitoring interval: {args.interval} seconds")
    print(f"🎯 Error rate threshold: {args.error_threshold:.1%}")
    print(f"⏱️ Response time threshold: {args.response_time_threshold}ms")
    
    start_time = datetime.now()
    end_time = start_time + timedelta(seconds=duration_seconds)
    
    try:
        while datetime.now() < end_time:
            # Collect metrics
            current_metrics = monitor.collect_metrics()
            
            # Validate thresholds
            if not monitor.validate_thresholds(current_metrics, thresholds):
                print("❌ Threshold validation failed - triggering rollback")
                sys.exit(1)
            
            # Compare with baseline
            if not monitor.compare_with_baseline(current_metrics):
                print("❌ Baseline comparison failed - triggering rollback")
                sys.exit(1)
            
            # Analyze trends
            if not monitor.analyze_trends():
                print("❌ Trend analysis failed - triggering rollback")
                sys.exit(1)
            
            # Wait for next interval
            time.sleep(args.interval)
        
        print("✅ Canary monitoring completed successfully")
        
        # Generate final report
        report = monitor.generate_report()
        print(f"📊 Final Report:")
        print(f"  - Total samples: {report['total_samples']}")
        print(f"  - Average error rate: {report['averages']['error_rate']:.2%}")
        print(f"  - Average response time: {report['averages']['response_time']:.2f}ms")
        print(f"  - Average throughput: {report['averages']['throughput']:.2f} req/s")
        
        # Save metrics if requested
        if args.output:
            monitor.save_metrics(args.output)
        
        sys.exit(0)
        
    except KeyboardInterrupt:
        print("\n⚠️ Monitoring interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Monitoring failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
