# 🌍 Cross-Platform Development Guide

This guide covers setting up and running the Notify Service API across different operating systems with our enhanced development tools.

## 📋 Table of Contents

- [Platform Support](#platform-support)
- [Universal Launcher](#universal-launcher)
- [Platform-Specific Tools](#platform-specific-tools)
- [Dependency Management](#dependency-management)
- [Configuration Management](#configuration-management)
- [Troubleshooting](#troubleshooting)

## 🖥️ Platform Support

### **Supported Platforms**

| Platform | Status | Scripts | Package Manager | Notes |
|----------|--------|---------|-----------------|-------|
| **Windows 10/11** | ✅ Full Support | PowerShell | winget, choco | Primary development platform |
| **Linux (Ubuntu/Debian)** | ✅ Full Support | Bash | apt-get | Tested on Ubuntu 20.04+ |
| **Linux (CentOS/RHEL)** | ✅ Full Support | Bash | yum/dnf | Tested on CentOS 8+ |
| **macOS** | ✅ Full Support | Bash | brew | Tested on macOS 12+ |
| **Windows WSL** | ✅ Full Support | Bash | apt-get | Use Linux scripts |

### **Required Dependencies**

| Dependency | Windows | Linux | macOS | Auto-Install |
|------------|---------|-------|-------|--------------|
| **Docker** | Docker Desktop | docker.io | Docker Desktop | ✅ |
| **.NET 8 SDK** | MSI/winget | Package manager | PKG/brew | ✅ |
| **Git** | winget/installer | Package manager | Xcode/brew | ✅ |
| **PowerShell** | Built-in | Optional | Optional | ❌ |

## 🚀 Universal Launcher

### **Cross-Platform Setup Script**

The `dev-setup.ps1` script automatically detects your platform and launches the appropriate tools:

```powershell
# Universal launcher (works on all platforms)
.\scripts\dev-setup.ps1

# Specific actions
.\scripts\dev-setup.ps1 -Menu     # Interactive development menu
.\scripts\dev-setup.ps1 -Start    # Start development environment
.\scripts\dev-setup.ps1 -Config   # Configuration manager
.\scripts\dev-setup.ps1 -Stop     # Stop services
.\scripts\dev-setup.ps1 -Help     # Show help
```

### **Platform Detection**

The launcher automatically detects:
- **Windows**: Uses PowerShell scripts in `scripts/`
- **Linux/macOS**: Uses Bash scripts in `scripts/unix/`
- **Dependencies**: Checks and offers to install missing tools
- **Package Managers**: Uses appropriate package manager for auto-installation

## 🎛️ Platform-Specific Tools

### **Windows (PowerShell)**

#### **Interactive Menu**
```powershell
.\scripts\dev-menu.ps1
```

#### **Development Scripts**
```powershell
# Start environment with dependency checking
.\scripts\start-dev-services.ps1

# Configuration manager with full features
.\scripts\config-manager.ps1

# Stop services with options
.\scripts\stop-dev-services.ps1 -Remove -RemoveVolumes
```

#### **Features**
- ✅ **Full configuration management** (11 categories)
- ✅ **Dependency auto-installation** (winget, chocolatey)
- ✅ **Export/import configurations**
- ✅ **Secure password input**
- ✅ **Beautiful ASCII art interface**

### **Linux/macOS (Bash)**

#### **Interactive Menu**
```bash
./scripts/unix/dev-menu.sh
```

#### **Development Scripts**
```bash
# Start environment with dependency checking
./scripts/unix/start-dev-services.sh

# Basic configuration manager
./scripts/unix/config-manager.sh

# Stop services with options
./scripts/unix/stop-dev-services.sh --remove --remove-volumes
```

#### **Features**
- ✅ **Core development environment setup**
- ✅ **Dependency checking and installation**
- ✅ **Basic configuration management**
- ✅ **Colorized output**
- ✅ **Docker container management**

## 🔍 Dependency Management

### **Automatic Dependency Checking**

All scripts include comprehensive dependency checking:

```bash
# What gets checked:
✅ Docker (required)
✅ .NET 8 SDK (required)
✅ Git (optional but recommended)
✅ Package managers (for auto-installation)
```

### **Auto-Installation Support**

#### **Windows**
```powershell
# Windows Package Manager (winget)
winget install Docker.DockerDesktop
winget install Microsoft.DotNet.SDK.8
winget install Git.Git

# Chocolatey (alternative)
choco install docker-desktop
choco install dotnet-8.0-sdk
choco install git
```

#### **macOS**
```bash
# Homebrew
brew install --cask docker
brew install --cask dotnet
brew install git

# Manual installers also supported
```

#### **Linux (Ubuntu/Debian)**
```bash
# APT package manager
sudo apt-get update
sudo apt-get install docker.io git

# .NET SDK (special installer)
wget https://dot.net/v1/dotnet-install.sh
chmod +x dotnet-install.sh
./dotnet-install.sh --channel 8.0
```

#### **Linux (CentOS/RHEL)**
```bash
# YUM/DNF package manager
sudo yum install docker git
# or
sudo dnf install docker git

# .NET SDK (special installer)
wget https://dot.net/v1/dotnet-install.sh
chmod +x dotnet-install.sh
./dotnet-install.sh --channel 8.0
```

## ⚙️ Configuration Management

### **Windows (Full-Featured)**

The PowerShell configuration manager provides complete functionality:

```powershell
.\scripts\config-manager.ps1
```

**Available Options:**
1. 🚀 **Quick Setup** - Guided development environment setup
2. 🗄️ **Database** - PostgreSQL connection configuration
3. 🔴 **Redis** - Cache server configuration
4. 📧 **Email** - SMTP settings (Gmail, SendGrid, custom)
5. 🔐 **Security** - JWT keys and authentication
6. 🐳 **Docker** - Container and registry settings
7. ☁️ **Azure** - Deployment configuration
8. 📊 **Monitoring** - Logging and alerting
9. 📄 **View Config** - Display current settings
10. 💾 **Export** - Save configuration
11. 📥 **Import** - Load configuration

### **Linux/macOS (Core Features)**

The Bash configuration manager provides essential functionality:

```bash
./scripts/unix/config-manager.sh
```

**Available Options:**
1. 🚀 **Quick Setup** - Basic development environment setup
2. 📄 **View Config** - Display current settings
3. ⚠️ **Advanced Features** - Redirects to PowerShell version

### **Configuration Files**

Both platforms use the same configuration files:

```
.env              # Environment variables
secrets.json      # Sensitive configuration
.env.template     # Template for new setups
```

## 🧪 Testing Your Setup

### **Verification Commands**

After setup, verify your installation:

```bash
# Check Docker
docker --version
docker info

# Check .NET SDK
dotnet --version
dotnet --list-sdks

# Check Git
git --version

# Test API (after starting services)
curl https://localhost:5001/health
```

### **Health Checks**

The API includes comprehensive health checks:

```bash
# Health endpoint
curl https://localhost:5001/health

# Expected response:
{
  "status": "Healthy",
  "checks": {
    "database": "Healthy",
    "redis": "Healthy"
  }
}
```

## 🚨 Troubleshooting

### **Common Issues**

#### **Docker Not Running**
```bash
# Windows
# Start Docker Desktop from Start Menu

# Linux
sudo systemctl start docker
sudo systemctl enable docker

# macOS
# Start Docker Desktop from Applications
```

#### **Permission Issues (Linux)**
```bash
# Add user to docker group
sudo usermod -aG docker $USER
# Logout and login again
```

#### **PowerShell Execution Policy (Windows)**
```powershell
# Allow script execution
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### **.NET SDK Not Found**
```bash
# Add to PATH (Linux/macOS)
export PATH="$HOME/.dotnet:$PATH"
echo 'export PATH="$HOME/.dotnet:$PATH"' >> ~/.bashrc
```

### **Platform-Specific Issues**

#### **Windows WSL**
```bash
# Use Linux scripts in WSL
cd /mnt/c/path/to/project
./scripts/unix/dev-menu.sh
```

#### **macOS Permissions**
```bash
# Allow script execution
chmod +x scripts/unix/*.sh

# If Gatekeeper blocks execution
sudo spctl --master-disable  # (not recommended for production)
```

#### **Linux SELinux**
```bash
# If SELinux blocks Docker
sudo setsebool -P container_manage_cgroup on
```

## 📞 Support

### **Getting Help**

1. **Check platform-specific documentation**
2. **Use the help commands**:
   ```bash
   .\scripts\dev-setup.ps1 -Help
   ./scripts/unix/stop-dev-services.sh --help
   ```
3. **Review logs and error messages**
4. **Check Docker and .NET SDK installation**

### **Reporting Issues**

When reporting issues, please include:
- Operating system and version
- PowerShell/Bash version
- Docker version
- .NET SDK version
- Complete error messages
- Steps to reproduce

---

**Happy cross-platform development!** 🌍✨
