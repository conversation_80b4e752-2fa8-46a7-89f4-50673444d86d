#!/usr/bin/env python3
"""
Performance Validation Script for CI/CD Pipelines
Validates benchmark results against defined thresholds and baselines
"""

import json
import sys
import argparse
import os
import glob
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class PerformanceThreshold:
    """Performance threshold configuration"""
    max_execution_time: float  # milliseconds
    max_memory_usage: float    # MB
    regression_threshold: float  # percentage
    baseline_file: Optional[str] = None

@dataclass
class BenchmarkResult:
    """Benchmark test result"""
    name: str
    execution_time: float
    memory_usage: float
    throughput: Optional[float] = None
    error_rate: Optional[float] = None

class PerformanceValidator:
    """Validates performance test results against thresholds"""
    
    def __init__(self, thresholds: PerformanceThreshold):
        self.thresholds = thresholds
        self.baseline_results: Dict[str, BenchmarkResult] = {}
        self.current_results: List[BenchmarkResult] = []
        
    def load_baseline(self, baseline_file: str) -> bool:
        """Load baseline performance results"""
        try:
            if os.path.exists(baseline_file):
                with open(baseline_file, 'r') as f:
                    baseline_data = json.load(f)
                    
                for result_data in baseline_data.get('results', []):
                    result = BenchmarkResult(
                        name=result_data['name'],
                        execution_time=result_data['execution_time'],
                        memory_usage=result_data['memory_usage'],
                        throughput=result_data.get('throughput'),
                        error_rate=result_data.get('error_rate')
                    )
                    self.baseline_results[result.name] = result
                    
                print(f"✅ Loaded {len(self.baseline_results)} baseline results")
                return True
            else:
                print(f"⚠️ Baseline file not found: {baseline_file}")
                return False
        except Exception as e:
            print(f"❌ Error loading baseline: {e}")
            return False
    
    def parse_benchmark_results(self, results_dir: str) -> bool:
        """Parse benchmark results from various formats"""
        try:
            # Look for BenchmarkDotNet JSON results
            json_files = glob.glob(os.path.join(results_dir, "**/*.json"), recursive=True)
            
            for json_file in json_files:
                if self._parse_benchmarkdotnet_json(json_file):
                    continue
                elif self._parse_custom_json(json_file):
                    continue
            
            # Look for JUnit XML results (for other frameworks)
            xml_files = glob.glob(os.path.join(results_dir, "**/*.xml"), recursive=True)
            for xml_file in xml_files:
                self._parse_junit_xml(xml_file)
            
            print(f"✅ Parsed {len(self.current_results)} benchmark results")
            return len(self.current_results) > 0
            
        except Exception as e:
            print(f"❌ Error parsing results: {e}")
            return False
    
    def _parse_benchmarkdotnet_json(self, json_file: str) -> bool:
        """Parse BenchmarkDotNet JSON format"""
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
                
            if 'Benchmarks' not in data:
                return False
                
            for benchmark in data['Benchmarks']:
                result = BenchmarkResult(
                    name=benchmark.get('FullName', benchmark.get('Method', 'Unknown')),
                    execution_time=benchmark.get('Statistics', {}).get('Mean', 0) / 1_000_000,  # Convert ns to ms
                    memory_usage=benchmark.get('Memory', {}).get('BytesAllocatedPerOperation', 0) / 1_048_576,  # Convert bytes to MB
                    throughput=benchmark.get('Statistics', {}).get('Throughput')
                )
                self.current_results.append(result)
                
            return True
            
        except Exception as e:
            print(f"⚠️ Could not parse as BenchmarkDotNet JSON: {e}")
            return False
    
    def _parse_custom_json(self, json_file: str) -> bool:
        """Parse custom JSON format"""
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
                
            if 'performance_results' not in data:
                return False
                
            for result_data in data['performance_results']:
                result = BenchmarkResult(
                    name=result_data['name'],
                    execution_time=result_data['execution_time'],
                    memory_usage=result_data['memory_usage'],
                    throughput=result_data.get('throughput'),
                    error_rate=result_data.get('error_rate')
                )
                self.current_results.append(result)
                
            return True
            
        except Exception as e:
            print(f"⚠️ Could not parse as custom JSON: {e}")
            return False
    
    def _parse_junit_xml(self, xml_file: str) -> bool:
        """Parse JUnit XML format for performance data"""
        try:
            import xml.etree.ElementTree as ET
            tree = ET.parse(xml_file)
            root = tree.getroot()
            
            for testcase in root.findall('.//testcase'):
                name = testcase.get('name', 'Unknown')
                time_str = testcase.get('time', '0')
                execution_time = float(time_str) * 1000  # Convert seconds to ms
                
                # Look for custom properties with memory usage
                memory_usage = 0
                for prop in testcase.findall('.//property'):
                    if prop.get('name') == 'memory_usage_mb':
                        memory_usage = float(prop.get('value', '0'))
                
                result = BenchmarkResult(
                    name=name,
                    execution_time=execution_time,
                    memory_usage=memory_usage
                )
                self.current_results.append(result)
                
            return True
            
        except Exception as e:
            print(f"⚠️ Could not parse XML: {e}")
            return False
    
    def validate_thresholds(self) -> bool:
        """Validate current results against absolute thresholds"""
        violations = []
        
        for result in self.current_results:
            # Check execution time threshold
            if result.execution_time > self.thresholds.max_execution_time:
                violations.append(f"❌ {result.name}: Execution time {result.execution_time:.2f}ms exceeds threshold {self.thresholds.max_execution_time}ms")
            
            # Check memory usage threshold
            if result.memory_usage > self.thresholds.max_memory_usage:
                violations.append(f"❌ {result.name}: Memory usage {result.memory_usage:.2f}MB exceeds threshold {self.thresholds.max_memory_usage}MB")
        
        if violations:
            print("\n🚨 Performance Threshold Violations:")
            for violation in violations:
                print(f"  {violation}")
            return False
        else:
            print("✅ All performance thresholds passed")
            return True
    
    def validate_regression(self) -> bool:
        """Validate current results against baseline for regression"""
        if not self.baseline_results:
            print("⚠️ No baseline results available for regression testing")
            return True
        
        regressions = []
        improvements = []
        
        for result in self.current_results:
            if result.name in self.baseline_results:
                baseline = self.baseline_results[result.name]
                
                # Calculate execution time regression
                time_change = ((result.execution_time - baseline.execution_time) / baseline.execution_time) * 100
                if time_change > self.thresholds.regression_threshold:
                    regressions.append(f"❌ {result.name}: Execution time regression {time_change:.1f}% (was {baseline.execution_time:.2f}ms, now {result.execution_time:.2f}ms)")
                elif time_change < -5:  # Improvement threshold
                    improvements.append(f"🚀 {result.name}: Execution time improved by {abs(time_change):.1f}%")
                
                # Calculate memory regression
                memory_change = ((result.memory_usage - baseline.memory_usage) / baseline.memory_usage) * 100 if baseline.memory_usage > 0 else 0
                if memory_change > self.thresholds.regression_threshold:
                    regressions.append(f"❌ {result.name}: Memory usage regression {memory_change:.1f}% (was {baseline.memory_usage:.2f}MB, now {result.memory_usage:.2f}MB)")
        
        if improvements:
            print("\n🚀 Performance Improvements:")
            for improvement in improvements:
                print(f"  {improvement}")
        
        if regressions:
            print("\n🚨 Performance Regressions:")
            for regression in regressions:
                print(f"  {regression}")
            return False
        else:
            print("✅ No performance regressions detected")
            return True
    
    def save_current_as_baseline(self, baseline_file: str) -> bool:
        """Save current results as new baseline"""
        try:
            baseline_data = {
                "timestamp": str(Path().absolute()),
                "results": [
                    {
                        "name": result.name,
                        "execution_time": result.execution_time,
                        "memory_usage": result.memory_usage,
                        "throughput": result.throughput,
                        "error_rate": result.error_rate
                    }
                    for result in self.current_results
                ]
            }
            
            os.makedirs(os.path.dirname(baseline_file), exist_ok=True)
            with open(baseline_file, 'w') as f:
                json.dump(baseline_data, f, indent=2)
                
            print(f"✅ Saved {len(self.current_results)} results as new baseline")
            return True
            
        except Exception as e:
            print(f"❌ Error saving baseline: {e}")
            return False
    
    def generate_report(self) -> str:
        """Generate performance report"""
        report = ["# Performance Test Report\n"]
        
        if self.current_results:
            report.append("## Current Results\n")
            report.append("| Test Name | Execution Time (ms) | Memory Usage (MB) |")
            report.append("|-----------|-------------------|------------------|")
            
            for result in self.current_results:
                report.append(f"| {result.name} | {result.execution_time:.2f} | {result.memory_usage:.2f} |")
            
            report.append("\n")
        
        if self.baseline_results:
            report.append("## Comparison with Baseline\n")
            report.append("| Test Name | Time Change | Memory Change |")
            report.append("|-----------|-------------|---------------|")
            
            for result in self.current_results:
                if result.name in self.baseline_results:
                    baseline = self.baseline_results[result.name]
                    time_change = ((result.execution_time - baseline.execution_time) / baseline.execution_time) * 100
                    memory_change = ((result.memory_usage - baseline.memory_usage) / baseline.memory_usage) * 100 if baseline.memory_usage > 0 else 0
                    
                    time_icon = "🔴" if time_change > 10 else "🟡" if time_change > 5 else "🟢"
                    memory_icon = "🔴" if memory_change > 10 else "🟡" if memory_change > 5 else "🟢"
                    
                    report.append(f"| {result.name} | {time_icon} {time_change:+.1f}% | {memory_icon} {memory_change:+.1f}% |")
        
        return "\n".join(report)

def main():
    parser = argparse.ArgumentParser(description="Validate performance test results")
    parser.add_argument("--results", required=True, help="Directory containing performance test results")
    parser.add_argument("--threshold", type=float, default=10.0, help="Regression threshold percentage (default: 10%)")
    parser.add_argument("--max-time", type=float, default=5000.0, help="Maximum execution time in ms (default: 5000)")
    parser.add_argument("--max-memory", type=float, default=100.0, help="Maximum memory usage in MB (default: 100)")
    parser.add_argument("--baseline", help="Baseline results file for regression testing")
    parser.add_argument("--save-baseline", help="Save current results as new baseline")
    parser.add_argument("--report", help="Generate performance report file")
    
    args = parser.parse_args()
    
    # Create performance thresholds
    thresholds = PerformanceThreshold(
        max_execution_time=args.max_time,
        max_memory_usage=args.max_memory,
        regression_threshold=args.threshold,
        baseline_file=args.baseline
    )
    
    # Initialize validator
    validator = PerformanceValidator(thresholds)
    
    # Load baseline if provided
    if args.baseline:
        validator.load_baseline(args.baseline)
    
    # Parse current results
    if not validator.parse_benchmark_results(args.results):
        print("❌ No performance results found")
        sys.exit(1)
    
    # Validate thresholds
    threshold_passed = validator.validate_thresholds()
    
    # Validate regression
    regression_passed = validator.validate_regression()
    
    # Save new baseline if requested
    if args.save_baseline:
        validator.save_current_as_baseline(args.save_baseline)
    
    # Generate report if requested
    if args.report:
        report = validator.generate_report()
        with open(args.report, 'w') as f:
            f.write(report)
        print(f"📊 Performance report saved to {args.report}")
    
    # Exit with appropriate code
    if threshold_passed and regression_passed:
        print("\n🎉 All performance validations passed!")
        sys.exit(0)
    else:
        print("\n💥 Performance validation failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
