# ═══════════════════════════════════════════════════════════════════════════════
# 🎯 Deployment Target Detection System
# ═══════════════════════════════════════════════════════════════════════════════
# Automatically detects and configures deployment targets based on repository analysis

param(
    [string]$ProjectPath = ".",
    [string]$OutputFormat = "json",    # json, yaml, powershell
    [switch]$Interactive,
    [switch]$Verbose
)

# Configuration
$ScriptRoot = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Resolve-Path $ProjectPath

function Write-DetectorLog {
    param([string]$Message, [string]$Level = "INFO")
    
    if ($Verbose) {
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            default { "Cyan" }
        }
        Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
    }
}

function Test-FileExists {
    param([string]$Path, [string]$Pattern = "")
    
    if ($Pattern) {
        return (Get-ChildItem -Path $Path -Filter $Pattern -Recurse -ErrorAction SilentlyContinue).Count -gt 0
    } else {
        return Test-Path $Path
    }
}

function Get-FileContent {
    param([string]$FilePath)
    
    if (Test-Path $FilePath) {
        return Get-Content $FilePath -Raw -ErrorAction SilentlyContinue
    }
    return ""
}

function Detect-AzureTargets {
    param([string]$ProjectPath)
    
    Write-DetectorLog "Detecting Azure deployment targets..." "INFO"
    
    $azureTargets = @()
    
    # Azure Web Apps
    if (Test-FileExists $ProjectPath "web.config" -or 
        Test-FileExists $ProjectPath "*.csproj" -or
        Test-FileExists $ProjectPath "package.json") {
        
        $webAppConfig = @{
            "type" = "azure-webapp"
            "name" = "Azure Web App"
            "confidence" = 0.8
            "requirements" = @("Azure subscription", "App Service plan")
            "supportedFrameworks" = @(".NET", "Node.js", "Python", "Java", "PHP")
            "deploymentMethods" = @("ZIP", "Git", "Docker", "FTP")
            "configuration" = @{
                "appServicePlan" = "Standard"
                "runtime" = "Auto-detect"
                "enableLogging" = $true
                "enableMetrics" = $true
            }
        }
        
        # Check for specific framework indicators
        if (Test-FileExists $ProjectPath "*.csproj") {
            $webAppConfig.runtime = ".NET"
            $webAppConfig.confidence = 0.9
        }
        elseif (Test-FileExists $ProjectPath "package.json") {
            $webAppConfig.runtime = "Node.js"
            $webAppConfig.confidence = 0.9
        }
        
        $azureTargets += $webAppConfig
    }
    
    # Azure Container Instances
    if (Test-FileExists $ProjectPath "Dockerfile") {
        $aciConfig = @{
            "type" = "azure-container-instances"
            "name" = "Azure Container Instances"
            "confidence" = 0.7
            "requirements" = @("Azure subscription", "Container Registry")
            "supportedFrameworks" = @("Any containerized application")
            "deploymentMethods" = @("Docker image")
            "configuration" = @{
                "cpu" = 1
                "memory" = "1.5GB"
                "osType" = "Linux"
                "restartPolicy" = "Always"
            }
        }
        $azureTargets += $aciConfig
    }
    
    # Azure Kubernetes Service
    if (Test-FileExists $ProjectPath "*.yaml" -or Test-FileExists $ProjectPath "*.yml") {
        $k8sFiles = Get-ChildItem -Path $ProjectPath -Filter "*.yaml" -Recurse | 
                   Where-Object { (Get-Content $_.FullName -Raw) -match "apiVersion.*apps" }
        
        if ($k8sFiles.Count -gt 0) {
            $aksConfig = @{
                "type" = "azure-kubernetes-service"
                "name" = "Azure Kubernetes Service"
                "confidence" = 0.9
                "requirements" = @("Azure subscription", "AKS cluster", "kubectl")
                "supportedFrameworks" = @("Any containerized application")
                "deploymentMethods" = @("Kubernetes manifests", "Helm charts")
                "configuration" = @{
                    "nodeCount" = 3
                    "vmSize" = "Standard_DS2_v2"
                    "kubernetesVersion" = "latest"
                    "enableRBAC" = $true
                }
            }
            $azureTargets += $aksConfig
        }
    }
    
    # Azure Functions
    if (Test-FileExists $ProjectPath "host.json" -or Test-FileExists $ProjectPath "function.json") {
        $functionsConfig = @{
            "type" = "azure-functions"
            "name" = "Azure Functions"
            "confidence" = 0.95
            "requirements" = @("Azure subscription", "Function App")
            "supportedFrameworks" = @(".NET", "Node.js", "Python", "Java", "PowerShell")
            "deploymentMethods" = @("ZIP", "Git", "Docker")
            "configuration" = @{
                "runtime" = "Auto-detect"
                "version" = "~4"
                "plan" = "Consumption"
                "enableAppInsights" = $true
            }
        }
        $azureTargets += $functionsConfig
    }
    
    return $azureTargets
}

function Detect-AWSTargets {
    param([string]$ProjectPath)
    
    Write-DetectorLog "Detecting AWS deployment targets..." "INFO"
    
    $awsTargets = @()
    
    # AWS Elastic Beanstalk
    if (Test-FileExists $ProjectPath ".ebextensions" -or 
        Test-FileExists $ProjectPath "Dockerrun.aws.json") {
        
        $ebConfig = @{
            "type" = "aws-elastic-beanstalk"
            "name" = "AWS Elastic Beanstalk"
            "confidence" = 0.9
            "requirements" = @("AWS account", "EB CLI")
            "supportedFrameworks" = @(".NET", "Node.js", "Python", "Java", "PHP", "Ruby", "Go")
            "deploymentMethods" = @("ZIP", "Docker", "Git")
            "configuration" = @{
                "platform" = "Auto-detect"
                "instanceType" = "t3.micro"
                "loadBalancer" = $true
                "autoScaling" = $true
            }
        }
        $awsTargets += $ebConfig
    }
    
    # AWS Lambda
    if (Test-FileExists $ProjectPath "template.yaml" -or 
        Test-FileExists $ProjectPath "serverless.yml" -or
        Test-FileExists $ProjectPath "sam.yaml") {
        
        $lambdaConfig = @{
            "type" = "aws-lambda"
            "name" = "AWS Lambda"
            "confidence" = 0.9
            "requirements" = @("AWS account", "SAM CLI or Serverless Framework")
            "supportedFrameworks" = @(".NET", "Node.js", "Python", "Java", "Go", "Ruby")
            "deploymentMethods" = @("SAM", "Serverless Framework", "CloudFormation")
            "configuration" = @{
                "runtime" = "Auto-detect"
                "memory" = 512
                "timeout" = 30
                "environment" = "dev"
            }
        }
        $awsTargets += $lambdaConfig
    }
    
    # AWS ECS
    if (Test-FileExists $ProjectPath "Dockerfile" -and 
        Test-FileExists $ProjectPath "task-definition.json") {
        
        $ecsConfig = @{
            "type" = "aws-ecs"
            "name" = "AWS Elastic Container Service"
            "confidence" = 0.85
            "requirements" = @("AWS account", "ECS cluster", "ECR repository")
            "supportedFrameworks" = @("Any containerized application")
            "deploymentMethods" = @("Docker image", "Task definitions")
            "configuration" = @{
                "launchType" = "FARGATE"
                "cpu" = 256
                "memory" = 512
                "networkMode" = "awsvpc"
            }
        }
        $awsTargets += $ecsConfig
    }
    
    return $awsTargets
}

function Detect-DockerTargets {
    param([string]$ProjectPath)
    
    Write-DetectorLog "Detecting Docker deployment targets..." "INFO"
    
    $dockerTargets = @()
    
    if (Test-FileExists $ProjectPath "Dockerfile") {
        $dockerConfig = @{
            "type" = "docker-registry"
            "name" = "Docker Registry"
            "confidence" = 0.9
            "requirements" = @("Docker", "Container registry access")
            "supportedFrameworks" = @("Any containerized application")
            "deploymentMethods" = @("Docker build and push")
            "configuration" = @{
                "registry" = "docker.io"
                "repository" = "Auto-detect from project name"
                "tag" = "latest"
                "buildArgs" = @{}
            }
        }
        
        # Check for multi-stage builds
        $dockerfileContent = Get-FileContent (Join-Path $ProjectPath "Dockerfile")
        if ($dockerfileContent -match "FROM.*AS") {
            $dockerConfig.configuration.multistage = $true
            $dockerConfig.confidence = 0.95
        }
        
        # Check for docker-compose
        if (Test-FileExists $ProjectPath "docker-compose.yml" -or 
            Test-FileExists $ProjectPath "docker-compose.yaml") {
            $dockerConfig.configuration.compose = $true
            $dockerConfig.deploymentMethods += "Docker Compose"
        }
        
        $dockerTargets += $dockerConfig
    }
    
    return $dockerTargets
}

function Detect-KubernetesTargets {
    param([string]$ProjectPath)
    
    Write-DetectorLog "Detecting Kubernetes deployment targets..." "INFO"
    
    $k8sTargets = @()
    
    # Check for Kubernetes manifests
    $k8sFiles = Get-ChildItem -Path $ProjectPath -Filter "*.yaml" -Recurse | 
               Where-Object { 
                   $content = Get-Content $_.FullName -Raw -ErrorAction SilentlyContinue
                   $content -match "apiVersion.*v1" -or $content -match "kind:\s*(Deployment|Service|Ingress)"
               }
    
    if ($k8sFiles.Count -gt 0) {
        $k8sConfig = @{
            "type" = "kubernetes"
            "name" = "Kubernetes Cluster"
            "confidence" = 0.9
            "requirements" = @("Kubernetes cluster", "kubectl", "Docker registry")
            "supportedFrameworks" = @("Any containerized application")
            "deploymentMethods" = @("kubectl apply", "Helm charts", "Kustomize")
            "configuration" = @{
                "namespace" = "default"
                "replicas" = 3
                "strategy" = "RollingUpdate"
                "resources" = @{
                    "requests" = @{ "cpu" = "100m"; "memory" = "128Mi" }
                    "limits" = @{ "cpu" = "500m"; "memory" = "512Mi" }
                }
            }
        }
        
        # Check for Helm charts
        if (Test-FileExists $ProjectPath "Chart.yaml" -or Test-FileExists $ProjectPath "values.yaml") {
            $k8sConfig.configuration.helm = $true
            $k8sConfig.confidence = 0.95
        }
        
        # Check for Kustomize
        if (Test-FileExists $ProjectPath "kustomization.yaml") {
            $k8sConfig.configuration.kustomize = $true
        }
        
        $k8sTargets += $k8sConfig
    }
    
    return $k8sTargets
}

function Detect-FTPTargets {
    param([string]$ProjectPath)
    
    Write-DetectorLog "Detecting FTP/SFTP deployment targets..." "INFO"
    
    $ftpTargets = @()
    
    # Check for web application indicators
    if (Test-FileExists $ProjectPath "index.html" -or 
        Test-FileExists $ProjectPath "index.php" -or
        Test-FileExists $ProjectPath "*.html" -or
        Test-FileExists $ProjectPath "wwwroot") {
        
        $ftpConfig = @{
            "type" = "ftp-server"
            "name" = "FTP/SFTP Server"
            "confidence" = 0.6
            "requirements" = @("FTP/SFTP server access", "Credentials")
            "supportedFrameworks" = @("Static websites", "PHP", "HTML/CSS/JS")
            "deploymentMethods" = @("FTP", "SFTP", "FTPS")
            "configuration" = @{
                "protocol" = "SFTP"
                "port" = 22
                "preservePermissions" = $true
                "backup" = $true
                "excludePatterns" = @("*.log", ".git", "node_modules")
            }
        }
        
        # Higher confidence for static sites
        if (Test-FileExists $ProjectPath "index.html" -and 
            -not (Test-FileExists $ProjectPath "*.csproj") -and
            -not (Test-FileExists $ProjectPath "package.json")) {
            $ftpConfig.confidence = 0.8
        }
        
        $ftpTargets += $ftpConfig
    }
    
    return $ftpTargets
}

function Get-DeploymentRecommendations {
    param([array]$AllTargets)
    
    Write-DetectorLog "Generating deployment recommendations..." "INFO"
    
    $recommendations = @()
    
    # Sort targets by confidence
    $sortedTargets = $AllTargets | Sort-Object confidence -Descending
    
    foreach ($target in $sortedTargets) {
        $recommendation = @{
            "target" = $target
            "priority" = switch ($target.confidence) {
                { $_ -ge 0.9 } { "High" }
                { $_ -ge 0.7 } { "Medium" }
                default { "Low" }
            }
            "reasoning" = @()
        }
        
        # Add reasoning based on target type and confidence
        if ($target.confidence -ge 0.9) {
            $recommendation.reasoning += "Strong indicators found for this deployment target"
        }
        
        if ($target.type -match "azure|aws") {
            $recommendation.reasoning += "Cloud-native deployment provides scalability and managed services"
        }
        
        if ($target.type -match "kubernetes") {
            $recommendation.reasoning += "Container orchestration provides high availability and scaling"
        }
        
        if ($target.type -match "docker") {
            $recommendation.reasoning += "Containerization ensures consistent deployment across environments"
        }
        
        if ($target.type -match "ftp") {
            $recommendation.reasoning += "Simple deployment suitable for static content or legacy systems"
        }
        
        $recommendations += $recommendation
    }
    
    return $recommendations
}

function Export-Results {
    param([object]$Results, [string]$Format, [string]$OutputPath = "")
    
    switch ($Format.ToLower()) {
        "json" {
            $json = $Results | ConvertTo-Json -Depth 10
            if ($OutputPath) {
                $json | Set-Content -Path $OutputPath -Encoding UTF8
                Write-DetectorLog "Results exported to: $OutputPath" "SUCCESS"
            } else {
                return $json
            }
        }
        "yaml" {
            # Simple YAML conversion (basic implementation)
            $yaml = ConvertTo-Yaml $Results
            if ($OutputPath) {
                $yaml | Set-Content -Path $OutputPath -Encoding UTF8
                Write-DetectorLog "Results exported to: $OutputPath" "SUCCESS"
            } else {
                return $yaml
            }
        }
        "powershell" {
            $ps = $Results | ConvertTo-Json -Depth 10 | ConvertFrom-Json
            if ($OutputPath) {
                "`$deploymentTargets = " + ($Results | ConvertTo-Json -Depth 10) | Set-Content -Path $OutputPath -Encoding UTF8
                Write-DetectorLog "Results exported to: $OutputPath" "SUCCESS"
            } else {
                return $ps
            }
        }
    }
}

function ConvertTo-Yaml {
    param([object]$Object, [int]$Depth = 0)
    
    $indent = "  " * $Depth
    $yaml = ""
    
    if ($Object -is [hashtable] -or $Object -is [PSCustomObject]) {
        foreach ($key in $Object.Keys) {
            $value = $Object[$key]
            if ($value -is [array]) {
                $yaml += "$indent$key:`n"
                foreach ($item in $value) {
                    $yaml += "$indent- $(ConvertTo-Yaml $item ($Depth + 1))`n"
                }
            } elseif ($value -is [hashtable] -or $value -is [PSCustomObject]) {
                $yaml += "$indent$key:`n"
                $yaml += ConvertTo-Yaml $value ($Depth + 1)
            } else {
                $yaml += "$indent$key`: $value`n"
            }
        }
    } else {
        $yaml = $Object.ToString()
    }
    
    return $yaml
}

# Main execution
Write-DetectorLog "Starting deployment target detection for: $ProjectRoot" "INFO"

$detectionResults = @{
    "projectPath" = $ProjectRoot
    "detectionTimestamp" = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
    "targets" = @{
        "azure" = Detect-AzureTargets $ProjectRoot
        "aws" = Detect-AWSTargets $ProjectRoot
        "docker" = Detect-DockerTargets $ProjectRoot
        "kubernetes" = Detect-KubernetesTargets $ProjectRoot
        "ftp" = Detect-FTPTargets $ProjectRoot
    }
}

# Flatten all targets for recommendations
$allTargets = @()
$detectionResults.targets.Keys | ForEach-Object {
    $allTargets += $detectionResults.targets[$_]
}

$detectionResults.recommendations = Get-DeploymentRecommendations $allTargets
$detectionResults.summary = @{
    "totalTargetsFound" = $allTargets.Count
    "highConfidenceTargets" = ($allTargets | Where-Object { $_.confidence -ge 0.9 }).Count
    "recommendedTarget" = if ($allTargets.Count -gt 0) { ($allTargets | Sort-Object confidence -Descending)[0].name } else { "None detected" }
}

Write-DetectorLog "Detection completed. Found $($allTargets.Count) potential deployment targets." "SUCCESS"

if ($Interactive) {
    Write-Host "`n🎯 Deployment Target Detection Results" -ForegroundColor Cyan
    Write-Host "=======================================" -ForegroundColor Gray
    
    Write-Host "`n📊 Summary:" -ForegroundColor Yellow
    Write-Host "  Total targets found: $($detectionResults.summary.totalTargetsFound)" -ForegroundColor White
    Write-Host "  High confidence targets: $($detectionResults.summary.highConfidenceTargets)" -ForegroundColor White
    Write-Host "  Recommended target: $($detectionResults.summary.recommendedTarget)" -ForegroundColor Green
    
    if ($allTargets.Count -gt 0) {
        Write-Host "`n🎯 Detected Targets:" -ForegroundColor Yellow
        foreach ($target in ($allTargets | Sort-Object confidence -Descending)) {
            $confidenceColor = if ($target.confidence -ge 0.9) { "Green" } elseif ($target.confidence -ge 0.7) { "Yellow" } else { "Red" }
            Write-Host "  • $($target.name) (Confidence: $($target.confidence * 100)%)" -ForegroundColor $confidenceColor
            Write-Host "    Type: $($target.type)" -ForegroundColor Gray
            Write-Host "    Methods: $($target.deploymentMethods -join ', ')" -ForegroundColor Gray
        }
    }
}

# Export results
$output = Export-Results $detectionResults $OutputFormat
if (-not $Interactive) {
    Write-Output $output
}
