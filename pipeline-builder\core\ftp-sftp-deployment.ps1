# ═══════════════════════════════════════════════════════════════════════════════
# 📁 FTP/SFTP Deployment Module
# ═══════════════════════════════════════════════════════════════════════════════
# Handles FTP, SFTP, and SSH-based deployments with key management

param(
    [string]$Action = "menu",           # menu, deploy, test, configure
    [string]$Protocol = "",             # ftp, sftp, scp, rsync
    [string]$Environment = "",          # dev, staging, prod
    [string]$ConfigFile = "",           # Deployment configuration file
    [switch]$TestConnection,            # Test connection only
    [switch]$DryRun,                    # Dry run mode
    [switch]$Json                       # JSON output
)

# Configuration
$ScriptRoot = Split-Path -Parent $MyInvocation.MyCommand.Path
$PipelineBuilderRoot = Split-Path -Parent $ScriptRoot
$ProjectRoot = Split-Path -Parent $PipelineBuilderRoot
$FtpConfigFile = Join-Path $PipelineBuilderRoot "config/ftp-sftp-config.json"
$SshKeysDir = Join-Path $PipelineBuilderRoot "ssh-keys"
$LogsDir = Join-Path $PipelineBuilderRoot "logs"

# Ensure directories exist
@($SshKeysDir, $LogsDir) | ForEach-Object {
    if (-not (Test-Path $_)) {
        New-Item -ItemType Directory -Path $_ | Out-Null
    }
}

# Colors
$Colors = @{
    Red = "Red"; Green = "Green"; Yellow = "Yellow"; Blue = "Blue"
    Cyan = "Cyan"; Magenta = "Magenta"; Gray = "Gray"; White = "White"
}

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    if (-not $Json) { Write-Host $Text -ForegroundColor $Colors[$Color] }
}

function Write-FtpLog {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    if (-not $Json) {
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            default { "Cyan" }
        }
        Write-ColorText "[$timestamp] [$Level] $Message" $color
    }
    
    # Write to log file
    $logFile = Join-Path $LogsDir "ftp-deployment-$(Get-Date -Format 'yyyy-MM-dd').log"
    Add-Content -Path $logFile -Value "[$timestamp] [$Level] $Message"
}

function Show-Header {
    if ($Json) { return }
    
    Clear-Host
    Write-Host ""
    Write-ColorText "    📁📁📁 FTP/SFTP DEPLOYMENT MODULE 📁📁📁" "Cyan"
    Write-Host ""
    Write-ColorText "    🔐 SSH Keys • SFTP • FTP • SCP • Rsync" "Yellow"
    Write-ColorText "    🌐 Remote Servers • Secure Transfer • Automation" "Gray"
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "Gray"
}

function Initialize-FtpConfiguration {
    Write-FtpLog "Initializing FTP/SFTP configuration..." "INFO"
    
    $configDir = Split-Path -Parent $FtpConfigFile
    if (-not (Test-Path $configDir)) {
        New-Item -ItemType Directory -Path $configDir | Out-Null
    }
    
    if (-not (Test-Path $FtpConfigFile)) {
        $defaultConfig = @{
            "deploymentTargets" = @{
                "ftp" = @{
                    "name" = "FTP Deployment"
                    "protocol" = "ftp"
                    "port" = 21
                    "passive" = $true
                    "ssl" = $false
                    "authMethods" = @("username", "anonymous")
                    "transferModes" = @("binary", "ascii")
                    "features" = @("directory_listing", "file_upload", "file_download")
                }
                "ftps" = @{
                    "name" = "FTP over SSL/TLS"
                    "protocol" = "ftps"
                    "port" = 990
                    "passive" = $true
                    "ssl" = $true
                    "authMethods" = @("username", "certificate")
                    "transferModes" = @("binary", "ascii")
                    "features" = @("directory_listing", "file_upload", "file_download", "encryption")
                }
                "sftp" = @{
                    "name" = "SSH File Transfer Protocol"
                    "protocol" = "sftp"
                    "port" = 22
                    "authMethods" = @("password", "publickey", "keyboard-interactive")
                    "keyTypes" = @("rsa", "ed25519", "ecdsa")
                    "features" = @("directory_listing", "file_upload", "file_download", "permissions", "symbolic_links")
                }
                "scp" = @{
                    "name" = "Secure Copy Protocol"
                    "protocol" = "scp"
                    "port" = 22
                    "authMethods" = @("password", "publickey")
                    "keyTypes" = @("rsa", "ed25519", "ecdsa")
                    "features" = @("file_upload", "file_download", "recursive_copy")
                }
                "rsync" = @{
                    "name" = "Remote Sync"
                    "protocol" = "rsync"
                    "port" = 873
                    "sshPort" = 22
                    "authMethods" = @("password", "publickey", "anonymous")
                    "features" = @("incremental_sync", "compression", "checksum_verification", "bandwidth_limiting")
                }
            }
            "environments" = @{
                "dev" = @{
                    "ftp" = @{
                        "host" = "dev-ftp.company.com"
                        "port" = 21
                        "username" = "dev_user"
                        "password" = ""
                        "remotePath" = "/var/www/dev"
                        "passive" = $true
                        "ssl" = $false
                    }
                    "sftp" = @{
                        "host" = "dev-sftp.company.com"
                        "port" = 22
                        "username" = "dev_user"
                        "keyFile" = "dev_rsa"
                        "remotePath" = "/var/www/dev"
                        "strictHostKeyChecking" = $false
                    }
                }
                "staging" = @{
                    "sftp" = @{
                        "host" = "staging-sftp.company.com"
                        "port" = 22
                        "username" = "staging_user"
                        "keyFile" = "staging_rsa"
                        "remotePath" = "/var/www/staging"
                        "strictHostKeyChecking" = $true
                    }
                    "rsync" = @{
                        "host" = "staging-rsync.company.com"
                        "port" = 22
                        "username" = "staging_user"
                        "keyFile" = "staging_rsa"
                        "remotePath" = "/var/www/staging"
                        "options" = @("--delete", "--compress", "--verbose")
                    }
                }
                "prod" = @{
                    "sftp" = @{
                        "host" = "prod-sftp.company.com"
                        "port" = 22
                        "username" = "prod_user"
                        "keyFile" = "prod_rsa"
                        "remotePath" = "/var/www/production"
                        "strictHostKeyChecking" = $true
                        "backup" = @{
                            "enabled" = $true
                            "path" = "/var/backups/webapp"
                            "retention" = 7
                        }
                    }
                }
            }
            "sshKeys" = @{
                "keyDirectory" = $SshKeysDir
                "keyTypes" = @{
                    "rsa" = @{
                        "keySize" = 4096
                        "format" = "OpenSSH"
                        "comment" = "Pipeline Builder RSA Key"
                    }
                    "ed25519" = @{
                        "format" = "OpenSSH"
                        "comment" = "Pipeline Builder Ed25519 Key"
                    }
                    "ecdsa" = @{
                        "keySize" = 256
                        "format" = "OpenSSH"
                        "comment" = "Pipeline Builder ECDSA Key"
                    }
                }
                "security" = @{
                    "encryptPrivateKeys" = $true
                    "keyRotationDays" = 90
                    "backupKeys" = $true
                    "auditAccess" = $true
                }
            }
            "deployment" = @{
                "options" = @{
                    "createBackup" = $true
                    "verifyTransfer" = $true
                    "preservePermissions" = $true
                    "excludePatterns" = @("*.log", "*.tmp", ".git/*", "node_modules/*")
                    "includePatterns" = @("*")
                    "retryAttempts" = 3
                    "retryDelay" = 5
                    "timeout" = 300
                }
                "healthChecks" = @{
                    "enabled" = $true
                    "endpoint" = "/health"
                    "timeout" = 30
                    "retries" = 3
                    "expectedStatus" = 200
                }
                "rollback" = @{
                    "enabled" = $true
                    "automatic" = $false
                    "backupPath" = "/var/backups"
                    "maxBackups" = 5
                }
            }
        }
        
        $defaultConfig | ConvertTo-Json -Depth 10 | Set-Content -Path $FtpConfigFile -Encoding UTF8
        Write-FtpLog "Default FTP/SFTP configuration created" "SUCCESS"
    }
    
    return Get-Content $FtpConfigFile | ConvertFrom-Json
}

function New-SshKeyPair {
    param(
        [string]$KeyName,
        [string]$KeyType = "rsa",
        [string]$Comment = "",
        [int]$KeySize = 4096,
        [switch]$Overwrite
    )
    
    Write-FtpLog "Generating SSH key pair: $KeyName ($KeyType)" "INFO"
    
    $privateKeyPath = Join-Path $SshKeysDir "$KeyName"
    $publicKeyPath = Join-Path $SshKeysDir "$KeyName.pub"
    
    # Check if key already exists
    if ((Test-Path $privateKeyPath) -and -not $Overwrite) {
        Write-FtpLog "SSH key already exists: $KeyName (use -Overwrite to replace)" "WARN"
        return $false
    }
    
    try {
        # Generate SSH key pair using ssh-keygen
        $sshKeygenArgs = @(
            "-t", $KeyType
            "-f", $privateKeyPath
            "-N", '""'  # No passphrase for automation
        )
        
        if ($KeyType -eq "rsa") {
            $sshKeygenArgs += @("-b", $KeySize.ToString())
        }
        
        if ($Comment) {
            $sshKeygenArgs += @("-C", $Comment)
        }
        
        $process = Start-Process -FilePath "ssh-keygen" -ArgumentList $sshKeygenArgs -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-FtpLog "SSH key pair generated successfully: $KeyName" "SUCCESS"
            
            # Set appropriate permissions (Windows)
            if ($IsWindows -or $PSVersionTable.PSVersion.Major -le 5) {
                icacls $privateKeyPath /inheritance:r /grant:r "$env:USERNAME:(R)" | Out-Null
            } else {
                # Unix-like systems
                chmod 600 $privateKeyPath
                chmod 644 $publicKeyPath
            }
            
            # Display public key
            if (Test-Path $publicKeyPath) {
                $publicKey = Get-Content $publicKeyPath
                Write-FtpLog "Public key content:" "INFO"
                Write-ColorText $publicKey "Gray"
            }
            
            return $true
        } else {
            Write-FtpLog "Failed to generate SSH key pair: $KeyName" "ERROR"
            return $false
        }
    } catch {
        Write-FtpLog "Error generating SSH key: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-FtpConnection {
    param(
        [object]$Config,
        [string]$Protocol,
        [string]$Environment
    )
    
    Write-FtpLog "Testing $Protocol connection to $Environment..." "INFO"
    
    $envConfig = $Config.environments.$Environment.$Protocol
    if (-not $envConfig) {
        Write-FtpLog "No $Protocol configuration found for environment: $Environment" "ERROR"
        return $false
    }
    
    try {
        switch ($Protocol.ToLower()) {
            "ftp" {
                return Test-FtpConnectionInternal -Config $envConfig
            }
            "sftp" {
                return Test-SftpConnectionInternal -Config $envConfig
            }
            "scp" {
                return Test-ScpConnectionInternal -Config $envConfig
            }
            "rsync" {
                return Test-RsyncConnectionInternal -Config $envConfig
            }
            default {
                Write-FtpLog "Unsupported protocol: $Protocol" "ERROR"
                return $false
            }
        }
    } catch {
        Write-FtpLog "Connection test failed: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-FtpConnectionInternal {
    param([object]$Config)
    
    try {
        # Create FTP request
        $ftpRequest = [System.Net.FtpWebRequest]::Create("ftp://$($Config.host):$($Config.port)/")
        $ftpRequest.Method = [System.Net.WebRequestMethods+Ftp]::ListDirectory
        $ftpRequest.Credentials = New-Object System.Net.NetworkCredential($Config.username, $Config.password)
        $ftpRequest.UsePassive = $Config.passive
        $ftpRequest.Timeout = 10000
        
        # Test connection
        $response = $ftpRequest.GetResponse()
        $response.Close()
        
        Write-FtpLog "FTP connection successful" "SUCCESS"
        return $true
    } catch {
        Write-FtpLog "FTP connection failed: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-SftpConnectionInternal {
    param([object]$Config)
    
    try {
        # Test SFTP connection using ssh command
        $sshArgs = @(
            "-o", "BatchMode=yes"
            "-o", "ConnectTimeout=10"
            "-p", $Config.port
        )
        
        if ($Config.keyFile) {
            $keyPath = Join-Path $SshKeysDir $Config.keyFile
            if (Test-Path $keyPath) {
                $sshArgs += @("-i", $keyPath)
            }
        }
        
        if (-not $Config.strictHostKeyChecking) {
            $sshArgs += @("-o", "StrictHostKeyChecking=no")
        }
        
        $sshArgs += @("$($Config.username)@$($Config.host)", "echo 'SFTP connection test successful'")
        
        $process = Start-Process -FilePath "ssh" -ArgumentList $sshArgs -Wait -PassThru -NoNewWindow -RedirectStandardOutput
        
        if ($process.ExitCode -eq 0) {
            Write-FtpLog "SFTP connection successful" "SUCCESS"
            return $true
        } else {
            Write-FtpLog "SFTP connection failed with exit code: $($process.ExitCode)" "ERROR"
            return $false
        }
    } catch {
        Write-FtpLog "SFTP connection test error: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-ScpConnectionInternal {
    param([object]$Config)
    
    # SCP uses SSH, so test SSH connection
    return Test-SftpConnectionInternal -Config $Config
}

function Test-RsyncConnectionInternal {
    param([object]$Config)
    
    try {
        # Test rsync connection
        $rsyncArgs = @(
            "--dry-run"
            "--list-only"
            "-e", "ssh -p $($Config.port)"
        )
        
        if ($Config.keyFile) {
            $keyPath = Join-Path $SshKeysDir $Config.keyFile
            if (Test-Path $keyPath) {
                $rsyncArgs[2] = "ssh -p $($Config.port) -i $keyPath"
            }
        }
        
        $rsyncArgs += "$($Config.username)@$($Config.host):$($Config.remotePath)/"
        
        $process = Start-Process -FilePath "rsync" -ArgumentList $rsyncArgs -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-FtpLog "Rsync connection successful" "SUCCESS"
            return $true
        } else {
            Write-FtpLog "Rsync connection failed with exit code: $($process.ExitCode)" "ERROR"
            return $false
        }
    } catch {
        Write-FtpLog "Rsync connection test error: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Show-FtpMenu {
    Write-Host ""
    Write-ColorText "📁 FTP/SFTP Deployment Menu" "Cyan"
    Write-Host ""
    Write-ColorText "    [1] 🔐 SSH Key Management" "Green"
    Write-ColorText "        └─ Generate, manage, and configure SSH keys"
    Write-Host ""
    Write-ColorText "    [2] ⚙️ Configure Deployment Targets" "Blue"
    Write-ColorText "        └─ Setup FTP, SFTP, SCP, and Rsync configurations"
    Write-Host ""
    Write-ColorText "    [3] 🧪 Test Connections" "Yellow"
    Write-ColorText "        └─ Verify connectivity to deployment targets"
    Write-Host ""
    Write-ColorText "    [4] 🚀 Deploy Application" "Magenta"
    Write-ColorText "        └─ Deploy to FTP/SFTP servers"
    Write-Host ""
    Write-ColorText "    [5] 📊 View Configuration" "Cyan"
    Write-ColorText "        └─ Display current deployment configuration"
    Write-Host ""
    Write-ColorText "    [0] 🚪 Exit" "Gray"
    Write-Host ""
}

# Main execution
Show-Header

$config = Initialize-FtpConfiguration

if ($Action -eq "menu") {
    do {
        Show-FtpMenu
        $choice = Read-Host "Enter your choice (0-5)"
        
        switch ($choice) {
            "1" {
                Write-Host ""
                Write-ColorText "🔐 SSH Key Management" "Cyan"
                Write-Host ""
                Write-ColorText "  [1] Generate new SSH key pair" "White"
                Write-ColorText "  [2] List existing SSH keys" "White"
                Write-ColorText "  [3] Display public key" "White"
                Write-ColorText "  [4] Remove SSH key" "White"
                $keyChoice = Read-Host "Enter choice (1-4)"
                
                switch ($keyChoice) {
                    "1" {
                        $keyName = Read-Host "Enter key name (e.g., dev_rsa, staging_ed25519)"
                        $keyType = Read-Host "Enter key type (rsa/ed25519/ecdsa, default: rsa)"
                        if (-not $keyType) { $keyType = "rsa" }
                        $comment = Read-Host "Enter comment (optional)"
                        
                        New-SshKeyPair -KeyName $keyName -KeyType $keyType -Comment $comment
                    }
                    "2" {
                        Write-ColorText "Existing SSH keys:" "Yellow"
                        Get-ChildItem -Path $SshKeysDir -Filter "*" | Where-Object { -not $_.Name.EndsWith(".pub") } | ForEach-Object {
                            Write-ColorText "  • $($_.Name)" "White"
                        }
                    }
                    "3" {
                        $keyName = Read-Host "Enter key name"
                        $publicKeyPath = Join-Path $SshKeysDir "$keyName.pub"
                        if (Test-Path $publicKeyPath) {
                            Write-ColorText "Public key content:" "Yellow"
                            Get-Content $publicKeyPath | Write-Host
                        } else {
                            Write-FtpLog "Public key not found: $keyName.pub" "ERROR"
                        }
                    }
                    "4" {
                        $keyName = Read-Host "Enter key name to remove"
                        $confirm = Read-Host "Are you sure you want to remove key '$keyName'? (y/N)"
                        if ($confirm -eq 'y' -or $confirm -eq 'Y') {
                            $privateKeyPath = Join-Path $SshKeysDir $keyName
                            $publicKeyPath = Join-Path $SshKeysDir "$keyName.pub"
                            
                            if (Test-Path $privateKeyPath) { Remove-Item $privateKeyPath }
                            if (Test-Path $publicKeyPath) { Remove-Item $publicKeyPath }
                            
                            Write-FtpLog "SSH key removed: $keyName" "SUCCESS"
                        }
                    }
                }
                Read-Host "Press Enter to continue"
            }
            "2" {
                Write-FtpLog "Configuration management functionality coming soon..." "INFO"
                Read-Host "Press Enter to continue"
            }
            "3" {
                Write-Host ""
                $env = Read-Host "Enter environment (dev/staging/prod)"
                $protocol = Read-Host "Enter protocol (ftp/sftp/scp/rsync)"
                
                if ($env -and $protocol) {
                    Test-FtpConnection -Config $config -Protocol $protocol -Environment $env
                } else {
                    Write-FtpLog "Environment and protocol are required" "ERROR"
                }
                Read-Host "Press Enter to continue"
            }
            "4" {
                Write-FtpLog "Deployment functionality coming soon..." "INFO"
                Read-Host "Press Enter to continue"
            }
            "5" {
                Write-Host ""
                Write-ColorText "Current FTP/SFTP Configuration:" "Cyan"
                $config | ConvertTo-Json -Depth 3 | Write-Host
                Read-Host "Press Enter to continue"
            }
            "0" {
                Write-FtpLog "Goodbye!" "INFO"
                break
            }
            default {
                Write-FtpLog "Invalid choice. Please try again." "WARN"
                Read-Host "Press Enter to continue"
            }
        }
    } while ($choice -ne "0")
}
