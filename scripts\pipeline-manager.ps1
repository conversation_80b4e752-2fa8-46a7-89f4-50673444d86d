# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 Notify Service API - Pipeline Management System
# ═══════════════════════════════════════════════════════════════════════════════
# Comprehensive pipeline management with Azure DevOps integration, OAuth, and monitoring

param(
    [string]$Action = "menu",           # menu, trigger, status, logs, deploy, secrets
    [string]$Environment = "",          # dev, staging, prod
    [string]$Branch = "",               # Branch to build/deploy
    [string]$BuildId = "",              # Specific build ID
    [string]$PipelineId = "",           # Pipeline ID
    [switch]$Wait,                      # Wait for completion
    [switch]$Force,                     # Force deployment
    [switch]$DryRun,                    # Dry run mode
    [switch]$Verbose,                   # Verbose output
    [switch]$Json                       # JSON output
)

# Configuration
$ProjectRoot = Split-Path -Parent (Split-Path -Parent $MyInvocation.MyCommand.Path)
$ConfigFile = Join-Path $ProjectRoot "pipeline-config.json"
$SecretsFile = Join-Path $ProjectRoot "pipeline-secrets.json"
$LogsDir = Join-Path $ProjectRoot "logs"

# Ensure logs directory exists
if (-not (Test-Path $LogsDir)) {
    New-Item -ItemType Directory -Path $LogsDir | Out-Null
}

# Colors
$Colors = @{
    Red = "Red"; Green = "Green"; Yellow = "Yellow"; Blue = "Blue"
    Cyan = "Cyan"; Magenta = "Magenta"; Gray = "Gray"; White = "White"
    DarkGray = "DarkGray"
}

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    if (-not $Json) { Write-Host $Text -ForegroundColor $Colors[$Color] }
}

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    # Write to console
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARN" { "Yellow" }
        "SUCCESS" { "Green" }
        default { "White" }
    }
    Write-ColorText $logEntry $color
    
    # Write to log file
    $logFile = Join-Path $LogsDir "pipeline-$(Get-Date -Format 'yyyy-MM-dd').log"
    Add-Content -Path $logFile -Value $logEntry
}

function Show-Header {
    if ($Json) { return }
    
    Clear-Host
    Write-Host ""
    Write-ColorText "    🚀🚀🚀 NOTIFY SERVICE API - PIPELINE MANAGER 🚀🚀🚀" "Cyan"
    Write-Host ""
    Write-ColorText "    📊 Azure DevOps • OAuth • Deployment • Monitoring" "Yellow"
    Write-ColorText "    🔧 Build • Deploy • Monitor • Manage" "Gray"
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "DarkGray"
}

function Initialize-Configuration {
    Write-Log "Initializing pipeline configuration..." "INFO"
    
    if (-not (Test-Path $ConfigFile)) {
        $defaultConfig = @{
            "azureDevOps" = @{
                "organization" = "your-org"
                "project" = "notify-service"
                "pipelineId" = "1"
                "repository" = "notify-service-api"
            }
            "environments" = @{
                "dev" = @{
                    "name" = "Development"
                    "webAppName" = "notify-service-api-dev"
                    "resourceGroup" = "notify-service-rg-dev"
                    "subscriptionId" = ""
                }
                "staging" = @{
                    "name" = "Staging"
                    "webAppName" = "notify-service-api-staging"
                    "resourceGroup" = "notify-service-rg-staging"
                    "subscriptionId" = ""
                }
                "prod" = @{
                    "name" = "Production"
                    "webAppName" = "notify-service-api-prod"
                    "resourceGroup" = "notify-service-rg-prod"
                    "subscriptionId" = ""
                }
            }
            "notifications" = @{
                "slack" = @{
                    "enabled" = $false
                    "webhookUrl" = ""
                    "channel" = "#deployments"
                }
                "teams" = @{
                    "enabled" = $false
                    "webhookUrl" = ""
                }
                "email" = @{
                    "enabled" = $false
                    "recipients" = @()
                }
            }
            "monitoring" = @{
                "healthCheckUrl" = "/health"
                "timeoutSeconds" = 300
                "retryAttempts" = 5
                "retryDelaySeconds" = 30
            }
        }
        
        $defaultConfig | ConvertTo-Json -Depth 10 | Set-Content -Path $ConfigFile -Encoding UTF8
        Write-Log "Default configuration created at: $ConfigFile" "SUCCESS"
    }
    
    return Get-Content $ConfigFile | ConvertFrom-Json
}

function Get-AzureDevOpsHeaders {
    param([string]$PersonalAccessToken)
    
    $encodedToken = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(":$PersonalAccessToken"))
    return @{
        "Authorization" = "Basic $encodedToken"
        "Content-Type" = "application/json"
    }
}

function Invoke-AzureDevOpsApi {
    param(
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Headers,
        [string]$Body = ""
    )
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            Headers = $Headers
        }
        
        if ($Body) {
            $params.Body = $Body
        }
        
        $response = Invoke-RestMethod @params
        return $response
    } catch {
        Write-Log "API call failed: $($_.Exception.Message)" "ERROR"
        throw
    }
}

function Get-PersonalAccessToken {
    $secretsPath = $SecretsFile
    
    if (Test-Path $secretsPath) {
        $secrets = Get-Content $secretsPath | ConvertFrom-Json
        if ($secrets.azureDevOps.personalAccessToken) {
            return $secrets.azureDevOps.personalAccessToken
        }
    }
    
    Write-Log "Personal Access Token not found. Please configure OAuth or provide PAT." "WARN"
    $pat = Read-Host "Enter Azure DevOps Personal Access Token" -AsSecureString
    $patPlainText = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($pat))
    
    # Save for future use
    $secrets = @{
        "azureDevOps" = @{
            "personalAccessToken" = $patPlainText
        }
    }
    $secrets | ConvertTo-Json -Depth 10 | Set-Content -Path $secretsPath -Encoding UTF8
    
    return $patPlainText
}

function Start-Pipeline {
    param(
        [object]$Config,
        [string]$Branch = "main",
        [string]$Environment = ""
    )
    
    Write-Log "Starting pipeline for branch: $Branch" "INFO"
    
    $pat = Get-PersonalAccessToken
    $headers = Get-AzureDevOpsHeaders $pat
    
    $org = $Config.azureDevOps.organization
    $project = $Config.azureDevOps.project
    $pipelineId = $Config.azureDevOps.pipelineId
    
    $url = "https://dev.azure.com/$org/$project/_apis/pipelines/$pipelineId/runs?api-version=7.0"
    
    $body = @{
        "resources" = @{
            "repositories" = @{
                "self" = @{
                    "refName" = "refs/heads/$Branch"
                }
            }
        }
    }
    
    if ($Environment) {
        $body.variables = @{
            "DEPLOY_ENVIRONMENT" = @{
                "value" = $Environment
            }
        }
    }
    
    $bodyJson = $body | ConvertTo-Json -Depth 10
    
    try {
        $response = Invoke-AzureDevOpsApi -Url $url -Method "POST" -Headers $headers -Body $bodyJson
        
        Write-Log "Pipeline started successfully!" "SUCCESS"
        Write-Log "Build ID: $($response.id)" "INFO"
        Write-Log "Build URL: $($response._links.web.href)" "INFO"
        
        if ($Wait) {
            Wait-ForPipelineCompletion -Config $Config -BuildId $response.id
        }
        
        return $response
    } catch {
        Write-Log "Failed to start pipeline: $($_.Exception.Message)" "ERROR"
        throw
    }
}

function Get-PipelineStatus {
    param(
        [object]$Config,
        [string]$BuildId
    )
    
    $pat = Get-PersonalAccessToken
    $headers = Get-AzureDevOpsHeaders $pat
    
    $org = $Config.azureDevOps.organization
    $project = $Config.azureDevOps.project
    $pipelineId = $Config.azureDevOps.pipelineId
    
    $url = "https://dev.azure.com/$org/$project/_apis/pipelines/$pipelineId/runs/$BuildId?api-version=7.0"
    
    try {
        $response = Invoke-AzureDevOpsApi -Url $url -Headers $headers
        return $response
    } catch {
        Write-Log "Failed to get pipeline status: $($_.Exception.Message)" "ERROR"
        throw
    }
}

function Wait-ForPipelineCompletion {
    param(
        [object]$Config,
        [string]$BuildId
    )
    
    Write-Log "Waiting for pipeline completion..." "INFO"
    
    do {
        Start-Sleep -Seconds 30
        $status = Get-PipelineStatus -Config $Config -BuildId $BuildId
        
        Write-Log "Pipeline status: $($status.state)" "INFO"
        
        if ($status.state -eq "completed") {
            if ($status.result -eq "succeeded") {
                Write-Log "Pipeline completed successfully!" "SUCCESS"
            } else {
                Write-Log "Pipeline failed with result: $($status.result)" "ERROR"
            }
            break
        }
    } while ($status.state -in @("inProgress", "notStarted"))
}

function Show-PipelineMenu {
    param([object]$Config)
    
    Write-Host ""
    Write-ColorText "🎛️ Pipeline Management Menu" "Cyan"
    Write-Host ""
    Write-ColorText "    [1] 🚀 Trigger Pipeline" "Green"
    Write-ColorText "        └─ Start a new build and deployment"
    Write-Host ""
    Write-ColorText "    [2] 📊 Check Pipeline Status" "Blue"
    Write-ColorText "        └─ Monitor running or recent builds"
    Write-Host ""
    Write-ColorText "    [3] 🔄 Deploy Specific Build" "Yellow"
    Write-ColorText "        └─ Deploy a specific build to environment"
    Write-Host ""
    Write-ColorText "    [4] 🔐 Manage Secrets" "Magenta"
    Write-ColorText "        └─ Configure OAuth and access tokens"
    Write-Host ""
    Write-ColorText "    [5] 🖥️ Local Pipeline Emulator" "Cyan"
    Write-ColorText "        └─ Run pipeline steps locally"
    Write-Host ""
    Write-ColorText "    [6] ⚙️ Configuration" "Gray"
    Write-ColorText "        └─ Manage pipeline settings"
    Write-Host ""
    Write-ColorText "    [0] 🚪 Exit" "Gray"
    Write-Host ""
}

# Main execution
Show-Header

$config = Initialize-Configuration

if ($Action -eq "menu") {
    do {
        Show-PipelineMenu $config
        $choice = Read-Host "Enter your choice (0-6)"
        
        switch ($choice) {
            "1" {
                $branch = Read-Host "Enter branch name (default: main)"
                if (-not $branch) { $branch = "main" }
                
                $env = Read-Host "Enter environment (dev/staging/prod, optional)"
                
                try {
                    Start-Pipeline -Config $config -Branch $branch -Environment $env
                } catch {
                    Write-Log "Pipeline trigger failed" "ERROR"
                }
                
                Read-Host "Press Enter to continue"
            }
            "2" {
                $buildId = Read-Host "Enter Build ID"
                if ($buildId) {
                    try {
                        $status = Get-PipelineStatus -Config $config -BuildId $buildId
                        Write-Log "Build Status: $($status.state)" "INFO"
                        Write-Log "Build Result: $($status.result)" "INFO"
                        Write-Log "Build URL: $($status._links.web.href)" "INFO"
                    } catch {
                        Write-Log "Failed to get status" "ERROR"
                    }
                }
                Read-Host "Press Enter to continue"
            }
            "3" {
                Write-Log "Deploy specific build functionality coming soon..." "INFO"
                Read-Host "Press Enter to continue"
            }
            "4" {
                Write-Log "OAuth secrets management functionality coming soon..." "INFO"
                Read-Host "Press Enter to continue"
            }
            "5" {
                Write-Log "Local pipeline emulator functionality coming soon..." "INFO"
                Read-Host "Press Enter to continue"
            }
            "6" {
                Write-Log "Configuration management functionality coming soon..." "INFO"
                Read-Host "Press Enter to continue"
            }
            "0" {
                Write-Log "Goodbye!" "INFO"
                break
            }
            default {
                Write-Log "Invalid choice. Please try again." "WARN"
                Read-Host "Press Enter to continue"
            }
        }
    } while ($choice -ne "0")
} else {
    # Handle command line actions
    switch ($Action.ToLower()) {
        "trigger" {
            if (-not $Branch) { $Branch = "main" }
            Start-Pipeline -Config $config -Branch $Branch -Environment $Environment
        }
        "status" {
            if ($BuildId) {
                $status = Get-PipelineStatus -Config $config -BuildId $BuildId
                if ($Json) {
                    $status | ConvertTo-Json -Depth 10
                } else {
                    Write-Log "Build Status: $($status.state)" "INFO"
                    Write-Log "Build Result: $($status.result)" "INFO"
                }
            } else {
                Write-Log "BuildId parameter required for status action" "ERROR"
            }
        }
        default {
            Write-Log "Unknown action: $Action" "ERROR"
            Write-Log "Available actions: menu, trigger, status, logs, deploy, secrets" "INFO"
        }
    }
}
