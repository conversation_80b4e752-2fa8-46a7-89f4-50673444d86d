# 🔐 SSH Key Management Guide

Comprehensive guide for managing SSH keys in the Universal Pipeline Builder for secure FTP/SFTP deployments.

## 🎯 Overview

SSH keys provide a secure way to authenticate with remote servers without passwords. The Pipeline Builder includes comprehensive SSH key management capabilities for automated deployments.

## 🔑 SSH Key Types

### RSA Keys
- **Key Size**: 2048, 3072, or 4096 bits (recommended: 4096)
- **Security**: Widely supported, mature algorithm
- **Performance**: Slower than Ed25519 but compatible everywhere
- **Use Case**: Legacy systems, maximum compatibility

```bash
# Generate RSA key
ssh-keygen -t rsa -b 4096 -f ~/.ssh/production_rsa -C "production-deployment"
```

### Ed25519 Keys
- **Key Size**: Fixed 256 bits
- **Security**: Modern, highly secure algorithm
- **Performance**: Fast generation and verification
- **Use Case**: Modern systems, best security practices

```bash
# Generate Ed25519 key
ssh-keygen -t ed25519 -f ~/.ssh/production_ed25519 -C "production-deployment"
```

### ECDSA Keys
- **Key Size**: 256, 384, or 521 bits
- **Security**: Good security, elliptic curve cryptography
- **Performance**: Fast, smaller key sizes
- **Use Case**: Balance between security and performance

```bash
# Generate ECDSA key
ssh-keygen -t ecdsa -b 256 -f ~/.ssh/production_ecdsa -C "production-deployment"
```

## 🏗️ Pipeline Builder SSH Key Management

### Interactive Key Generation

```powershell
# Launch FTP/SFTP module
.\pipeline-builder.ps1 -Action sftp

# Select: [1] SSH Key Management
# Select: [1] Generate new SSH key pair

# Follow prompts:
# Key name: production_rsa
# Key type: rsa (or ed25519, ecdsa)
# Comment: Production deployment key
```

### Programmatic Key Generation

```powershell
# Generate key via PowerShell module
.\core\ftp-sftp-deployment.ps1 -Action generate-key -KeyName "staging_ed25519" -KeyType "ed25519"
```

## 📁 Key Storage Structure

```
pipeline-builder/ssh-keys/
├── production_rsa              # Private key
├── production_rsa.pub          # Public key
├── staging_ed25519             # Private key
├── staging_ed25519.pub         # Public key
├── dev_ecdsa                   # Private key
├── dev_ecdsa.pub               # Public key
└── .key-metadata.json          # Key metadata and rotation info
```

## 🔒 Security Best Practices

### Key Generation
1. **Use Strong Key Types**: Prefer Ed25519, then RSA 4096-bit
2. **Unique Keys per Environment**: Separate keys for dev/staging/prod
3. **Descriptive Comments**: Include purpose and environment in comments
4. **Secure Generation**: Generate keys on secure, trusted systems

### Key Storage
1. **Proper Permissions**: 
   - Private keys: `600` (owner read/write only)
   - Public keys: `644` (owner read/write, others read)
   - Key directory: `700` (owner access only)

2. **Encryption**: Consider encrypting private keys with passphrases
3. **Backup**: Secure backup of private keys
4. **Version Control**: Never commit private keys to version control

### Key Distribution
1. **Public Key Only**: Only distribute public keys to servers
2. **Authorized Keys**: Add public keys to `~/.ssh/authorized_keys` on target servers
3. **Principle of Least Privilege**: Grant minimal necessary access
4. **Regular Audits**: Review and remove unused keys

## 🔄 Key Rotation

### Automated Rotation

```json
{
  "sshKeys": {
    "security": {
      "keyRotationDays": 90,
      "rotationWarningDays": 7,
      "automaticRotation": true,
      "backupOldKeys": true
    }
  }
}
```

### Manual Rotation Process

1. **Generate New Key Pair**
```powershell
.\core\ftp-sftp-deployment.ps1 -Action generate-key -KeyName "production_rsa_new" -KeyType "rsa"
```

2. **Deploy New Public Key**
```bash
# Copy new public key to server
ssh-copy-id -i production_rsa_new.pub <EMAIL>

# Or manually add to authorized_keys
cat production_rsa_new.pub >> ~/.ssh/authorized_keys
```

3. **Test New Key**
```bash
# Test connection with new key
ssh -i production_rsa_new <EMAIL> "echo 'Connection successful'"
```

4. **Update Pipeline Configuration**
```json
{
  "environments": {
    "prod": {
      "sftp": {
        "keyFile": "production_rsa_new"
      }
    }
  }
}
```

5. **Remove Old Key**
```bash
# Remove old public key from server
# Edit ~/.ssh/authorized_keys and remove old key line

# Remove old key files locally (after backup)
rm production_rsa production_rsa.pub
```

## 🌐 Server Configuration

### SSH Server Setup

1. **Enable Public Key Authentication**
```bash
# /etc/ssh/sshd_config
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys
PasswordAuthentication no  # Disable password auth for security
```

2. **Configure User Account**
```bash
# Create deployment user
sudo useradd -m -s /bin/bash deploy
sudo usermod -aG www-data deploy

# Setup SSH directory
sudo -u deploy mkdir -p /home/<USER>/.ssh
sudo -u deploy chmod 700 /home/<USER>/.ssh
sudo -u deploy touch /home/<USER>/.ssh/authorized_keys
sudo -u deploy chmod 600 /home/<USER>/.ssh/authorized_keys
```

3. **Add Public Key**
```bash
# Add pipeline builder public key
echo "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQ... production-deployment" >> /home/<USER>/.ssh/authorized_keys
```

### Directory Permissions

```bash
# Web directory setup
sudo mkdir -p /var/www/production
sudo chown -R deploy:www-data /var/www/production
sudo chmod -R 755 /var/www/production

# Backup directory setup
sudo mkdir -p /var/backups/webapp
sudo chown -R deploy:deploy /var/backups/webapp
sudo chmod -R 750 /var/backups/webapp
```

## 🔧 Pipeline Integration

### Azure DevOps Service Connections

1. **Create SSH Service Connection**
   - Go to Project Settings → Service connections
   - New service connection → SSH
   - Configure:
     - Host name: `production-server.com`
     - Port: `22`
     - User name: `deploy`
     - Private key: Copy private key content
     - Passphrase: If key is encrypted

2. **Use in Pipeline**
```yaml
- task: SSH@0
  displayName: 'Deploy via SSH'
  inputs:
    sshEndpoint: 'Production-SSH'
    runOptions: 'commands'
    commands: |
      echo "Deployment commands here"
```

### GitHub Actions Secrets

1. **Add SSH Secrets**
   - Go to Repository → Settings → Secrets and variables → Actions
   - Add secrets:
     - `SSH_PRIVATE_KEY_PROD`: Private key content
     - `SSH_HOST_PROD`: Server hostname
     - `SSH_USER_PROD`: Username (deploy)

2. **Use in Workflow**
```yaml
- name: Setup SSH Key
  uses: webfactory/ssh-agent@v0.8.0
  with:
    ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY_PROD }}

- name: Deploy via SSH
  run: |
    ssh -o StrictHostKeyChecking=no ${{ secrets.SSH_USER_PROD }}@${{ secrets.SSH_HOST_PROD }} \
      "deployment commands here"
```

## 🛡️ Security Monitoring

### Key Usage Auditing

```bash
# Monitor SSH connections
sudo tail -f /var/log/auth.log | grep ssh

# Check last login times
lastlog -u deploy

# Monitor failed authentication attempts
sudo grep "Failed password" /var/log/auth.log
```

### Automated Security Checks

```powershell
# Check key permissions
.\core\ftp-sftp-deployment.ps1 -Action audit-keys

# Verify key integrity
.\core\ftp-sftp-deployment.ps1 -Action verify-keys

# Check for key rotation needs
.\core\ftp-sftp-deployment.ps1 -Action check-rotation
```

## 🚨 Incident Response

### Compromised Key Response

1. **Immediate Actions**
   - Remove compromised public key from all servers
   - Generate new key pair immediately
   - Update all pipeline configurations
   - Audit recent deployments and server access

2. **Investigation**
   - Review server logs for unauthorized access
   - Check deployment history for suspicious activities
   - Verify integrity of deployed applications

3. **Recovery**
   - Deploy new public key to all servers
   - Test all deployment pipelines
   - Update documentation and procedures
   - Conduct security review

### Emergency Access

```bash
# Emergency key generation (if pipeline builder unavailable)
ssh-keygen -t ed25519 -f emergency_key -C "emergency-access-$(date +%Y%m%d)"

# Quick server access setup
ssh-copy-id -i emergency_key.pub <EMAIL>
```

## 📊 Key Management Metrics

### Tracking Key Health

```json
{
  "keyMetrics": {
    "totalKeys": 12,
    "activeKeys": 8,
    "expiredKeys": 2,
    "rotationDue": 3,
    "lastRotation": "2024-01-15",
    "nextRotation": "2024-04-15"
  }
}
```

### Automated Reporting

```powershell
# Generate key status report
.\core\ftp-sftp-deployment.ps1 -Action generate-report -Format html

# Send rotation reminders
.\core\ftp-sftp-deployment.ps1 -Action check-rotation -Notify
```

## 🔍 Troubleshooting

### Common Issues

1. **Permission Denied**
   - Check private key permissions (should be 600)
   - Verify public key is in authorized_keys
   - Ensure SSH agent has the key loaded

2. **Host Key Verification Failed**
   - Add server to known_hosts: `ssh-keyscan server.com >> ~/.ssh/known_hosts`
   - Or disable strict checking: `ssh -o StrictHostKeyChecking=no`

3. **Key Not Found**
   - Verify key file path in configuration
   - Check key file exists and is readable
   - Ensure correct key name in pipeline settings

### Debug Commands

```bash
# Test SSH connection with verbose output
ssh -vvv -i private_key <EMAIL>

# Check SSH agent
ssh-add -l

# Verify public key format
ssh-keygen -l -f public_key.pub

# Test key authentication
ssh -o PasswordAuthentication=no -i private_key <EMAIL>
```

## 📚 Additional Resources

- [SSH Key Security Best Practices](https://docs.github.com/en/authentication/connecting-to-github-with-ssh)
- [OpenSSH Manual](https://man.openbsd.org/ssh-keygen)
- [Azure DevOps SSH Tasks](https://docs.microsoft.com/en-us/azure/devops/pipelines/tasks/deploy/ssh)
- [GitHub Actions SSH Setup](https://docs.github.com/en/actions/deployment/deploying-to-your-cloud-provider)

## 🆘 Support

For SSH key management issues:
- **Documentation**: [SSH Key Management Wiki](https://wiki.company.com/ssh-keys)
- **Support Channel**: #ssh-key-support
- **Emergency Contact**: <EMAIL>
- **Runbooks**: [SSH Emergency Procedures](https://runbooks.company.com/ssh-keys)
