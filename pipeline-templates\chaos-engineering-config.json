{"chaosEngineering": {"enabled": true, "description": "Comprehensive chaos engineering experiments to validate system resilience", "framework": "Chaos Toolkit", "version": "1.0", "title": "Notify Service API Resilience Testing", "tags": ["resilience", "microservices", "api", "kubernetes"], "configuration": {"kubernetes": {"namespace": "notify-service", "kubeconfig": "~/.kube/config"}, "monitoring": {"prometheus_url": "http://prometheus:9090", "grafana_url": "http://grafana:3000", "alert_manager_url": "http://alertmanager:9093"}}, "steady_state_hypothesis": {"title": "Application is healthy and responsive", "probes": [{"name": "health-check-probe", "type": "probe", "tolerance": {"type": "jsonpath", "path": "$.status", "expect": "healthy"}, "provider": {"type": "http", "url": "http://notify-service-api/health", "method": "GET", "timeout": 10, "headers": {"Accept": "application/json"}}}, {"name": "response-time-probe", "type": "probe", "tolerance": {"type": "range", "range": [0, 500]}, "provider": {"type": "http", "url": "http://notify-service-api/api/notifications", "method": "GET", "timeout": 10, "measure": "response_time_ms"}}, {"name": "error-rate-probe", "type": "probe", "tolerance": {"type": "range", "range": [0, 0.05]}, "provider": {"type": "prometheus", "query": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m])", "timeout": 30}}, {"name": "cpu-usage-probe", "type": "probe", "tolerance": {"type": "range", "range": [0, 80]}, "provider": {"type": "prometheus", "query": "avg(cpu_usage_percent{service=\"notify-service-api\"})", "timeout": 30}}]}, "experiments": {"network_latency": {"title": "Network Latency Injection", "description": "Inject network latency to test application resilience to slow network conditions", "enabled": true, "tags": ["network", "latency", "performance"], "method": [{"name": "inject-network-latency", "type": "action", "provider": {"type": "kubernetes", "module": "chaosk8s.pod.actions", "func": "exec_in_pods", "arguments": {"label_selector": "app=notify-service-api", "command": ["tc", "qdisc", "add", "dev", "eth0", "root", "netem", "delay", "100ms", "20ms"], "ns": "notify-service"}}, "pauses": {"after": 300}}], "rollbacks": [{"name": "remove-network-latency", "type": "action", "provider": {"type": "kubernetes", "module": "chaosk8s.pod.actions", "func": "exec_in_pods", "arguments": {"label_selector": "app=notify-service-api", "command": ["tc", "qdisc", "del", "dev", "eth0", "root"], "ns": "notify-service"}}}]}, "pod_failure": {"title": "Pod Failure Simulation", "description": "Randomly kill pods to test application resilience and recovery", "enabled": true, "tags": ["kubernetes", "pods", "failure"], "method": [{"name": "kill-random-pod", "type": "action", "provider": {"type": "kubernetes", "module": "chaosk8s.pod.actions", "func": "terminate_pods", "arguments": {"label_selector": "app=notify-service-api", "rand": true, "qty": 1, "ns": "notify-service"}}, "pauses": {"after": 60}}]}, "cpu_stress": {"title": "CPU Stress Test", "description": "Apply CPU stress to test application behavior under high CPU load", "enabled": true, "tags": ["cpu", "stress", "performance"], "method": [{"name": "apply-cpu-stress", "type": "action", "provider": {"type": "kubernetes", "module": "chaosk8s.pod.actions", "func": "exec_in_pods", "arguments": {"label_selector": "app=notify-service-api", "command": ["stress-ng", "--cpu", "2", "--timeout", "300s", "--cpu-load", "80"], "ns": "notify-service"}}, "pauses": {"after": 300}}]}, "memory_stress": {"title": "Memory Stress Test", "description": "Apply memory pressure to test application memory management", "enabled": true, "tags": ["memory", "stress", "performance"], "method": [{"name": "apply-memory-stress", "type": "action", "provider": {"type": "kubernetes", "module": "chaosk8s.pod.actions", "func": "exec_in_pods", "arguments": {"label_selector": "app=notify-service-api", "command": ["stress-ng", "--vm", "1", "--vm-bytes", "512M", "--timeout", "300s"], "ns": "notify-service"}}, "pauses": {"after": 300}}]}, "database_failure": {"title": "Database Connection Failure", "description": "Simulate database connectivity issues", "enabled": true, "tags": ["database", "connectivity", "failure"], "method": [{"name": "block-database-traffic", "type": "action", "provider": {"type": "kubernetes", "module": "chaosk8s.networking.actions", "func": "deny_all_ingress_traffic", "arguments": {"label_selector": "app=postgresql", "ns": "notify-service"}}, "pauses": {"after": 120}}], "rollbacks": [{"name": "restore-database-traffic", "type": "action", "provider": {"type": "kubernetes", "module": "chaosk8s.networking.actions", "func": "remove_deny_all_ingress_traffic", "arguments": {"label_selector": "app=postgresql", "ns": "notify-service"}}}]}, "disk_pressure": {"title": "Disk Space Exhaustion", "description": "Fill up disk space to test application behavior under storage pressure", "enabled": false, "tags": ["disk", "storage", "pressure"], "method": [{"name": "fill-disk-space", "type": "action", "provider": {"type": "kubernetes", "module": "chaosk8s.pod.actions", "func": "exec_in_pods", "arguments": {"label_selector": "app=notify-service-api", "command": ["dd", "if=/dev/zero", "of=/tmp/largefile", "bs=1M", "count=1000"], "ns": "notify-service"}}, "pauses": {"after": 180}}], "rollbacks": [{"name": "cleanup-disk-space", "type": "action", "provider": {"type": "kubernetes", "module": "chaosk8s.pod.actions", "func": "exec_in_pods", "arguments": {"label_selector": "app=notify-service-api", "command": ["rm", "-f", "/tmp/largefile"], "ns": "notify-service"}}}]}, "service_mesh_failure": {"title": "Service Mesh Failure", "description": "Simulate service mesh (Istio) failures and traffic routing issues", "enabled": false, "tags": ["istio", "service-mesh", "networking"], "method": [{"name": "inject-http-fault", "type": "action", "provider": {"type": "kubernetes", "module": "chaosistio.fault.actions", "func": "add_fault_injection", "arguments": {"virtual_service_name": "notify-service-vs", "routes": [{"destination": "notify-service-api", "fault": {"delay": {"percentage": {"value": 50}, "fixed_delay": "5s"}, "abort": {"percentage": {"value": 10}, "http_status": 503}}}], "ns": "notify-service"}}, "pauses": {"after": 300}}], "rollbacks": [{"name": "remove-http-fault", "type": "action", "provider": {"type": "kubernetes", "module": "chaosistio.fault.actions", "func": "remove_fault_injection", "arguments": {"virtual_service_name": "notify-service-vs", "ns": "notify-service"}}}]}}, "scheduling": {"enabled": false, "cron_expression": "0 2 * * 1", "timezone": "UTC", "environments": ["staging"], "notifications": {"slack": {"enabled": true, "webhook_url": "${SLACK_WEBHOOK_URL}", "channel": "#chaos-engineering"}, "email": {"enabled": false, "recipients": ["<EMAIL>"]}}}, "reporting": {"enabled": true, "formats": ["json", "html"], "storage": {"type": "s3", "bucket": "chaos-engineering-reports", "prefix": "notify-service/"}, "retention_days": 90}, "safety": {"abort_conditions": [{"name": "high-error-rate", "condition": "error_rate > 0.1", "description": "Abort if error rate exceeds 10%"}, {"name": "low-availability", "condition": "availability < 0.95", "description": "Abort if availability drops below 95%"}, {"name": "response-time-degradation", "condition": "response_time_p95 > 2000", "description": "Abort if 95th percentile response time exceeds 2 seconds"}], "circuit_breaker": {"enabled": true, "failure_threshold": 3, "recovery_timeout": "5m"}}, "environments": {"staging": {"enabled": true, "experiments": ["network_latency", "pod_failure", "cpu_stress", "memory_stress", "database_failure"], "schedule": "0 2 * * 1-5"}, "production": {"enabled": false, "experiments": ["network_latency"], "schedule": "0 3 * * 6", "approval_required": true, "approvers": ["sre-team", "platform-team"]}}}}