using Data.Mapping;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Reflection;

namespace Data.Contexts;

public class ApplicationDbContext : DbContext, IDbContext
{
    // Register entity mappings dynamically
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        RegisterEntityMapping(modelBuilder);

        // Configure DateTime properties for PostgreSQL compatibility
        ConfigureDateTimeProperties(modelBuilder);

        base.OnModelCreating(modelBuilder);
    }

    private static void ConfigureDateTimeProperties(ModelBuilder modelBuilder)
    {
        // Configure all DateTime properties to use timestamp with time zone for PostgreSQL
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            foreach (var property in entityType.GetProperties())
            {
                if (property.ClrType == typeof(DateTime) || property.ClrType == typeof(DateTime?))
                {
                    property.SetColumnType("timestamp with time zone");
                }
            }
        }
    }

    public void RegisterEntityMapping(ModelBuilder modelBuilder)
    {
        var typeConfigurations = Assembly.GetExecutingAssembly().GetTypes().Where(type =>
            // Dynamically find all mapping configurations
            (type.BaseType?.IsGenericType ?? false) &&
            (type.BaseType.GetGenericTypeDefinition() == typeof(MappingEntityTypeConfiguration<>))
        );
        foreach (var item in typeConfigurations)
        {
            var configuration = (IMappingConfiguration)Activator.CreateInstance(item);
            configuration!.ApplyConfiguration(modelBuilder);
        }
    }

    public new virtual DbSet<TEntity> Set<TEntity>() where TEntity : class
    {
        return base.Set<TEntity>();
    }
}