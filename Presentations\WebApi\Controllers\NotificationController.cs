using Microsoft.AspNetCore.Mvc;
using Models.DTOs.Push;
using Models.DTOs.WhatsApp;
using Services.Concrete;
using System.Threading;
using System.Threading.Tasks;
using Services.Interfaces;

namespace WebApi.Controllers;

[Route("api/[controller]")]
[ApiController]
public class NotificationController : ControllerBase
{
    private readonly IMessageService<WhatsAppPayload, WhatsAppResult> _whatsAppService;
    private readonly PushNotificationService _pushNotificationService;

    public NotificationController(
        IMessageService<WhatsAppPayload, WhatsAppResult> whatsAppService,
        PushNotificationService pushNotificationService)
    {
        _whatsAppService = whatsAppService;
        _pushNotificationService = pushNotificationService; // Simplified for optional generics
    }

    [HttpPost("whatsapp/send")]
    public async Task<IActionResult> SendWhatsAppMessage([FromBody] WhatsAppPayload payload, CancellationToken cancellationToken)
    {
        var result = await _whatsAppService.SendAsync(payload, cancellationToken);
        return Ok(result);
    }

    [HttpPost("push/send")]
    public async Task<IActionResult> SendPushNotification([FromBody] PushNotificationPayload payload, CancellationToken cancellationToken)
    {
        var result = await _pushNotificationService.SendNotificationAsync(payload, cancellationToken);
        return Ok(result);
    }
}