# Docker Compose for Test Dependencies
# Generated by Pipeline Builder for acceptance testing

version: '3.8'

services:
  # PostgreSQL Test Database
  postgres-test:
    image: postgres:15-alpine
    container_name: notify-postgres-test
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: test_password
      POSTGRES_DB: notify_test_db
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    ports:
      - "5433:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./test-data/init-test-db.sql:/docker-entrypoint-initdb.d/init-test-db.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d notify_test_db"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - test-network
    restart: unless-stopped

  # Redis Test Cache
  redis-test:
    image: redis:7-alpine
    container_name: notify-redis-test
    command: redis-server --requirepass test_password --appendonly yes
    ports:
      - "6380:6379"
    volumes:
      - redis_test_data:/data
      - ./test-data/redis.conf:/usr/local/etc/redis/redis.conf:ro
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s
    networks:
      - test-network
    restart: unless-stopped

  # Elasticsearch for Search Testing (Optional)
  elasticsearch-test:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: notify-elasticsearch-test
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - bootstrap.memory_lock=true
    ports:
      - "9201:9200"
      - "9301:9300"
    volumes:
      - elasticsearch_test_data:/usr/share/elasticsearch/data
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - test-network
    restart: unless-stopped
    ulimits:
      memlock:
        soft: -1
        hard: -1

  # RabbitMQ for Message Queue Testing (Optional)
  rabbitmq-test:
    image: rabbitmq:3.12-management-alpine
    container_name: notify-rabbitmq-test
    environment:
      RABBITMQ_DEFAULT_USER: test_user
      RABBITMQ_DEFAULT_PASS: test_password
      RABBITMQ_DEFAULT_VHOST: test_vhost
    ports:
      - "5673:5672"
      - "15673:15672"
    volumes:
      - rabbitmq_test_data:/var/lib/rabbitmq
      - ./test-data/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - test-network
    restart: unless-stopped

  # MinIO for S3-compatible Storage Testing (Optional)
  minio-test:
    image: minio/minio:latest
    container_name: notify-minio-test
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: test_access_key
      MINIO_ROOT_PASSWORD: test_secret_key
    ports:
      - "9001:9000"
      - "9091:9001"
    volumes:
      - minio_test_data:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - test-network
    restart: unless-stopped

  # Jaeger for Distributed Tracing Testing (Optional)
  jaeger-test:
    image: jaegertracing/all-in-one:1.50
    container_name: notify-jaeger-test
    environment:
      COLLECTOR_OTLP_ENABLED: true
    ports:
      - "16687:16686"  # Jaeger UI
      - "14269:14268"  # Jaeger collector HTTP
      - "14251:14250"  # Jaeger collector gRPC
      - "6832:6831/udp"  # Jaeger agent UDP
    networks:
      - test-network
    restart: unless-stopped

  # Prometheus for Metrics Testing (Optional)
  prometheus-test:
    image: prom/prometheus:v2.47.0
    container_name: notify-prometheus-test
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=1h'
      - '--web.enable-lifecycle'
    ports:
      - "9091:9090"
    volumes:
      - ./test-data/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_test_data:/prometheus
    networks:
      - test-network
    restart: unless-stopped

  # Test Application Instance
  notify-api-test:
    build:
      context: ..
      dockerfile: Dockerfile
      target: test
    container_name: notify-api-test
    environment:
      ASPNETCORE_ENVIRONMENT: Testing
      ASPNETCORE_URLS: http://+:80
      ConnectionStrings__DefaultConnection: "Host=postgres-test;Port=5432;Database=notify_test_db;Username=postgres;Password=test_password"
      ConnectionStrings__Redis: "redis-test:6379,password=test_password"
      Logging__LogLevel__Default: Information
      Logging__LogLevel__Microsoft: Warning
      Logging__LogLevel__Microsoft.Hosting.Lifetime: Information
    ports:
      - "5001:80"
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - test-network
    restart: unless-stopped

  # Selenium Hub for E2E Testing (Optional)
  selenium-hub:
    image: selenium/hub:4.15.0
    container_name: notify-selenium-hub
    ports:
      - "4444:4444"
      - "4442:4442"
      - "4443:4443"
    environment:
      GRID_MAX_SESSION: 16
      GRID_BROWSER_TIMEOUT: 300
      GRID_TIMEOUT: 300
    networks:
      - test-network
    restart: unless-stopped

  # Chrome Node for Selenium
  selenium-chrome:
    image: selenium/node-chrome:4.15.0
    container_name: notify-selenium-chrome
    shm_size: 2gb
    depends_on:
      - selenium-hub
    environment:
      HUB_HOST: selenium-hub
      HUB_PORT: 4444
      NODE_MAX_INSTANCES: 4
      NODE_MAX_SESSION: 4
    volumes:
      - /dev/shm:/dev/shm
    networks:
      - test-network
    restart: unless-stopped

  # Firefox Node for Selenium
  selenium-firefox:
    image: selenium/node-firefox:4.15.0
    container_name: notify-selenium-firefox
    shm_size: 2gb
    depends_on:
      - selenium-hub
    environment:
      HUB_HOST: selenium-hub
      HUB_PORT: 4444
      NODE_MAX_INSTANCES: 4
      NODE_MAX_SESSION: 4
    volumes:
      - /dev/shm:/dev/shm
    networks:
      - test-network
    restart: unless-stopped

  # Test Data Seeder
  test-data-seeder:
    build:
      context: ..
      dockerfile: Dockerfile.test-seeder
    container_name: notify-test-seeder
    environment:
      ConnectionStrings__DefaultConnection: "Host=postgres-test;Port=5432;Database=notify_test_db;Username=postgres;Password=test_password"
      ConnectionStrings__Redis: "redis-test:6379,password=test_password"
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    networks:
      - test-network
    restart: "no"

volumes:
  postgres_test_data:
    driver: local
  redis_test_data:
    driver: local
  elasticsearch_test_data:
    driver: local
  rabbitmq_test_data:
    driver: local
  minio_test_data:
    driver: local
  prometheus_test_data:
    driver: local

networks:
  test-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
