﻿using Core.Exceptions;
using GraphQL;
using GraphQL.Types;
using Microsoft.AspNetCore.Mvc;
using Models.ResponseModels;
using System;
using System.Net;
using System.Threading.Tasks;
using WebApi.GraphQL;

namespace WebApi.Controllers;

[Route("api/[controller]")]
//[Authorize]
public class GraphQLController(IDocumentExecuter documentExecuter, ISchema schema) : ControllerBase
{
    [HttpPost]
    public async Task<IActionResult> Post([FromBody] GraphQlQuery query)
    {
        if (query == null)
        {
            throw new Exception(nameof(query));
        }
        try
        {
            //_ = query.Variables.ToInputs();
            var executionOptions = new ExecutionOptions
            {
                Schema = schema,
                Query = query.Query,
                // Inputs = inputs
            };
            var result = await documentExecuter
                .ExecuteAsync(executionOptions);

            if (result.Errors?.Count > 0)
                return BadRequest(result);
            return Ok(new BaseResponse<object>(result.Data, "Graph result"));
        }
        catch (Exception ex)
        {
            throw new ApiException($"{ex.Message}") { StatusCode = (int)HttpStatusCode.InternalServerError };
        }   
    }
}