# ═══════════════════════════════════════════════════════════════════════════════
# 🏗️ Universal Pipeline Builder
# ═══════════════════════════════════════════════════════════════════════════════
# Generate Azure DevOps Pipelines and GitHub Actions based on repository analysis

param(
    [string]$Action = "menu",           # menu, analyze, build, validate, export
    [string]$Platform = "",             # azuredevops, github, both
    [string]$ProjectType = "",          # auto, dotnet, node, python, docker
    [string]$OutputPath = "",           # Output directory for generated files
    [string]$ConfigFile = "",           # Custom configuration file
    [switch]$Interactive,               # Interactive mode
    [switch]$Force,                     # Overwrite existing files
    [switch]$Validate,                  # Validate generated pipelines
    [switch]$Json                       # JSON output
)

# Configuration
$ProjectRoot = Split-Path -Parent (Split-Path -Parent $MyInvocation.MyCommand.Path)
$BuilderConfigFile = Join-Path $ProjectRoot "pipeline-builder-config.json"
$TemplatesDir = Join-Path $ProjectRoot "pipeline-templates"
$OutputDir = if ($OutputPath) { $OutputPath } else { Join-Path $ProjectRoot "generated-pipelines" }

# Ensure directories exist
@($TemplatesDir, $OutputDir) | ForEach-Object {
    if (-not (Test-Path $_)) {
        New-Item -ItemType Directory -Path $_ | Out-Null
    }
}

# Colors
$Colors = @{
    Red = "Red"; Green = "Green"; Yellow = "Yellow"; Blue = "Blue"
    Cyan = "Cyan"; Magenta = "Magenta"; Gray = "Gray"; White = "White"
}

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    if (-not $Json) { Write-Host $Text -ForegroundColor $Colors[$Color] }
}

function Write-BuilderLog {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    if (-not $Json) {
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            default { "Cyan" }
        }
        Write-ColorText "[$timestamp] [$Level] $Message" $color
    }
}

function Show-Header {
    if ($Json) { return }
    
    Clear-Host
    Write-Host ""
    Write-ColorText "    🏗️🏗️🏗️ UNIVERSAL PIPELINE BUILDER 🏗️🏗️🏗️" "Cyan"
    Write-Host ""
    Write-ColorText "    🚀 Azure DevOps • GitHub Actions • Smart Detection" "Yellow"
    Write-ColorText "    🔧 Auto-Analysis • Templates • Validation" "Gray"
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "Gray"
}

function Initialize-BuilderConfiguration {
    Write-BuilderLog "Initializing pipeline builder configuration..." "INFO"
    
    if (-not (Test-Path $BuilderConfigFile)) {
        $defaultConfig = @{
            "projectTypes" = @{
                "dotnet" = @{
                    "name" = ".NET Application"
                    "detectionFiles" = @("*.csproj", "*.sln", "global.json")
                    "buildCommands" = @("dotnet restore", "dotnet build", "dotnet test", "dotnet publish")
                    "dockerSupport" = $true
                    "testFrameworks" = @("xunit", "nunit", "mstest")
                }
                "node" = @{
                    "name" = "Node.js Application"
                    "detectionFiles" = @("package.json", "yarn.lock", "package-lock.json")
                    "buildCommands" = @("npm install", "npm run build", "npm test")
                    "dockerSupport" = $true
                    "testFrameworks" = @("jest", "mocha", "jasmine")
                }
                "python" = @{
                    "name" = "Python Application"
                    "detectionFiles" = @("requirements.txt", "setup.py", "pyproject.toml", "Pipfile")
                    "buildCommands" = @("pip install -r requirements.txt", "python -m pytest")
                    "dockerSupport" = $true
                    "testFrameworks" = @("pytest", "unittest", "nose")
                }
                "docker" = @{
                    "name" = "Docker Application"
                    "detectionFiles" = @("Dockerfile", "docker-compose.yml", "docker-compose.yaml")
                    "buildCommands" = @("docker build", "docker push")
                    "dockerSupport" = $true
                }
            }
            "deploymentTargets" = @{
                "azure" = @{
                    "name" = "Azure"
                    "services" = @("webapp", "containerinstances", "kubernetes", "functions")
                    "authMethods" = @("serviceprincipal", "managedidentity")
                }
                "aws" = @{
                    "name" = "Amazon Web Services"
                    "services" = @("ec2", "ecs", "lambda", "beanstalk")
                    "authMethods" = @("accesskey", "role")
                }
                "gcp" = @{
                    "name" = "Google Cloud Platform"
                    "services" = @("compute", "run", "functions", "kubernetes")
                    "authMethods" = @("serviceaccount", "workloadidentity")
                }
                "kubernetes" = @{
                    "name" = "Kubernetes"
                    "services" = @("deployment", "service", "ingress")
                    "authMethods" = @("kubeconfig", "token")
                }
                "docker" = @{
                    "name" = "Docker Registry"
                    "services" = @("registry", "hub")
                    "authMethods" = @("username", "token")
                }
            }
            "platforms" = @{
                "azuredevops" = @{
                    "name" = "Azure DevOps"
                    "fileName" = "azure-pipelines.yml"
                    "syntax" = "yaml"
                    "features" = @("stages", "jobs", "steps", "variables", "templates")
                }
                "github" = @{
                    "name" = "GitHub Actions"
                    "fileName" = ".github/workflows/ci-cd.yml"
                    "syntax" = "yaml"
                    "features" = @("jobs", "steps", "matrix", "secrets", "environments")
                }
            }
            "conditions" = @{
                "branches" = @("main", "master", "develop", "release/*", "feature/*")
                "triggers" = @("push", "pull_request", "schedule", "manual")
                "environments" = @("dev", "staging", "prod")
            }
            "testing" = @{
                "acceptance" = @{
                    "enabled" = $true
                    "codeCoverage" = @{
                        "minimum" = 80
                        "format" = "cobertura"
                        "excludePatterns" = @("**/bin/**", "**/obj/**", "**/wwwroot/**")
                        "failBelowThreshold" = $true
                    }
                    "benchmarks" = @{
                        "enabled" = $true
                        "framework" = "BenchmarkDotNet"
                        "thresholds" = @{
                            "maxExecutionTime" = 5000
                            "maxMemoryUsage" = 100
                            "performanceRegression" = 10
                        }
                        "compareBaseline" = $true
                    }
                    "integrationTests" = @{
                        "enabled" = $true
                        "testContainers" = $true
                        "databaseTests" = $true
                        "apiTests" = $true
                    }
                    "e2eTests" = @{
                        "enabled" = $false
                        "framework" = "Playwright"
                        "browsers" = @("chromium", "firefox", "webkit")
                        "parallel" = $true
                    }
                }
                "qualityGates" = @{
                    "sonarQube" = @{
                        "enabled" = $false
                        "qualityGate" = "Sonar way"
                        "coverage" = 80
                        "duplicatedLines" = 3
                        "maintainabilityRating" = "A"
                        "reliabilityRating" = "A"
                        "securityRating" = "A"
                    }
                    "codeClimate" = @{
                        "enabled" = $false
                        "maintainabilityThreshold" = "A"
                        "testCoverageThreshold" = 80
                    }
                }
            }
        }
        
        $defaultConfig | ConvertTo-Json -Depth 10 | Set-Content -Path $BuilderConfigFile -Encoding UTF8
        Write-BuilderLog "Default configuration created" "SUCCESS"
    }
    
    return Get-Content $BuilderConfigFile | ConvertFrom-Json
}

function Analyze-Repository {
    Write-BuilderLog "Analyzing repository structure..." "INFO"
    
    $analysis = @{
        "projectType" = "unknown"
        "detectedFiles" = @()
        "hasDocker" = $false
        "hasTests" = $false
        "deploymentHints" = @()
        "frameworks" = @()
        "languages" = @()
    }
    
    # Get all files in the repository
    $allFiles = Get-ChildItem -Path $ProjectRoot -Recurse -File | Where-Object { 
        $_.FullName -notlike "*\.git\*" -and 
        $_.FullName -notlike "*\node_modules\*" -and
        $_.FullName -notlike "*\bin\*" -and
        $_.FullName -notlike "*\obj\*"
    }
    
    $config = Get-Content $BuilderConfigFile | ConvertFrom-Json
    
    # Detect project type
    foreach ($projectType in $config.projectTypes.PSObject.Properties) {
        $typeConfig = $projectType.Value
        $detectionFiles = $typeConfig.detectionFiles
        
        foreach ($pattern in $detectionFiles) {
            $matchingFiles = $allFiles | Where-Object { $_.Name -like $pattern }
            if ($matchingFiles) {
                $analysis.projectType = $projectType.Name
                $analysis.detectedFiles += $matchingFiles.Name
                Write-BuilderLog "Detected project type: $($typeConfig.name)" "SUCCESS"
                break
            }
        }
        
        if ($analysis.projectType -ne "unknown") { break }
    }
    
    # Check for Docker
    $dockerFiles = $allFiles | Where-Object { $_.Name -in @("Dockerfile", "docker-compose.yml", "docker-compose.yaml") }
    if ($dockerFiles) {
        $analysis.hasDocker = $true
        $analysis.deploymentHints += "docker"
        Write-BuilderLog "Docker support detected" "INFO"
    }
    
    # Check for tests
    $testPatterns = @("*test*", "*spec*", "*Test*", "*Spec*")
    $testFiles = $allFiles | Where-Object { 
        $testPatterns | ForEach-Object { $_.Name -like $_ } | Where-Object { $_ }
    }
    if ($testFiles) {
        $analysis.hasTests = $true
        Write-BuilderLog "Test files detected" "INFO"
    }
    
    # Detect deployment hints
    $deploymentFiles = @{
        "azure" = @("azure-pipelines.yml", "azuredeploy.json", "*.bicep")
        "aws" = @("cloudformation.yml", "serverless.yml", "sam.yml")
        "kubernetes" = @("*.yaml", "*.yml") | Where-Object { $allFiles | Where-Object { $_.Content -like "*apiVersion*" -and $_.Content -like "*kind:*" } }
    }
    
    foreach ($target in $deploymentFiles.Keys) {
        $patterns = $deploymentFiles[$target]
        foreach ($pattern in $patterns) {
            if ($allFiles | Where-Object { $_.Name -like $pattern }) {
                $analysis.deploymentHints += $target
                break
            }
        }
    }
    
    # Detect languages and frameworks
    $languageFiles = @{
        "csharp" = @("*.cs", "*.csproj")
        "javascript" = @("*.js", "package.json")
        "typescript" = @("*.ts", "tsconfig.json")
        "python" = @("*.py", "requirements.txt")
        "java" = @("*.java", "pom.xml", "build.gradle")
        "go" = @("*.go", "go.mod")
        "rust" = @("*.rs", "Cargo.toml")
    }
    
    foreach ($lang in $languageFiles.Keys) {
        $patterns = $languageFiles[$lang]
        foreach ($pattern in $patterns) {
            if ($allFiles | Where-Object { $_.Name -like $pattern }) {
                $analysis.languages += $lang
                break
            }
        }
    }
    
    return $analysis
}

function Get-UserPreferences {
    Write-BuilderLog "Gathering user preferences..." "INFO"
    
    $preferences = @{
        "platforms" = @()
        "environments" = @()
        "deploymentTargets" = @()
        "triggers" = @()
        "branches" = @()
        "features" = @()
    }
    
    if ($Interactive) {
        Write-Host ""
        Write-ColorText "🎯 Pipeline Configuration Wizard" "Cyan"
        Write-Host ""
        
        # Platform selection
        Write-ColorText "Select target platforms:" "Yellow"
        Write-ColorText "  [1] Azure DevOps only" "White"
        Write-ColorText "  [2] GitHub Actions only" "White"
        Write-ColorText "  [3] Both platforms" "White"
        $platformChoice = Read-Host "Enter choice (1-3)"
        
        switch ($platformChoice) {
            "1" { $preferences.platforms = @("azuredevops") }
            "2" { $preferences.platforms = @("github") }
            "3" { $preferences.platforms = @("azuredevops", "github") }
            default { $preferences.platforms = @("azuredevops") }
        }
        
        # Environment selection
        Write-Host ""
        Write-ColorText "Select deployment environments (comma-separated):" "Yellow"
        Write-ColorText "Available: dev, staging, prod" "Gray"
        $envInput = Read-Host "Environments"
        if ($envInput) {
            $preferences.environments = $envInput -split "," | ForEach-Object { $_.Trim() }
        } else {
            $preferences.environments = @("dev", "staging", "prod")
        }
        
        # Deployment targets
        Write-Host ""
        Write-ColorText "Select deployment targets:" "Yellow"
        Write-ColorText "  [1] Azure (Web Apps, Container Instances)" "White"
        Write-ColorText "  [2] AWS (EC2, ECS, Lambda)" "White"
        Write-ColorText "  [3] Docker Registry" "White"
        Write-ColorText "  [4] Kubernetes" "White"
        Write-ColorText "  [5] FTP/FTPS Server" "White"
        Write-ColorText "  [6] SFTP/SCP Server" "White"
        Write-ColorText "  [7] Rsync Server" "White"
        Write-ColorText "  [8] Multiple targets" "White"
        $targetChoice = Read-Host "Enter choice (1-8)"
        
        switch ($targetChoice) {
            "1" { $preferences.deploymentTargets = @("azure") }
            "2" { $preferences.deploymentTargets = @("aws") }
            "3" { $preferences.deploymentTargets = @("docker") }
            "4" { $preferences.deploymentTargets = @("kubernetes") }
            "5" { $preferences.deploymentTargets = @("ftp") }
            "6" { $preferences.deploymentTargets = @("sftp") }
            "7" { $preferences.deploymentTargets = @("rsync") }
            "8" {
                $targetsInput = Read-Host "Enter targets (comma-separated): azure, aws, docker, kubernetes, ftp, sftp, rsync"
                $preferences.deploymentTargets = $targetsInput -split "," | ForEach-Object { $_.Trim() }
            }
            default { $preferences.deploymentTargets = @("azure") }
        }
        
        # Triggers
        Write-Host ""
        Write-ColorText "Select pipeline triggers:" "Yellow"
        Write-ColorText "Available: push, pull_request, schedule, manual" "Gray"
        $triggersInput = Read-Host "Triggers (comma-separated)"
        if ($triggersInput) {
            $preferences.triggers = $triggersInput -split "," | ForEach-Object { $_.Trim() }
        } else {
            $preferences.triggers = @("push", "pull_request")
        }
        
        # Branch patterns
        Write-Host ""
        Write-ColorText "Enter branch patterns for triggers:" "Yellow"
        Write-ColorText "Default: main, develop, release/*, feature/*" "Gray"
        $branchesInput = Read-Host "Branch patterns (comma-separated)"
        if ($branchesInput) {
            $preferences.branches = $branchesInput -split "," | ForEach-Object { $_.Trim() }
        } else {
            $preferences.branches = @("main", "develop", "release/*", "feature/*")
        }
        
        # Testing configuration
        Write-Host ""
        Write-ColorText "🧪 Testing Configuration:" "Yellow"

        # Code coverage settings
        Write-ColorText "Configure code coverage requirements:" "White"
        $coverageInput = Read-Host "Minimum code coverage percentage (default: 80)"
        $coverageThreshold = if ($coverageInput) { [int]$coverageInput } else { 80 }
        $preferences.testing = @{
            "codeCoverage" = @{
                "minimum" = $coverageThreshold
                "failBelowThreshold" = $true
            }
        }

        # Benchmark testing
        Write-ColorText "Enable benchmark/performance testing? (y/N)" "White"
        $benchmarkInput = Read-Host
        $enableBenchmarks = $benchmarkInput -eq 'y' -or $benchmarkInput -eq 'Y'

        if ($enableBenchmarks) {
            Write-ColorText "Configure benchmark thresholds:" "White"
            $maxExecTime = Read-Host "Maximum execution time (ms, default: 5000)"
            $maxMemory = Read-Host "Maximum memory usage (MB, default: 100)"
            $regressionThreshold = Read-Host "Performance regression threshold (%, default: 10)"

            $preferences.testing.benchmarks = @{
                "enabled" = $true
                "maxExecutionTime" = if ($maxExecTime) { [int]$maxExecTime } else { 5000 }
                "maxMemoryUsage" = if ($maxMemory) { [int]$maxMemory } else { 100 }
                "regressionThreshold" = if ($regressionThreshold) { [int]$regressionThreshold } else { 10 }
            }
        }

        # Integration and E2E testing
        Write-ColorText "Enable integration testing? (Y/n)" "White"
        $integrationInput = Read-Host
        $enableIntegration = $integrationInput -ne 'n' -and $integrationInput -ne 'N'

        if ($enableIntegration) {
            $preferences.testing.integration = @{
                "enabled" = $true
                "testContainers" = $true
                "databaseTests" = $true
                "apiTests" = $true
            }
        }

        Write-ColorText "Enable end-to-end testing? (y/N)" "White"
        $e2eInput = Read-Host
        $enableE2E = $e2eInput -eq 'y' -or $e2eInput -eq 'Y'

        if ($enableE2E) {
            Write-ColorText "Select E2E framework:" "White"
            Write-ColorText "  [1] Playwright" "White"
            Write-ColorText "  [2] Selenium" "White"
            Write-ColorText "  [3] Cypress" "White"
            $e2eFramework = Read-Host "Framework choice (1-3)"

            $frameworkName = switch ($e2eFramework) {
                "1" { "Playwright" }
                "2" { "Selenium" }
                "3" { "Cypress" }
                default { "Playwright" }
            }

            $preferences.testing.e2e = @{
                "enabled" = $true
                "framework" = $frameworkName
                "browsers" = @("chromium", "firefox", "webkit")
                "parallel" = $true
            }
        }

        # Quality gates
        Write-Host ""
        Write-ColorText "🎯 Quality Gates:" "Yellow"
        Write-ColorText "Enable SonarQube quality gate? (y/N)" "White"
        $sonarInput = Read-Host
        $enableSonar = $sonarInput -eq 'y' -or $sonarInput -eq 'Y'

        if ($enableSonar) {
            $preferences.testing.sonarQube = @{
                "enabled" = $true
                "coverage" = $coverageThreshold
                "duplicatedLines" = 3
                "maintainabilityRating" = "A"
                "reliabilityRating" = "A"
                "securityRating" = "A"
            }
        }

        # Additional features
        Write-Host ""
        Write-ColorText "Select additional features:" "Yellow"
        Write-ColorText "  [1] Code quality checks (CodeQL)" "White"
        Write-ColorText "  [2] Security scanning (SAST/DAST)" "White"
        Write-ColorText "  [3] Dependency vulnerability scanning" "White"
        Write-ColorText "  [4] Deployment approvals" "White"
        Write-ColorText "  [5] Notifications (Slack, Teams)" "White"
        Write-ColorText "  [6] Acceptance testing gates" "White"
        Write-ColorText "  [7] Advanced deployment strategies (Canary/Blue-Green)" "White"
        Write-ColorText "  [8] Chaos engineering" "White"
        Write-ColorText "  [9] Observability stack (Prometheus, Grafana, Jaeger)" "White"
        Write-ColorText "  [10] SLI/SLO monitoring" "White"
        $featuresInput = Read-Host "Features (comma-separated numbers, e.g., 1,2,6,9)"

        if ($featuresInput) {
            $featureNumbers = $featuresInput -split "," | ForEach-Object { $_.Trim() }
            foreach ($num in $featureNumbers) {
                switch ($num) {
                    "1" { $preferences.features += "code-quality" }
                    "2" { $preferences.features += "security-scanning" }
                    "3" { $preferences.features += "dependency-scanning" }
                    "4" { $preferences.features += "deployment-approvals" }
                    "5" { $preferences.features += "notifications" }
                    "6" { $preferences.features += "acceptance-testing" }
                    "7" { $preferences.features += "advanced-deployment" }
                    "8" { $preferences.features += "chaos-engineering" }
                    "9" { $preferences.features += "observability" }
                    "10" { $preferences.features += "sli-slo-monitoring" }
                }
            }
        }
    } else {
        # Use defaults or command line parameters
        $preferences.platforms = if ($Platform) { @($Platform) } else { @("azuredevops", "github") }
        $preferences.environments = @("dev", "staging", "prod")
        $preferences.deploymentTargets = @("azure")
        $preferences.triggers = @("push", "pull_request")
        $preferences.branches = @("main", "develop", "release/*", "feature/*")
    }
    
    return $preferences
}

function Generate-AzureDevOpsPipeline {
    param(
        [object]$Analysis,
        [object]$Preferences,
        [object]$Config
    )

    Write-BuilderLog "Generating Azure DevOps pipeline..." "INFO"

    $pipeline = @"
# Azure DevOps Pipeline - Generated by Pipeline Builder
# Project Type: $($Analysis.projectType)
# Generated: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

trigger:
  branches:
    include:
"@

    foreach ($branch in $Preferences.branches) {
        $pipeline += "`n      - $branch"
    }

    if ("pull_request" -in $Preferences.triggers) {
        $pipeline += @"

pr:
  branches:
    include:
"@
        foreach ($branch in $Preferences.branches) {
            $pipeline += "`n      - $branch"
        }
    }

    $pipeline += @"

variables:
  buildConfiguration: 'Release'
  vmImageName: 'ubuntu-latest'

stages:
"@

    # Build stage
    $pipeline += @"

- stage: Build
  displayName: 'Build and Test'
  jobs:
  - job: Build
    displayName: 'Build job'
    pool:
      vmImage: `$(vmImageName)
    steps:
"@

    # Add build steps based on project type
    switch ($Analysis.projectType) {
        "dotnet" {
            $pipeline += @"

    - task: UseDotNet@2
      displayName: 'Use .NET SDK'
      inputs:
        packageType: 'sdk'
        version: '8.0.x'

    - task: DotNetCoreCLI@2
      displayName: 'Restore packages'
      inputs:
        command: 'restore'
        projects: '**/*.csproj'

    - task: DotNetCoreCLI@2
      displayName: 'Build solution'
      inputs:
        command: 'build'
        projects: '**/*.csproj'
        arguments: '--configuration `$(buildConfiguration) --no-restore'
"@
            if ($Analysis.hasTests) {
                $pipeline += @"

    - task: DotNetCoreCLI@2
      displayName: 'Run unit tests'
      inputs:
        command: 'test'
        projects: '**/*Test*.csproj'
        arguments: '--configuration `$(buildConfiguration) --no-build --collect:"XPlat Code Coverage" --logger trx --results-directory `$(Agent.TempDirectory)'

    - task: PublishTestResults@2
      displayName: 'Publish test results'
      condition: succeededOrFailed()
      inputs:
        testResultsFormat: 'VSTest'
        testResultsFiles: '**/*.trx'
        searchFolder: '`$(Agent.TempDirectory)'
        mergeTestResults: true
"@

                # Add code coverage validation if specified
                if ($Preferences.testing -and $Preferences.testing.codeCoverage) {
                    $coverageThreshold = $Preferences.testing.codeCoverage.minimum
                    $pipeline += @"

    - task: PublishCodeCoverageResults@1
      displayName: 'Publish code coverage'
      inputs:
        codeCoverageTool: 'Cobertura'
        summaryFileLocation: '`$(Agent.TempDirectory)/**/coverage.cobertura.xml'
        reportDirectory: '`$(Agent.TempDirectory)/coveragereport'
        failIfCoverageEmpty: true

    - task: BuildQualityChecks@8
      displayName: 'Check code coverage threshold'
      inputs:
        checkCoverage: true
        coverageFailOption: 'fixed'
        coverageType: 'lines'
        coverageThreshold: '$coverageThreshold'
        buildConfiguration: '`$(buildConfiguration)'
        forceCoverageImprovement: false
"@
                }

                # Add benchmark testing if enabled
                if ($Preferences.testing -and $Preferences.testing.benchmarks -and $Preferences.testing.benchmarks.enabled) {
                    $pipeline += @"

    - task: DotNetCoreCLI@2
      displayName: 'Run benchmark tests'
      inputs:
        command: 'run'
        projects: '**/*Benchmark*.csproj'
        arguments: '--configuration Release --no-build -- --exporters json --artifacts `$(Build.ArtifactStagingDirectory)/benchmarks'

    - task: PublishBuildArtifacts@1
      displayName: 'Publish benchmark results'
      inputs:
        pathToPublish: '`$(Build.ArtifactStagingDirectory)/benchmarks'
        artifactName: 'benchmark-results'
"@
                }

                # Add integration tests if enabled
                if ($Preferences.testing -and $Preferences.testing.integration -and $Preferences.testing.integration.enabled) {
                    $pipeline += @"

    - task: DockerCompose@0
      displayName: 'Start test dependencies'
      inputs:
        action: 'Run services'
        dockerComposeFile: 'docker-compose.test.yml'
        buildImages: false
        detached: true

    - task: DotNetCoreCLI@2
      displayName: 'Run integration tests'
      inputs:
        command: 'test'
        projects: '**/*IntegrationTest*.csproj'
        arguments: '--configuration `$(buildConfiguration) --no-build --logger trx --results-directory `$(Agent.TempDirectory)/integration'

    - task: DockerCompose@0
      displayName: 'Stop test dependencies'
      condition: always()
      inputs:
        action: 'Down'
        dockerComposeFile: 'docker-compose.test.yml'
"@
                }
            }

            $pipeline += @"

    - task: DotNetCoreCLI@2
      displayName: 'Publish application'
      inputs:
        command: 'publish'
        projects: '**/*.csproj'
        arguments: '--configuration `$(buildConfiguration) --output `$(Build.ArtifactStagingDirectory)'
        zipAfterPublish: true
"@
        }
        "node" {
            $pipeline += @"

    - task: NodeTool@0
      displayName: 'Use Node.js'
      inputs:
        versionSpec: '18.x'

    - script: npm install
      displayName: 'Install dependencies'

    - script: npm run build
      displayName: 'Build application'
"@
            if ($Analysis.hasTests) {
                $pipeline += @"

    - script: npm test
      displayName: 'Run tests'
"@
            }
        }
        "python" {
            $pipeline += @"

    - task: UsePythonVersion@0
      displayName: 'Use Python 3.x'
      inputs:
        versionSpec: '3.x'

    - script: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
      displayName: 'Install dependencies'

    - script: |
        python -m pytest --junitxml=junit/test-results.xml --cov=. --cov-report=xml
      displayName: 'Run tests'
      condition: and(succeeded(), eq('$($Analysis.hasTests)', 'True'))
"@
        }
    }

    # Add security scanning if requested
    if ("security-scanning" -in $Preferences.features) {
        $pipeline += @"

    - task: CodeQL3000Init@0
      displayName: 'Initialize CodeQL'

    - task: CodeQL3000Finalize@0
      displayName: 'Finalize CodeQL'
"@
    }

    # Docker build if detected
    if ($Analysis.hasDocker) {
        $pipeline += @"

    - task: Docker@2
      displayName: 'Build Docker image'
      inputs:
        command: 'build'
        dockerfile: 'Dockerfile'
        tags: |
          `$(Build.BuildId)
          latest
"@

        if ("docker" -in $Preferences.deploymentTargets) {
            $pipeline += @"

    - task: Docker@2
      displayName: 'Push Docker image'
      inputs:
        command: 'push'
        containerRegistry: 'DockerRegistry'
        repository: 'notify-service-api'
        tags: |
          `$(Build.BuildId)
          latest
"@
        }
    }

    $pipeline += @"

    - task: PublishBuildArtifacts@1
      displayName: 'Publish artifacts'
      inputs:
        pathToPublish: '`$(Build.ArtifactStagingDirectory)'
        artifactName: 'drop'
"@

    # Add acceptance testing stage if enabled
    if ("acceptance-testing" -in $Preferences.features) {
        $pipeline += @"

- stage: AcceptanceTests
  displayName: 'Acceptance Testing'
  dependsOn: Build
  condition: succeeded()
  jobs:
  - job: AcceptanceTests
    displayName: 'Run acceptance tests'
    pool:
      vmImage: `$(vmImageName)
    steps:
    - download: current
      artifact: drop

    - task: UseDotNet@2
      displayName: 'Use .NET SDK'
      inputs:
        packageType: 'sdk'
        version: '8.0.x'
"@

        # Add E2E testing if configured
        if ($Preferences.testing -and $Preferences.testing.e2e -and $Preferences.testing.e2e.enabled) {
            $e2eFramework = $Preferences.testing.e2e.framework

            switch ($e2eFramework) {
                "Playwright" {
                    $pipeline += @"

    - task: NodeTool@0
      displayName: 'Use Node.js for Playwright'
      inputs:
        versionSpec: '18.x'

    - script: |
        npm install -g @playwright/test
        npx playwright install
      displayName: 'Install Playwright'

    - script: |
        npx playwright test --reporter=junit
      displayName: 'Run Playwright E2E tests'
      env:
        CI: true
"@
                }
                "Selenium" {
                    $pipeline += @"

    - task: DotNetCoreCLI@2
      displayName: 'Run Selenium E2E tests'
      inputs:
        command: 'test'
        projects: '**/*E2ETest*.csproj'
        arguments: '--configuration `$(buildConfiguration) --logger trx --results-directory `$(Agent.TempDirectory)/e2e'
"@
                }
                "Cypress" {
                    $pipeline += @"

    - task: NodeTool@0
      displayName: 'Use Node.js for Cypress'
      inputs:
        versionSpec: '18.x'

    - script: |
        npm install cypress --save-dev
        npx cypress run --reporter junit --reporter-options mochaFile=results/cypress-[hash].xml
      displayName: 'Run Cypress E2E tests'
"@
                }
            }
        }

        # Add performance/load testing
        if ($Preferences.testing -and $Preferences.testing.benchmarks -and $Preferences.testing.benchmarks.enabled) {
            $pipeline += @"

    - task: DotNetCoreCLI@2
      displayName: 'Run performance tests'
      inputs:
        command: 'test'
        projects: '**/*PerformanceTest*.csproj'
        arguments: '--configuration Release --logger trx --results-directory `$(Agent.TempDirectory)/performance'

    - script: |
        echo "Validating performance benchmarks..."
        # Add custom performance validation logic here
      displayName: 'Validate performance thresholds'
"@
        }

        # Add acceptance test results publishing
        $pipeline += @"

    - task: PublishTestResults@2
      displayName: 'Publish acceptance test results'
      condition: succeededOrFailed()
      inputs:
        testResultsFormat: 'JUnit'
        testResultsFiles: '**/test-results.xml'
        mergeTestResults: true
        testRunTitle: 'Acceptance Tests'

    - task: PublishBuildArtifacts@1
      displayName: 'Publish acceptance test artifacts'
      condition: succeededOrFailed()
      inputs:
        pathToPublish: '`$(Agent.TempDirectory)'
        artifactName: 'acceptance-test-results'
"@
    }

    # Add deployment stages for each environment
    foreach ($env in $Preferences.environments) {
        $envCapitalized = $env.Substring(0,1).ToUpper() + $env.Substring(1)
        $pipeline += @"

- stage: Deploy$envCapitalized
  displayName: 'Deploy to $($env.ToUpper())'
  dependsOn: Build
  condition: succeeded()
"@

        # Add approval for production
        if ($env -eq "prod" -and "deployment-approvals" -in $Preferences.features) {
            $pipeline += @"
  variables:
  - group: 'Production-Secrets'
"@
        }

        $pipeline += @"
  jobs:
  - deployment: Deploy$envCapitalized
    displayName: 'Deploy to $env'
    environment: '$env'
    pool:
      vmImage: `$(vmImageName)
    strategy:
      runOnce:
        deploy:
          steps:
          - download: current
            artifact: drop
"@

        # Add deployment steps based on target
        foreach ($target in $Preferences.deploymentTargets) {
            switch ($target) {
                "azure" {
                    $pipeline += @"

          - task: AzureWebApp@1
            displayName: 'Deploy to Azure Web App'
            inputs:
              azureSubscription: 'Azure-$env'
              appType: 'webApp'
              appName: 'notify-service-api-$env'
              package: '`$(Pipeline.Workspace)/drop/**/*.zip'
"@
                }
                "kubernetes" {
                    $pipeline += @"

          - task: KubernetesManifest@0
            displayName: 'Deploy to Kubernetes'
            inputs:
              action: 'deploy'
              kubernetesServiceConnection: 'k8s-$env'
              namespace: 'notify-$env'
              manifests: 'k8s/*.yml'
"@
                }
                "aws" {
                    $pipeline += @"

          - task: AWSCLI@1
            displayName: 'Deploy to AWS'
            inputs:
              awsCredentials: 'AWS-$env'
              regionName: 'us-east-1'
              awsCommand: 'deploy'
              awsSubCommand: 'push'
"@
                }
                "ftp" {
                    $pipeline += @"

          - task: FtpUpload@2
            displayName: 'Deploy via FTP'
            inputs:
              credentialsOption: 'serviceEndpoint'
              serverEndpoint: 'FTP-$env'
              rootDirectory: '`$(Pipeline.Workspace)/drop'
              filePatterns: '**'
              remoteDirectory: '/var/www/$env'
              clean: false
              cleanContents: false
              preservePaths: true
              trustSSL: false
"@
                }
                "sftp" {
                    $pipeline += @"

          - task: SSH@0
            displayName: 'Deploy via SFTP'
            inputs:
              sshEndpoint: 'SFTP-$env'
              runOptions: 'commands'
              commands: |
                # Create backup
                if [ -d "/var/www/$env" ]; then
                  sudo cp -r /var/www/$env /var/backups/webapp-`$(date +%Y%m%d-%H%M%S)
                fi

                # Create directory if not exists
                sudo mkdir -p /var/www/$env

                # Set permissions
                sudo chown -R www-data:www-data /var/www/$env
              readyTimeout: '20000'

          - task: CopyFilesOverSSH@0
            displayName: 'Copy files via SFTP'
            inputs:
              sshEndpoint: 'SFTP-$env'
              sourceFolder: '`$(Pipeline.Workspace)/drop'
              contents: '**'
              targetFolder: '/var/www/$env'
              cleanTargetFolder: false
              overwrite: true
              failOnEmptySource: false
"@
                }
                "rsync" {
                    $pipeline += @"

          - task: SSH@0
            displayName: 'Deploy via Rsync'
            inputs:
              sshEndpoint: 'RSYNC-$env'
              runOptions: 'commands'
              commands: |
                # Create backup
                if [ -d "/var/www/$env" ]; then
                  sudo cp -r /var/www/$env /var/backups/webapp-`$(date +%Y%m%d-%H%M%S)
                fi

                # Rsync deployment
                rsync -avz --delete --exclude='.git' --exclude='*.log' \
                  `$(Pipeline.Workspace)/drop/ /var/www/$env/

                # Set permissions
                sudo chown -R www-data:www-data /var/www/$env
                sudo chmod -R 755 /var/www/$env
              readyTimeout: '20000'
"@
                }
            }
        }

        # Add health check
        $pipeline += @"

          - script: |
              echo "Waiting for deployment to be ready..."
              sleep 30
              curl -f https://notify-service-api-$env.azurewebsites.net/health || exit 1
            displayName: 'Health check'
"@
    }

    return $pipeline
}

function Generate-GitHubActionsPipeline {
    param(
        [object]$Analysis,
        [object]$Preferences,
        [object]$Config
    )

    Write-BuilderLog "Generating GitHub Actions workflow..." "INFO"

    $workflow = @"
# GitHub Actions Workflow - Generated by Pipeline Builder
# Project Type: $($Analysis.projectType)
# Generated: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

name: CI/CD Pipeline

on:
"@

    # Add triggers
    if ("push" -in $Preferences.triggers) {
        $workflow += @"
  push:
    branches:
"@
        foreach ($branch in $Preferences.branches) {
            $workflow += "`n      - $branch"
        }
    }

    if ("pull_request" -in $Preferences.triggers) {
        $workflow += @"
  pull_request:
    branches:
"@
        foreach ($branch in $Preferences.branches) {
            $workflow += "`n      - $branch"
        }
    }

    if ("schedule" -in $Preferences.triggers) {
        $workflow += @"
  schedule:
    - cron: '0 2 * * 1'  # Weekly on Monday at 2 AM
"@
    }

    $workflow += @"

env:
  BUILD_CONFIGURATION: Release

jobs:
  build:
    name: Build and Test
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
"@

    # Add build steps based on project type
    switch ($Analysis.projectType) {
        "dotnet" {
            $workflow += @"

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'

    - name: Restore dependencies
      run: dotnet restore

    - name: Build solution
      run: dotnet build --configuration `${{ env.BUILD_CONFIGURATION }} --no-restore
"@
            if ($Analysis.hasTests) {
                $workflow += @"

    - name: Run unit tests
      run: dotnet test --configuration `${{ env.BUILD_CONFIGURATION }} --no-build --verbosity normal --collect:"XPlat Code Coverage" --logger trx --results-directory ./TestResults

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results
        path: TestResults/

    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@v3
      with:
        directory: ./TestResults
        flags: unittests
        name: codecov-umbrella
"@

                # Add code coverage check if specified
                if ($Preferences.testing -and $Preferences.testing.codeCoverage) {
                    $coverageThreshold = $Preferences.testing.codeCoverage.minimum
                    $workflow += @"

    - name: Code Coverage Summary
      uses: irongut/CodeCoverageSummary@v1.3.0
      with:
        filename: TestResults/**/coverage.cobertura.xml
        badge: true
        fail_below_min: true
        format: markdown
        hide_branch_rate: false
        hide_complexity: true
        indicators: true
        output: both
        thresholds: '$coverageThreshold 80'
"@
                }

                # Add benchmark testing if enabled
                if ($Preferences.testing -and $Preferences.testing.benchmarks -and $Preferences.testing.benchmarks.enabled) {
                    $workflow += @"

    - name: Run benchmark tests
      run: |
        dotnet run --project **/*Benchmark*.csproj --configuration Release -- --exporters json --artifacts ./BenchmarkResults

    - name: Upload benchmark results
      uses: actions/upload-artifact@v4
      with:
        name: benchmark-results
        path: BenchmarkResults/

    - name: Performance regression check
      uses: benchmark-action/github-action-benchmark@v1
      with:
        tool: 'benchmarkdotnet'
        output-file-path: BenchmarkResults/results.json
        github-token: `${{ secrets.GITHUB_TOKEN }}
        auto-push: true
        alert-threshold: '110%'
        comment-on-alert: true
        fail-on-alert: true
"@
                }
            }

            $workflow += @"

    - name: Publish application
      run: dotnet publish --configuration `${{ env.BUILD_CONFIGURATION }} --output ./publish --no-build

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-artifacts
        path: ./publish/
"@
        }
        "node" {
            $workflow += @"

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build application
      run: npm run build
"@
            if ($Analysis.hasTests) {
                $workflow += @"

    - name: Run tests
      run: npm test
"@
            }

            $workflow += @"

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-artifacts
        path: dist/
"@
        }
        "python" {
            $workflow += @"

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.x'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
"@
            if ($Analysis.hasTests) {
                $workflow += @"

    - name: Run tests
      run: |
        python -m pytest --junitxml=junit/test-results.xml --cov=. --cov-report=xml
"@
            }
        }
    }

    # Add security scanning if requested
    if ("security-scanning" -in $Preferences.features) {
        $workflow += @"

    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: csharp

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3
"@
    }

    # Docker build if detected
    if ($Analysis.hasDocker) {
        $workflow += @"

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: false
        tags: notify-service-api:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max
"@
    }

    # Add acceptance testing job if enabled
    if ("acceptance-testing" -in $Preferences.features) {
        $workflow += @"

  acceptance-tests:
    name: Acceptance Tests
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: build-artifacts
        path: ./artifacts/
"@

        # Add E2E testing based on framework
        if ($Preferences.testing -and $Preferences.testing.e2e -and $Preferences.testing.e2e.enabled) {
            $e2eFramework = $Preferences.testing.e2e.framework

            switch ($e2eFramework) {
                "Playwright" {
                    $workflow += @"

    - name: Setup Node.js for Playwright
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install Playwright
      run: |
        npm install -g @playwright/test
        npx playwright install --with-deps

    - name: Run Playwright E2E tests
      run: npx playwright test
      env:
        CI: true

    - name: Upload Playwright report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report
        path: playwright-report/
        retention-days: 30
"@
                }
                "Cypress" {
                    $workflow += @"

    - name: Setup Node.js for Cypress
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Run Cypress E2E tests
      uses: cypress-io/github-action@v6
      with:
        start: npm start
        wait-on: 'http://localhost:3000'
        wait-on-timeout: 120
        browser: chrome
        record: true
      env:
        CYPRESS_RECORD_KEY: `${{ secrets.CYPRESS_RECORD_KEY }}
        GITHUB_TOKEN: `${{ secrets.GITHUB_TOKEN }}
"@
                }
                "Selenium" {
                    $workflow += @"

    - name: Setup .NET for Selenium tests
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'

    - name: Run Selenium E2E tests
      run: dotnet test **/*E2ETest*.csproj --configuration Release --logger trx --results-directory ./E2EResults

    - name: Upload E2E test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: e2e-test-results
        path: E2EResults/
"@
                }
            }
        }

        # Add performance testing
        if ($Preferences.testing -and $Preferences.testing.benchmarks -and $Preferences.testing.benchmarks.enabled) {
            $workflow += @"

    - name: Run performance tests
      run: dotnet test **/*PerformanceTest*.csproj --configuration Release --logger trx --results-directory ./PerformanceResults

    - name: Performance threshold validation
      run: |
        echo "Validating performance thresholds..."
        # Add custom performance validation logic
        python scripts/validate-performance.py --results ./PerformanceResults --threshold $($Preferences.testing.benchmarks.regressionThreshold)
"@
        }

        # Add integration tests with test containers
        if ($Preferences.testing -and $Preferences.testing.integration -and $Preferences.testing.integration.enabled) {
            $workflow += @"

    - name: Start test dependencies
      run: docker-compose -f docker-compose.test.yml up -d

    - name: Wait for services
      run: |
        timeout 60s bash -c 'until docker-compose -f docker-compose.test.yml ps | grep healthy; do sleep 2; done'

    - name: Run integration tests
      run: dotnet test **/*IntegrationTest*.csproj --configuration Release --logger trx --results-directory ./IntegrationResults

    - name: Stop test dependencies
      if: always()
      run: docker-compose -f docker-compose.test.yml down

    - name: Upload integration test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: integration-test-results
        path: IntegrationResults/
"@
        }

        $workflow += @"

    - name: Publish acceptance test results
      uses: dorny/test-reporter@v1
      if: always()
      with:
        name: Acceptance Tests
        path: '**/*.trx'
        reporter: dotnet-trx
        fail-on-error: true
"@
    }

    return $workflow
}

function Add-GitHubActionsDeploymentJobs {
    param(
        [string]$Workflow,
        [object]$Preferences
    )

    foreach ($env in $Preferences.environments) {
        $envCapitalized = $env.Substring(0,1).ToUpper() + $env.Substring(1)

        $Workflow += @"

  deploy-${env}:
    name: Deploy to $envCapitalized
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    environment: $env

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: build-artifacts
        path: ./artifacts/
"@

        # Add deployment steps based on target
        foreach ($target in $Preferences.deploymentTargets) {
            switch ($target) {
                "azure" {
                    $Workflow += @"

    - name: Login to Azure
      uses: azure/login@v1
      with:
        creds: `${{ secrets.AZURE_CREDENTIALS_$($env.ToUpper()) }}

    - name: Deploy to Azure Web App
      uses: azure/webapps-deploy@v2
      with:
        app-name: notify-service-api-$env
        package: ./artifacts/
"@
                }
                "aws" {
                    $Workflow += @"

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: `${{ secrets.AWS_ACCESS_KEY_ID_$($env.ToUpper()) }}
        aws-secret-access-key: `${{ secrets.AWS_SECRET_ACCESS_KEY_$($env.ToUpper()) }}
        aws-region: us-east-1

    - name: Deploy to AWS
      run: |
        aws s3 sync ./artifacts/ s3://notify-service-$env/
        aws elasticbeanstalk create-application-version --application-name notify-service --version-label `${{ github.sha }}
"@
                }
                "kubernetes" {
                    $Workflow += @"

    - name: Set up kubectl
      uses: azure/setup-kubectl@v3

    - name: Deploy to Kubernetes
      run: |
        echo "`${{ secrets.KUBECONFIG_$($env.ToUpper()) }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig
        kubectl apply -f k8s/
        kubectl set image deployment/notify-service-api notify-service-api=notify-service-api:`${{ github.sha }} -n notify-$env
"@
                }
                "docker" {
                    $Workflow += @"

    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: `${{ secrets.DOCKER_USERNAME }}
        password: `${{ secrets.DOCKER_PASSWORD }}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: |
          notify-service-api:latest
          notify-service-api:`${{ github.sha }}
"@
                }
                "ftp" {
                    $Workflow += @"

    - name: Deploy via FTP
      uses: SamKirkland/FTP-Deploy-Action@v4.3.4
      with:
        server: `${{ secrets.FTP_SERVER_$($env.ToUpper()) }}
        username: `${{ secrets.FTP_USERNAME_$($env.ToUpper()) }}
        password: `${{ secrets.FTP_PASSWORD_$($env.ToUpper()) }}
        local-dir: ./artifacts/
        server-dir: /var/www/$env/
        exclude: |
          **/.git*
          **/.git*/**
          **/node_modules/**
          **/*.log
"@
                }
                "sftp" {
                    $Workflow += @"

    - name: Setup SSH Key
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: `${{ secrets.SSH_PRIVATE_KEY_$($env.ToUpper()) }}

    - name: Create backup via SSH
      run: |
        ssh -o StrictHostKeyChecking=no `${{ secrets.SSH_USER_$($env.ToUpper()) }}@`${{ secrets.SSH_HOST_$($env.ToUpper()) }} \
          "if [ -d '/var/www/$env' ]; then sudo cp -r /var/www/$env /var/backups/webapp-\$(date +%Y%m%d-%H%M%S); fi"

    - name: Deploy via SFTP
      uses: wlixcc/SFTP-Deploy-Action@v1.2.4
      with:
        server: `${{ secrets.SSH_HOST_$($env.ToUpper()) }}
        username: `${{ secrets.SSH_USER_$($env.ToUpper()) }}
        ssh_private_key: `${{ secrets.SSH_PRIVATE_KEY_$($env.ToUpper()) }}
        local_path: './artifacts/*'
        remote_path: '/var/www/$env'
        sftpArgs: '-o ConnectTimeout=5'

    - name: Set permissions via SSH
      run: |
        ssh -o StrictHostKeyChecking=no `${{ secrets.SSH_USER_$($env.ToUpper()) }}@`${{ secrets.SSH_HOST_$($env.ToUpper()) }} \
          "sudo chown -R www-data:www-data /var/www/$env && sudo chmod -R 755 /var/www/$env"
"@
                }
                "rsync" {
                    $Workflow += @"

    - name: Setup SSH Key
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: `${{ secrets.SSH_PRIVATE_KEY_$($env.ToUpper()) }}

    - name: Deploy via Rsync
      run: |
        # Create backup
        ssh -o StrictHostKeyChecking=no `${{ secrets.SSH_USER_$($env.ToUpper()) }}@`${{ secrets.SSH_HOST_$($env.ToUpper()) }} \
          "if [ -d '/var/www/$env' ]; then sudo cp -r /var/www/$env /var/backups/webapp-\$(date +%Y%m%d-%H%M%S); fi"

        # Deploy with rsync
        rsync -avz --delete --exclude='.git' --exclude='*.log' \
          -e "ssh -o StrictHostKeyChecking=no" \
          ./artifacts/ `${{ secrets.SSH_USER_$($env.ToUpper()) }}@`${{ secrets.SSH_HOST_$($env.ToUpper()) }}:/var/www/$env/

        # Set permissions
        ssh -o StrictHostKeyChecking=no `${{ secrets.SSH_USER_$($env.ToUpper()) }}@`${{ secrets.SSH_HOST_$($env.ToUpper()) }} \
          "sudo chown -R www-data:www-data /var/www/$env && sudo chmod -R 755 /var/www/$env"
"@
                }
            }
        }

        # Add health check
        $Workflow += @"

    - name: Health check
      run: |
        echo "Waiting for deployment to be ready..."
        sleep 30
        curl -f https://notify-service-api-$env.azurewebsites.net/health || exit 1
"@

        # Add notifications if requested
        if ("notifications" -in $Preferences.features) {
            $Workflow += @"

    - name: Notify deployment success
      if: success()
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: "✅ Successfully deployed to $env environment"
      env:
        SLACK_WEBHOOK_URL: `${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Notify deployment failure
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        text: "❌ Failed to deploy to $env environment"
      env:
        SLACK_WEBHOOK_URL: `${{ secrets.SLACK_WEBHOOK_URL }}
"@
        }
    }

    return $Workflow
}

function New-ConditionalLogic {
    param(
        [object]$Preferences
    )

    $conditions = @{
        "branchConditions" = @()
        "pathConditions" = @()
        "environmentConditions" = @()
    }

    # Branch-based conditions
    if ("main" -in $Preferences.branches -or "master" -in $Preferences.branches) {
        $conditions.branchConditions += @{
            "condition" = "github.ref == 'refs/heads/main'"
            "description" = "Deploy only on main branch"
            "environments" = @("staging", "prod")
        }
    }

    if ($Preferences.branches | Where-Object { $_ -like "feature/*" }) {
        $conditions.branchConditions += @{
            "condition" = "startsWith(github.ref, 'refs/heads/feature/')"
            "description" = "Feature branch builds"
            "environments" = @("dev")
        }
    }

    # Path-based conditions
    $conditions.pathConditions += @{
        "condition" = "contains(github.event.head_commit.modified, 'src/')"
        "description" = "Only run when source code changes"
    }

    return $conditions
}

function Export-PipelineTemplates {
    param([object]$Config)

    Write-BuilderLog "Creating pipeline templates..." "INFO"

    # Create Azure DevOps templates
    $azureTemplatesDir = Join-Path $TemplatesDir "azure-devops"
    if (-not (Test-Path $azureTemplatesDir)) {
        New-Item -ItemType Directory -Path $azureTemplatesDir | Out-Null
    }

    # Create GitHub Actions templates
    $githubTemplatesDir = Join-Path $TemplatesDir "github-actions"
    if (-not (Test-Path $githubTemplatesDir)) {
        New-Item -ItemType Directory -Path $githubTemplatesDir | Out-Null
    }

    # Basic .NET template for Azure DevOps
    $dotnetAzureTemplate = @"
# Azure DevOps Pipeline Template for .NET Applications
parameters:
- name: buildConfiguration
  type: string
  default: 'Release'
- name: vmImage
  type: string
  default: 'ubuntu-latest'

stages:
- stage: Build
  displayName: 'Build and Test'
  jobs:
  - job: Build
    displayName: 'Build job'
    pool:
      vmImage: `${{ parameters.vmImage }}
    steps:
    - task: UseDotNet@2
      displayName: 'Use .NET SDK'
      inputs:
        packageType: 'sdk'
        version: '8.0.x'

    - task: DotNetCoreCLI@2
      displayName: 'Restore packages'
      inputs:
        command: 'restore'
        projects: '**/*.csproj'

    - task: DotNetCoreCLI@2
      displayName: 'Build solution'
      inputs:
        command: 'build'
        projects: '**/*.csproj'
        arguments: '--configuration `${{ parameters.buildConfiguration }} --no-restore'

    - task: DotNetCoreCLI@2
      displayName: 'Run tests'
      inputs:
        command: 'test'
        projects: '**/*Test*.csproj'
        arguments: '--configuration `${{ parameters.buildConfiguration }} --no-build --collect:"XPlat Code Coverage"'

    - task: DotNetCoreCLI@2
      displayName: 'Publish application'
      inputs:
        command: 'publish'
        projects: '**/*.csproj'
        arguments: '--configuration `${{ parameters.buildConfiguration }} --output `$(Build.ArtifactStagingDirectory)'
        zipAfterPublish: true

    - task: PublishBuildArtifacts@1
      displayName: 'Publish artifacts'
      inputs:
        pathToPublish: '`$(Build.ArtifactStagingDirectory)'
        artifactName: 'drop'
"@

    $dotnetAzureTemplate | Set-Content -Path (Join-Path $azureTemplatesDir "dotnet-build-template.yml") -Encoding UTF8

    # Basic .NET template for GitHub Actions
    $dotnetGitHubTemplate = @"
# GitHub Actions Workflow Template for .NET Applications
name: .NET CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  BUILD_CONFIGURATION: Release

jobs:
  build:
    name: Build and Test
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'

    - name: Restore dependencies
      run: dotnet restore

    - name: Build solution
      run: dotnet build --configuration `${{ env.BUILD_CONFIGURATION }} --no-restore

    - name: Run tests
      run: dotnet test --configuration `${{ env.BUILD_CONFIGURATION }} --no-build --verbosity normal --collect:"XPlat Code Coverage"

    - name: Publish application
      run: dotnet publish --configuration `${{ env.BUILD_CONFIGURATION }} --output ./publish --no-build

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-artifacts
        path: ./publish/
"@

    $dotnetGitHubTemplate | Set-Content -Path (Join-Path $githubTemplatesDir "dotnet-workflow.yml") -Encoding UTF8

    Write-BuilderLog "Pipeline templates created in: $TemplatesDir" "SUCCESS"
}

function Show-PipelineMenu {
    Write-Host ""
    Write-ColorText "🏗️ Pipeline Builder Menu" "Cyan"
    Write-Host ""
    Write-ColorText "    [1] 🔍 Analyze Repository" "Green"
    Write-ColorText "        └─ Scan repository structure and detect project type"
    Write-Host ""
    Write-ColorText "    [2] 🚀 Build Pipeline (Interactive)" "Blue"
    Write-ColorText "        └─ Create pipelines with guided setup"
    Write-Host ""
    Write-ColorText "    [3] ⚡ Quick Build (Auto-detect)" "Yellow"
    Write-ColorText "        └─ Generate pipelines with smart defaults"
    Write-Host ""
    Write-ColorText "    [4] 📋 Export Templates" "Magenta"
    Write-ColorText "        └─ Create reusable pipeline templates"
    Write-Host ""
    Write-ColorText "    [5] ✅ Validate Pipelines" "Cyan"
    Write-ColorText "        └─ Check generated pipelines for errors"
    Write-Host ""
    Write-ColorText "    [6] ⚙️ Configuration" "Gray"
    Write-ColorText "        └─ Manage builder settings"
    Write-Host ""
    Write-ColorText "    [0] 🚪 Exit" "Gray"
    Write-Host ""
}

# Main execution
Show-Header

$config = Initialize-BuilderConfiguration

if ($Action -eq "menu") {
    do {
        Show-PipelineMenu
        $choice = Read-Host "Enter your choice (0-6)"

        switch ($choice) {
            "1" {
                $analysis = Analyze-Repository
                Write-Host ""
                Write-BuilderLog "Repository Analysis Results:" "SUCCESS"
                Write-BuilderLog "Project Type: $($analysis.projectType)" "INFO"
                Write-BuilderLog "Languages: $($analysis.languages -join ', ')" "INFO"
                Write-BuilderLog "Has Docker: $($analysis.hasDocker)" "INFO"
                Write-BuilderLog "Has Tests: $($analysis.hasTests)" "INFO"
                Write-BuilderLog "Deployment Hints: $($analysis.deploymentHints -join ', ')" "INFO"
                Write-BuilderLog "Detected Files: $($analysis.detectedFiles -join ', ')" "INFO"
                Read-Host "Press Enter to continue"
            }
            "2" {
                $analysis = Analyze-Repository
                $preferences = Get-UserPreferences

                Write-Host ""
                Write-BuilderLog "Generating pipelines..." "INFO"

                foreach ($platform in $preferences.platforms) {
                    switch ($platform) {
                        "azuredevops" {
                            $pipeline = Generate-AzureDevOpsPipeline -Analysis $analysis -Preferences $preferences -Config $config
                            $outputFile = Join-Path $OutputDir "azure-pipelines.yml"
                            $pipeline | Set-Content -Path $outputFile -Encoding UTF8
                            Write-BuilderLog "Azure DevOps pipeline generated: $outputFile" "SUCCESS"
                        }
                        "github" {
                            $workflow = Generate-GitHubActionsPipeline -Analysis $analysis -Preferences $preferences -Config $config
                            $workflow = Add-GitHubActionsDeploymentJobs -Workflow $workflow -Preferences $preferences

                            $workflowDir = Join-Path $OutputDir ".github/workflows"
                            if (-not (Test-Path $workflowDir)) {
                                New-Item -ItemType Directory -Path $workflowDir -Force | Out-Null
                            }
                            $outputFile = Join-Path $workflowDir "ci-cd.yml"
                            $workflow | Set-Content -Path $outputFile -Encoding UTF8
                            Write-BuilderLog "GitHub Actions workflow generated: $outputFile" "SUCCESS"
                        }
                    }
                }

                Write-Host ""
                Write-BuilderLog "Pipeline generation complete!" "SUCCESS"
                Write-BuilderLog "Output directory: $OutputDir" "INFO"
                Read-Host "Press Enter to continue"
            }
            "3" {
                # Quick build with auto-detection
                $analysis = Analyze-Repository

                # Smart defaults based on analysis
                $preferences = @{
                    "platforms" = @("azuredevops", "github")
                    "environments" = @("dev", "staging", "prod")
                    "deploymentTargets" = if ($analysis.hasDocker) { @("docker", "azure") } else { @("azure") }
                    "triggers" = @("push", "pull_request")
                    "branches" = @("main", "develop", "release/*", "feature/*")
                    "features" = @("security-scanning")
                }

                Write-Host ""
                Write-BuilderLog "Quick build with smart defaults..." "INFO"
                Write-BuilderLog "Platforms: $($preferences.platforms -join ', ')" "INFO"
                Write-BuilderLog "Deployment targets: $($preferences.deploymentTargets -join ', ')" "INFO"

                foreach ($platform in $preferences.platforms) {
                    switch ($platform) {
                        "azuredevops" {
                            $pipeline = Generate-AzureDevOpsPipeline -Analysis $analysis -Preferences $preferences -Config $config
                            $outputFile = Join-Path $OutputDir "azure-pipelines.yml"
                            $pipeline | Set-Content -Path $outputFile -Encoding UTF8
                            Write-BuilderLog "Azure DevOps pipeline generated: $outputFile" "SUCCESS"
                        }
                        "github" {
                            $workflow = Generate-GitHubActionsPipeline -Analysis $analysis -Preferences $preferences -Config $config
                            $workflow = Add-GitHubActionsDeploymentJobs -Workflow $workflow -Preferences $preferences

                            $workflowDir = Join-Path $OutputDir ".github/workflows"
                            if (-not (Test-Path $workflowDir)) {
                                New-Item -ItemType Directory -Path $workflowDir -Force | Out-Null
                            }
                            $outputFile = Join-Path $workflowDir "ci-cd.yml"
                            $workflow | Set-Content -Path $outputFile -Encoding UTF8
                            Write-BuilderLog "GitHub Actions workflow generated: $outputFile" "SUCCESS"
                        }
                    }
                }

                Read-Host "Press Enter to continue"
            }
            "4" {
                Export-PipelineTemplates -Config $config
                Read-Host "Press Enter to continue"
            }
            "5" {
                Write-BuilderLog "Pipeline validation functionality coming soon..." "INFO"
                Read-Host "Press Enter to continue"
            }
            "6" {
                Write-BuilderLog "Configuration management functionality coming soon..." "INFO"
                Read-Host "Press Enter to continue"
            }
            "0" {
                Write-BuilderLog "Goodbye!" "INFO"
                break
            }
            default {
                Write-BuilderLog "Invalid choice. Please try again." "WARN"
                Read-Host "Press Enter to continue"
            }
        }
    } while ($choice -ne "0")
} else {
    # Handle command line actions
    $analysis = Analyze-Repository

    switch ($Action.ToLower()) {
        "analyze" {
            if ($Json) {
                $analysis | ConvertTo-Json -Depth 10
            } else {
                Write-BuilderLog "Repository Analysis Results:" "SUCCESS"
                Write-BuilderLog "Project Type: $($analysis.projectType)" "INFO"
                Write-BuilderLog "Languages: $($analysis.languages -join ', ')" "INFO"
                Write-BuilderLog "Has Docker: $($analysis.hasDocker)" "INFO"
                Write-BuilderLog "Has Tests: $($analysis.hasTests)" "INFO"
                Write-BuilderLog "Deployment Hints: $($analysis.deploymentHints -join ', ')" "INFO"
            }
        }
        "build" {
            if (-not $Platform) {
                Write-BuilderLog "Platform parameter required for build action (azuredevops, github, or both)" "ERROR"
                exit 1
            }

            # Use smart defaults for non-interactive build
            $preferences = @{
                "platforms" = if ($Platform -eq "both") { @("azuredevops", "github") } else { @($Platform) }
                "environments" = @("dev", "staging", "prod")
                "deploymentTargets" = if ($analysis.hasDocker) { @("docker", "azure") } else { @("azure") }
                "triggers" = @("push", "pull_request")
                "branches" = @("main", "develop", "release/*", "feature/*")
                "features" = @("security-scanning")
            }

            foreach ($platform in $preferences.platforms) {
                switch ($platform) {
                    "azuredevops" {
                        $pipeline = Generate-AzureDevOpsPipeline -Analysis $analysis -Preferences $preferences -Config $config
                        $outputFile = Join-Path $OutputDir "azure-pipelines.yml"
                        $pipeline | Set-Content -Path $outputFile -Encoding UTF8
                        Write-BuilderLog "Azure DevOps pipeline generated: $outputFile" "SUCCESS"
                    }
                    "github" {
                        $workflow = Generate-GitHubActionsPipeline -Analysis $analysis -Preferences $preferences -Config $config
                        $workflow = Add-GitHubActionsDeploymentJobs -Workflow $workflow -Preferences $preferences

                        $workflowDir = Join-Path $OutputDir ".github/workflows"
                        if (-not (Test-Path $workflowDir)) {
                            New-Item -ItemType Directory -Path $workflowDir -Force | Out-Null
                        }
                        $outputFile = Join-Path $workflowDir "ci-cd.yml"
                        $workflow | Set-Content -Path $outputFile -Encoding UTF8
                        Write-BuilderLog "GitHub Actions workflow generated: $outputFile" "SUCCESS"
                    }
                }
            }
        }
        "export" {
            Export-PipelineTemplates -Config $config
        }
        default {
            Write-BuilderLog "Unknown action: $Action" "ERROR"
            Write-BuilderLog "Available actions: menu, analyze, build, export" "INFO"
            exit 1
        }
    }
}
