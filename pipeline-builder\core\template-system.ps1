# ═══════════════════════════════════════════════════════════════════════════════
# 📋 Pipeline Template System
# ═══════════════════════════════════════════════════════════════════════════════
# Customizable pipeline templates for different project types and deployment scenarios

param(
    [string]$Action = "menu",           # menu, create, edit, list, apply, export
    [string]$TemplateName = "",         # Template name
    [string]$ProjectType = "",          # dotnet, node, python, docker, etc.
    [string]$Platform = "azuredevops",  # azuredevops, github
    [string]$OutputPath = "",           # Output directory
    [switch]$Interactive,               # Interactive mode
    [switch]$Json                       # JSON output
)

# Configuration
$ScriptRoot = Split-Path -Parent $MyInvocation.MyCommand.Path
$TemplatesDir = Join-Path $ScriptRoot "../templates"
$ConfigFile = Join-Path $ScriptRoot "../config/template-system-config.json"

# Ensure directories exist
@($TemplatesDir, (Split-Path -Parent $ConfigFile)) | ForEach-Object {
    if (-not (Test-Path $_)) {
        New-Item -ItemType Directory -Path $_ | Out-Null
    }
}

# Colors
$Colors = @{
    Red = "Red"; Green = "Green"; Yellow = "Yellow"; Blue = "Blue"
    Cyan = "Cyan"; Magenta = "Magenta"; Gray = "Gray"; White = "White"
}

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    if (-not $Json) { Write-Host $Text -ForegroundColor $Colors[$Color] }
}

function Write-TemplateLog {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    if (-not $Json) {
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            default { "Cyan" }
        }
        Write-ColorText "[$timestamp] [$Level] $Message" $color
    }
}

function Show-Header {
    if ($Json) { return }
    
    Clear-Host
    Write-Host ""
    Write-ColorText "    📋📋📋 PIPELINE TEMPLATE SYSTEM 📋📋📋" "Cyan"
    Write-Host ""
    Write-ColorText "    🎯 Reusable Templates • Project Types • Deployment Scenarios" "Yellow"
    Write-ColorText "    🏗️ Azure DevOps • GitHub Actions • Custom Workflows" "Gray"
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "Gray"
}

function Initialize-TemplateSystem {
    Write-TemplateLog "Initializing template system..." "INFO"
    
    if (-not (Test-Path $ConfigFile)) {
        $defaultConfig = @{
            "templateCategories" = @{
                "projectTypes" = @{
                    "name" = "Project Type Templates"
                    "description" = "Templates based on programming language and framework"
                    "templates" = @(
                        "dotnet-webapi", "dotnet-mvc", "dotnet-blazor", "dotnet-console",
                        "nodejs-express", "nodejs-react", "nodejs-vue", "nodejs-angular",
                        "python-flask", "python-django", "python-fastapi",
                        "java-spring", "java-maven", "java-gradle",
                        "docker-app", "docker-compose"
                    )
                }
                "deploymentTargets" = @{
                    "name" = "Deployment Target Templates"
                    "description" = "Templates for specific deployment destinations"
                    "templates" = @(
                        "azure-webapp", "azure-functions", "azure-aks", "azure-container-instances",
                        "aws-lambda", "aws-ecs", "aws-elastic-beanstalk",
                        "kubernetes", "docker-registry", "ftp-sftp"
                    )
                }
                "scenarios" = @{
                    "name" = "Deployment Scenario Templates"
                    "description" = "Templates for specific deployment patterns"
                    "templates" = @(
                        "simple-ci-cd", "multi-environment", "canary-deployment", "blue-green",
                        "microservices", "monorepo", "feature-branch", "gitflow"
                    )
                }
                "advanced" = @{
                    "name" = "Advanced Templates"
                    "description" = "Complex templates with advanced features"
                    "templates" = @(
                        "enterprise-pipeline", "security-focused", "performance-testing",
                        "chaos-engineering", "observability", "compliance"
                    )
                }
            }
            "templateVariables" = @{
                "project" = @{
                    "name" = "{{PROJECT_NAME}}"
                    "description" = "{{PROJECT_DESCRIPTION}}"
                    "version" = "{{PROJECT_VERSION}}"
                    "repository" = "{{REPOSITORY_URL}}"
                }
                "build" = @{
                    "configuration" = "{{BUILD_CONFIGURATION}}"
                    "platform" = "{{BUILD_PLATFORM}}"
                    "framework" = "{{TARGET_FRAMEWORK}}"
                    "runtime" = "{{RUNTIME_VERSION}}"
                }
                "deployment" = @{
                    "environments" = "{{DEPLOYMENT_ENVIRONMENTS}}"
                    "targets" = "{{DEPLOYMENT_TARGETS}}"
                    "strategy" = "{{DEPLOYMENT_STRATEGY}}"
                    "approvals" = "{{REQUIRE_APPROVALS}}"
                }
                "testing" = @{
                    "unitTests" = "{{ENABLE_UNIT_TESTS}}"
                    "integrationTests" = "{{ENABLE_INTEGRATION_TESTS}}"
                    "e2eTests" = "{{ENABLE_E2E_TESTS}}"
                    "codeCoverage" = "{{CODE_COVERAGE_THRESHOLD}}"
                }
                "security" = @{
                    "scanning" = "{{ENABLE_SECURITY_SCANNING}}"
                    "compliance" = "{{COMPLIANCE_FRAMEWORK}}"
                    "secrets" = "{{SECRETS_MANAGEMENT}}"
                }
            }
        }
        
        $defaultConfig | ConvertTo-Json -Depth 10 | Set-Content -Path $ConfigFile -Encoding UTF8
        Write-TemplateLog "Template system configuration created" "SUCCESS"
    }
    
    # Create default templates
    Create-DefaultTemplates
    
    return Get-Content $ConfigFile | ConvertFrom-Json
}

function Create-DefaultTemplates {
    Write-TemplateLog "Creating default templates..." "INFO"
    
    # .NET Web API Template
    $dotnetWebApiTemplate = @{
        "name" = "dotnet-webapi"
        "displayName" = ".NET Web API"
        "description" = "Template for .NET Web API projects with comprehensive CI/CD"
        "category" = "projectTypes"
        "platform" = "azuredevops"
        "variables" = @{
            "PROJECT_NAME" = "MyWebAPI"
            "BUILD_CONFIGURATION" = "Release"
            "TARGET_FRAMEWORK" = "net8.0"
            "DEPLOYMENT_ENVIRONMENTS" = @("dev", "staging", "prod")
            "ENABLE_UNIT_TESTS" = $true
            "CODE_COVERAGE_THRESHOLD" = 80
        }
        "pipeline" = @"
trigger:
  branches:
    include:
      - main
      - develop
      - feature/*

variables:
  buildConfiguration: '{{BUILD_CONFIGURATION}}'
  vmImageName: 'ubuntu-latest'

stages:
- stage: Build
  displayName: 'Build and Test'
  jobs:
  - job: Build
    displayName: 'Build {{PROJECT_NAME}}'
    pool:
      vmImage: `$(vmImageName)
    steps:
    - task: UseDotNet@2
      displayName: 'Use .NET SDK'
      inputs:
        packageType: 'sdk'
        version: '8.0.x'
    
    - task: DotNetCoreCLI@2
      displayName: 'Restore packages'
      inputs:
        command: 'restore'
        projects: '**/*.csproj'
    
    - task: DotNetCoreCLI@2
      displayName: 'Build project'
      inputs:
        command: 'build'
        projects: '**/*.csproj'
        arguments: '--configuration `$(buildConfiguration) --no-restore'
    
    {{#if ENABLE_UNIT_TESTS}}
    - task: DotNetCoreCLI@2
      displayName: 'Run tests'
      inputs:
        command: 'test'
        projects: '**/*Test*.csproj'
        arguments: '--configuration `$(buildConfiguration) --no-build --collect:"XPlat Code Coverage"'
    
    - task: PublishCodeCoverageResults@1
      displayName: 'Publish code coverage'
      inputs:
        codeCoverageTool: 'Cobertura'
        summaryFileLocation: '`$(Agent.TempDirectory)/**/coverage.cobertura.xml'
    {{/if}}
    
    - task: DotNetCoreCLI@2
      displayName: 'Publish application'
      inputs:
        command: 'publish'
        projects: '**/*.csproj'
        arguments: '--configuration `$(buildConfiguration) --output `$(Build.ArtifactStagingDirectory)'
        zipAfterPublish: true
    
    - task: PublishBuildArtifacts@1
      displayName: 'Publish artifacts'
      inputs:
        pathToPublish: '`$(Build.ArtifactStagingDirectory)'
        artifactName: 'drop'

{{#each DEPLOYMENT_ENVIRONMENTS}}
- stage: Deploy{{this}}
  displayName: 'Deploy to {{this}}'
  dependsOn: Build
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  jobs:
  - deployment: Deploy{{this}}
    displayName: 'Deploy to {{this}} environment'
    environment: '{{this}}'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebApp@1
            displayName: 'Deploy to Azure Web App'
            inputs:
              azureSubscription: 'Azure-{{this}}'
              appType: 'webApp'
              appName: '{{PROJECT_NAME}}-{{this}}'
              package: '`$(Pipeline.Workspace)/drop/**/*.zip'
{{/each}}
"@
    }
    
    # Node.js Express Template
    $nodeExpressTemplate = @{
        "name" = "nodejs-express"
        "displayName" = "Node.js Express API"
        "description" = "Template for Node.js Express applications"
        "category" = "projectTypes"
        "platform" = "github"
        "variables" = @{
            "PROJECT_NAME" = "express-api"
            "NODE_VERSION" = "18.x"
            "DEPLOYMENT_ENVIRONMENTS" = @("dev", "prod")
            "ENABLE_UNIT_TESTS" = $true
        }
        "pipeline" = @"
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '{{NODE_VERSION}}'

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: `${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linting
      run: npm run lint
    
    {{#if ENABLE_UNIT_TESTS}}
    - name: Run tests
      run: npm test
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
    {{/if}}
    
    - name: Build application
      run: npm run build
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-artifacts
        path: dist/

{{#each DEPLOYMENT_ENVIRONMENTS}}
  deploy-{{this}}:
    name: Deploy to {{this}}
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    environment: {{this}}
    
    steps:
    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: build-artifacts
        path: ./dist/
    
    - name: Deploy to {{this}}
      run: |
        echo "Deploying to {{this}} environment"
        # Add deployment commands here
{{/each}}
"@
    }
    
    # Save templates
    $templates = @($dotnetWebApiTemplate, $nodeExpressTemplate)
    
    foreach ($template in $templates) {
        $templatePath = Join-Path $TemplatesDir "$($template.platform)/$($template.name).json"
        $templateDir = Split-Path -Parent $templatePath
        
        if (-not (Test-Path $templateDir)) {
            New-Item -ItemType Directory -Path $templateDir -Force | Out-Null
        }
        
        $template | ConvertTo-Json -Depth 10 | Set-Content -Path $templatePath -Encoding UTF8
        Write-TemplateLog "Created template: $($template.name)" "SUCCESS"
    }
}

function Show-TemplateMenu {
    Write-Host ""
    Write-ColorText "📋 Pipeline Template System Menu" "Cyan"
    Write-Host ""
    Write-ColorText "    [1] 📝 Create New Template" "Green"
    Write-ColorText "        └─ Build custom pipeline template from scratch"
    Write-Host ""
    Write-ColorText "    [2] 📋 List Available Templates" "Blue"
    Write-ColorText "        └─ Browse existing templates by category"
    Write-Host ""
    Write-ColorText "    [3] 🎯 Apply Template" "Yellow"
    Write-ColorText "        └─ Generate pipeline from existing template"
    Write-Host ""
    Write-ColorText "    [4] ✏️ Edit Template" "Magenta"
    Write-ColorText "        └─ Modify existing template"
    Write-Host ""
    Write-ColorText "    [5] 📤 Export Template" "Cyan"
    Write-ColorText "        └─ Export template for sharing or backup"
    Write-Host ""
    Write-ColorText "    [6] 📥 Import Template" "White"
    Write-ColorText "        └─ Import template from file"
    Write-Host ""
    Write-ColorText "    [0] 🚪 Exit" "Gray"
    Write-Host ""
}

function List-Templates {
    param([object]$Config)
    
    Write-Host ""
    Write-ColorText "📋 Available Templates" "Cyan"
    Write-Host ""
    
    foreach ($category in $Config.templateCategories.Keys) {
        $categoryConfig = $Config.templateCategories.$category
        Write-ColorText "$($categoryConfig.name):" "Yellow"
        Write-ColorText "  $($categoryConfig.description)" "Gray"
        
        # List actual template files
        $categoryDir = Join-Path $TemplatesDir "*"
        $templateFiles = Get-ChildItem -Path $categoryDir -Filter "*.json" -Recurse -ErrorAction SilentlyContinue
        
        foreach ($templateFile in $templateFiles) {
            try {
                $template = Get-Content $templateFile.FullName | ConvertFrom-Json
                if ($template.category -eq $category) {
                    Write-ColorText "    • $($template.displayName) ($($template.name))" "White"
                    Write-ColorText "      $($template.description)" "Gray"
                    Write-ColorText "      Platform: $($template.platform)" "Gray"
                }
            } catch {
                Write-TemplateLog "Error reading template: $($templateFile.Name)" "WARN"
            }
        }
        Write-Host ""
    }
}

function Apply-Template {
    param([string]$TemplateName, [string]$Platform)
    
    Write-TemplateLog "Applying template: $TemplateName for $Platform" "INFO"
    
    # Find template file
    $templatePath = Join-Path $TemplatesDir "$Platform/$TemplateName.json"
    
    if (-not (Test-Path $templatePath)) {
        Write-TemplateLog "Template not found: $templatePath" "ERROR"
        return $null
    }
    
    try {
        $template = Get-Content $templatePath | ConvertFrom-Json
        
        Write-Host ""
        Write-ColorText "🎯 Applying Template: $($template.displayName)" "Cyan"
        Write-ColorText "Description: $($template.description)" "Gray"
        Write-Host ""
        
        # Collect variable values
        $variables = @{}
        foreach ($varName in $template.variables.PSObject.Properties.Name) {
            $defaultValue = $template.variables.$varName
            $prompt = "Enter value for $varName"
            if ($defaultValue) {
                $prompt += " (default: $defaultValue)"
            }
            
            $userValue = Read-Host $prompt
            if (-not $userValue -and $defaultValue) {
                $variables[$varName] = $defaultValue
            } else {
                $variables[$varName] = $userValue
            }
        }
        
        # Process template with variables
        $processedPipeline = $template.pipeline
        foreach ($varName in $variables.Keys) {
            $processedPipeline = $processedPipeline -replace "{{$varName}}", $variables[$varName]
        }
        
        # Handle conditional blocks (simple implementation)
        $processedPipeline = Process-ConditionalBlocks $processedPipeline $variables
        
        Write-Host ""
        Write-ColorText "Generated Pipeline:" "SUCCESS"
        Write-ColorText $processedPipeline "Green"
        
        return @{
            "template" = $template
            "variables" = $variables
            "pipeline" = $processedPipeline
        }
        
    } catch {
        Write-TemplateLog "Error applying template: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

function Process-ConditionalBlocks {
    param([string]$Content, [hashtable]$Variables)
    
    # Simple conditional processing for {{#if VARIABLE}} blocks
    $result = $Content
    
    # Process {{#if}} blocks
    $ifPattern = '{{#if\s+(\w+)}}(.*?){{/if}}'
    $matches = [regex]::Matches($result, $ifPattern, [System.Text.RegularExpressions.RegexOptions]::Singleline)
    
    foreach ($match in $matches) {
        $variable = $match.Groups[1].Value
        $content = $match.Groups[2].Value
        
        if ($Variables.ContainsKey($variable) -and $Variables[$variable]) {
            $result = $result -replace [regex]::Escape($match.Value), $content
        } else {
            $result = $result -replace [regex]::Escape($match.Value), ""
        }
    }
    
    # Process {{#each}} blocks (simple implementation)
    $eachPattern = '{{#each\s+(\w+)}}(.*?){{/each}}'
    $eachMatches = [regex]::Matches($result, $eachPattern, [System.Text.RegularExpressions.RegexOptions]::Singleline)
    
    foreach ($match in $eachMatches) {
        $variable = $match.Groups[1].Value
        $content = $match.Groups[2].Value
        
        if ($Variables.ContainsKey($variable) -and $Variables[$variable] -is [array]) {
            $expandedContent = ""
            foreach ($item in $Variables[$variable]) {
                $itemContent = $content -replace "{{this}}", $item
                $expandedContent += $itemContent
            }
            $result = $result -replace [regex]::Escape($match.Value), $expandedContent
        } else {
            $result = $result -replace [regex]::Escape($match.Value), ""
        }
    }
    
    return $result
}

function Create-NewTemplate {
    Write-Host ""
    Write-ColorText "📝 Create New Template" "Cyan"
    Write-Host ""
    
    $templateName = Read-Host "Enter template name (e.g., my-custom-template)"
    $displayName = Read-Host "Enter display name (e.g., My Custom Template)"
    $description = Read-Host "Enter description"
    
    Write-ColorText "Select category:" "Yellow"
    Write-ColorText "  [1] Project Types" "White"
    Write-ColorText "  [2] Deployment Targets" "White"
    Write-ColorText "  [3] Scenarios" "White"
    Write-ColorText "  [4] Advanced" "White"
    
    $categoryChoice = Read-Host "Enter choice (1-4)"
    $category = switch ($categoryChoice) {
        "1" { "projectTypes" }
        "2" { "deploymentTargets" }
        "3" { "scenarios" }
        "4" { "advanced" }
        default { "projectTypes" }
    }
    
    Write-ColorText "Select platform:" "Yellow"
    Write-ColorText "  [1] Azure DevOps" "White"
    Write-ColorText "  [2] GitHub Actions" "White"
    
    $platformChoice = Read-Host "Enter choice (1-2)"
    $platform = if ($platformChoice -eq "2") { "github" } else { "azuredevops" }
    
    Write-Host ""
    Write-ColorText "Enter pipeline content (end with empty line):" "Yellow"
    $pipelineLines = @()
    do {
        $line = Read-Host
        if ($line) {
            $pipelineLines += $line
        }
    } while ($line)
    
    $pipelineContent = $pipelineLines -join "`n"
    
    $newTemplate = @{
        "name" = $templateName
        "displayName" = $displayName
        "description" = $description
        "category" = $category
        "platform" = $platform
        "variables" = @{
            "PROJECT_NAME" = "MyProject"
            "BUILD_CONFIGURATION" = "Release"
        }
        "pipeline" = $pipelineContent
        "created" = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        "author" = $env:USERNAME
    }
    
    $templatePath = Join-Path $TemplatesDir "$platform/$templateName.json"
    $templateDir = Split-Path -Parent $templatePath
    
    if (-not (Test-Path $templateDir)) {
        New-Item -ItemType Directory -Path $templateDir -Force | Out-Null
    }
    
    $newTemplate | ConvertTo-Json -Depth 10 | Set-Content -Path $templatePath -Encoding UTF8
    Write-TemplateLog "Template created successfully: $templatePath" "SUCCESS"
    
    return $newTemplate
}

# Main execution
Show-Header

$config = Initialize-TemplateSystem

if ($Action -eq "menu") {
    do {
        Show-TemplateMenu
        $choice = Read-Host "Enter your choice (0-6)"
        
        switch ($choice) {
            "1" {
                Create-NewTemplate
                Read-Host "Press Enter to continue"
            }
            "2" {
                List-Templates $config
                Read-Host "Press Enter to continue"
            }
            "3" {
                Write-Host ""
                $templateName = Read-Host "Enter template name"
                $platform = Read-Host "Enter platform (azuredevops/github)"
                
                if ($templateName -and $platform) {
                    $result = Apply-Template $templateName $platform
                    if ($result -and $OutputPath) {
                        $result.pipeline | Set-Content -Path $OutputPath -Encoding UTF8
                        Write-TemplateLog "Pipeline saved to: $OutputPath" "SUCCESS"
                    }
                }
                Read-Host "Press Enter to continue"
            }
            "4" {
                Write-TemplateLog "Template editing functionality coming soon..." "INFO"
                Read-Host "Press Enter to continue"
            }
            "5" {
                Write-TemplateLog "Template export functionality coming soon..." "INFO"
                Read-Host "Press Enter to continue"
            }
            "6" {
                Write-TemplateLog "Template import functionality coming soon..." "INFO"
                Read-Host "Press Enter to continue"
            }
            "0" {
                Write-TemplateLog "Goodbye!" "INFO"
                break
            }
            default {
                Write-TemplateLog "Invalid choice. Please try again." "WARN"
                Read-Host "Press Enter to continue"
            }
        }
    } while ($choice -ne "0")
}
