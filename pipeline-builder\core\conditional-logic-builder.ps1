# ═══════════════════════════════════════════════════════════════════════════════
# 🔀 Conditional Logic Builder
# ═══════════════════════════════════════════════════════════════════════════════
# Build complex conditional logic for pipeline execution with visual interface

param(
    [string]$Action = "menu",           # menu, build, validate, export
    [string]$Platform = "azuredevops",  # azuredevops, github
    [string]$OutputPath = "",           # Output file path
    [switch]$Interactive,               # Interactive mode
    [switch]$Json                       # JSON output
)

# Configuration
$ScriptRoot = Split-Path -Parent $MyInvocation.MyCommand.Path
$ConditionsFile = Join-Path $ScriptRoot "../config/conditional-logic.json"

# Colors
$Colors = @{
    Red = "Red"; Green = "Green"; Yellow = "Yellow"; Blue = "Blue"
    Cyan = "Cyan"; Magenta = "Magenta"; Gray = "Gray"; White = "White"
}

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    if (-not $Json) { Write-Host $Text -ForegroundColor $Colors[$Color] }
}

function Write-LogicLog {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    if (-not $Json) {
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            default { "Cyan" }
        }
        Write-ColorText "[$timestamp] [$Level] $Message" $color
    }
}

function Show-Header {
    if ($Json) { return }
    
    Clear-Host
    Write-Host ""
    Write-ColorText "    🔀🔀🔀 CONDITIONAL LOGIC BUILDER 🔀🔀🔀" "Cyan"
    Write-Host ""
    Write-ColorText "    ⚡ Smart Conditions • Branch Logic • Environment Rules" "Yellow"
    Write-ColorText "    🎯 Azure DevOps • GitHub Actions • Complex Workflows" "Gray"
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "Gray"
}

function Initialize-ConditionalLogic {
    Write-LogicLog "Initializing conditional logic system..." "INFO"
    
    $configDir = Split-Path -Parent $ConditionsFile
    if (-not (Test-Path $configDir)) {
        New-Item -ItemType Directory -Path $configDir | Out-Null
    }
    
    if (-not (Test-Path $ConditionsFile)) {
        $defaultConfig = @{
            "conditionTypes" = @{
                "branch" = @{
                    "name" = "Branch Conditions"
                    "description" = "Execute based on Git branch patterns"
                    "operators" = @("equals", "startsWith", "endsWith", "contains", "matches", "in")
                    "examples" = @(
                        "eq(variables['Build.SourceBranch'], 'refs/heads/main')",
                        "startsWith(variables['Build.SourceBranch'], 'refs/heads/feature/')",
                        "in(variables['Build.SourceBranchName'], 'main', 'develop', 'master')"
                    )
                }
                "environment" = @{
                    "name" = "Environment Conditions"
                    "description" = "Execute based on target environment"
                    "operators" = @("equals", "notEquals", "in", "notIn")
                    "examples" = @(
                        "eq(variables['Environment'], 'production')",
                        "in(variables['Environment'], 'staging', 'production')",
                        "ne(variables['Environment'], 'development')"
                    )
                }
                "buildResult" = @{
                    "name" = "Build Result Conditions"
                    "description" = "Execute based on previous job/stage results"
                    "operators" = @("succeeded", "failed", "canceled", "succeededOrFailed")
                    "examples" = @(
                        "succeeded()",
                        "failed()",
                        "succeededOrFailed()",
                        "and(succeeded(), eq(variables['RunTests'], 'true'))"
                    )
                }
                "variable" = @{
                    "name" = "Variable Conditions"
                    "description" = "Execute based on pipeline variables"
                    "operators" = @("equals", "notEquals", "contains", "startsWith", "endsWith")
                    "examples" = @(
                        "eq(variables['DeployToProduction'], 'true')",
                        "ne(variables['SkipTests'], 'true')",
                        "contains(variables['BuildConfiguration'], 'Release')"
                    )
                }
                "time" = @{
                    "name" = "Time-based Conditions"
                    "description" = "Execute based on time/schedule"
                    "operators" = @("dayOfWeek", "timeOfDay", "dateRange")
                    "examples" = @(
                        "eq(variables['Build.Reason'], 'Schedule')",
                        "and(eq(variables['Build.Reason'], 'Schedule'), in(format('{0:dddd}', pipeline.startTime), 'Monday', 'Wednesday', 'Friday'))"
                    )
                }
                "file" = @{
                    "name" = "File Change Conditions"
                    "description" = "Execute based on changed files"
                    "operators" = @("contains", "startsWith", "endsWith", "matches")
                    "examples" = @(
                        "contains(variables['Build.SourcesDirectory'], 'src/')",
                        "or(contains(variables['System.PullRequest.SourceBranch'], 'feature/'), contains(variables['Build.SourceBranch'], 'refs/heads/feature/'))"
                    )
                }
                "custom" = @{
                    "name" = "Custom Conditions"
                    "description" = "User-defined custom logic"
                    "operators" = @("and", "or", "not", "eq", "ne", "gt", "lt", "ge", "le")
                    "examples" = @(
                        "and(succeeded(), eq(variables['Environment'], 'prod'), ne(variables['SkipDeployment'], 'true'))",
                        "or(eq(variables['Build.Reason'], 'Manual'), eq(variables['Build.Reason'], 'PullRequest'))"
                    )
                }
            }
            "templates" = @{
                "productionDeployment" = @{
                    "name" = "Production Deployment"
                    "condition" = "and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'), eq(variables['Environment'], 'production'))"
                    "description" = "Deploy to production only from main branch with successful build"
                }
                "featureBranch" = @{
                    "name" = "Feature Branch Testing"
                    "condition" = "and(succeeded(), startsWith(variables['Build.SourceBranch'], 'refs/heads/feature/'), eq(variables['RunTests'], 'true'))"
                    "description" = "Run tests for feature branches"
                }
                "pullRequest" = @{
                    "name" = "Pull Request Validation"
                    "condition" = "eq(variables['Build.Reason'], 'PullRequest')"
                    "description" = "Execute validation steps for pull requests"
                }
                "scheduledBuild" = @{
                    "name" = "Scheduled Build"
                    "condition" = "eq(variables['Build.Reason'], 'Schedule')"
                    "description" = "Execute for scheduled/nightly builds"
                }
                "manualDeployment" = @{
                    "name" = "Manual Deployment"
                    "condition" = "and(eq(variables['Build.Reason'], 'Manual'), ne(variables['SkipDeployment'], 'true'))"
                    "description" = "Execute for manual deployments only"
                }
            }
        }
        
        $defaultConfig | ConvertTo-Json -Depth 10 | Set-Content -Path $ConditionsFile -Encoding UTF8
        Write-LogicLog "Default conditional logic configuration created" "SUCCESS"
    }
    
    return Get-Content $ConditionsFile | ConvertFrom-Json
}

function Show-ConditionalMenu {
    Write-Host ""
    Write-ColorText "🔀 Conditional Logic Builder Menu" "Cyan"
    Write-Host ""
    Write-ColorText "    [1] 🎯 Build Simple Condition" "Green"
    Write-ColorText "        └─ Create basic conditional logic with guided prompts"
    Write-Host ""
    Write-ColorText "    [2] 🔧 Build Complex Condition" "Blue"
    Write-ColorText "        └─ Create advanced multi-part conditional logic"
    Write-Host ""
    Write-ColorText "    [3] 📋 Use Condition Template" "Yellow"
    Write-ColorText "        └─ Select from pre-built condition templates"
    Write-Host ""
    Write-ColorText "    [4] ✅ Validate Condition" "Magenta"
    Write-ColorText "        └─ Test and validate conditional logic syntax"
    Write-Host ""
    Write-ColorText "    [5] 📊 View Examples" "Cyan"
    Write-ColorText "        └─ Browse condition examples and documentation"
    Write-Host ""
    Write-ColorText "    [6] 💾 Export Conditions" "White"
    Write-ColorText "        └─ Export conditions for pipeline integration"
    Write-Host ""
    Write-ColorText "    [0] 🚪 Exit" "Gray"
    Write-Host ""
}

function Build-SimpleCondition {
    param([object]$Config)
    
    Write-Host ""
    Write-ColorText "🎯 Simple Condition Builder" "Cyan"
    Write-Host ""
    
    # Select condition type
    Write-ColorText "Select condition type:" "Yellow"
    $types = @($Config.conditionTypes.Keys)
    for ($i = 0; $i -lt $types.Count; $i++) {
        $type = $types[$i]
        $typeConfig = $Config.conditionTypes.$type
        Write-ColorText "  [$($i + 1)] $($typeConfig.name)" "White"
        Write-ColorText "      $($typeConfig.description)" "Gray"
    }
    
    $typeChoice = Read-Host "Enter choice (1-$($types.Count))"
    if ([int]$typeChoice -lt 1 -or [int]$typeChoice -gt $types.Count) {
        Write-LogicLog "Invalid choice" "ERROR"
        return $null
    }
    
    $selectedType = $types[[int]$typeChoice - 1]
    $typeConfig = $Config.conditionTypes.$selectedType
    
    Write-Host ""
    Write-ColorText "Building $($typeConfig.name) condition..." "INFO"
    
    $condition = switch ($selectedType) {
        "branch" { Build-BranchCondition }
        "environment" { Build-EnvironmentCondition }
        "buildResult" { Build-BuildResultCondition }
        "variable" { Build-VariableCondition }
        "time" { Build-TimeCondition }
        "file" { Build-FileCondition }
        "custom" { Build-CustomCondition }
    }
    
    if ($condition) {
        Write-Host ""
        Write-ColorText "Generated condition:" "SUCCESS"
        Write-ColorText $condition "Green"
        
        return @{
            "type" = $selectedType
            "condition" = $condition
            "description" = "User-generated $($typeConfig.name.ToLower())"
            "platform" = $Platform
        }
    }
    
    return $null
}

function Build-BranchCondition {
    Write-ColorText "Branch condition options:" "Yellow"
    Write-ColorText "  [1] Specific branch (e.g., main, develop)" "White"
    Write-ColorText "  [2] Branch pattern (e.g., feature/*, release/*)" "White"
    Write-ColorText "  [3] Multiple branches" "White"
    
    $choice = Read-Host "Enter choice (1-3)"
    
    switch ($choice) {
        "1" {
            $branch = Read-Host "Enter branch name (e.g., main, develop)"
            if ($Platform -eq "azuredevops") {
                return "eq(variables['Build.SourceBranch'], 'refs/heads/$branch')"
            } else {
                return "github.ref == 'refs/heads/$branch'"
            }
        }
        "2" {
            $pattern = Read-Host "Enter branch pattern (e.g., feature/, release/)"
            if ($Platform -eq "azuredevops") {
                return "startsWith(variables['Build.SourceBranch'], 'refs/heads/$pattern')"
            } else {
                return "startsWith(github.ref, 'refs/heads/$pattern')"
            }
        }
        "3" {
            $branches = Read-Host "Enter branch names (comma-separated, e.g., main,develop,master)"
            $branchArray = $branches -split "," | ForEach-Object { $_.Trim() }
            if ($Platform -eq "azuredevops") {
                $branchRefs = $branchArray | ForEach-Object { "'refs/heads/$_'" }
                return "in(variables['Build.SourceBranch'], $($branchRefs -join ', '))"
            } else {
                $branchRefs = $branchArray | ForEach-Object { "'refs/heads/$_'" }
                return "contains([$($branchRefs -join ', ')], github.ref)"
            }
        }
    }
    
    return $null
}

function Build-EnvironmentCondition {
    $environment = Read-Host "Enter environment name (e.g., dev, staging, prod)"
    
    if ($Platform -eq "azuredevops") {
        return "eq(variables['Environment'], '$environment')"
    } else {
        return "github.event.deployment.environment == '$environment'"
    }
}

function Build-BuildResultCondition {
    Write-ColorText "Build result options:" "Yellow"
    Write-ColorText "  [1] Succeeded" "White"
    Write-ColorText "  [2] Failed" "White"
    Write-ColorText "  [3] Succeeded or Failed" "White"
    Write-ColorText "  [4] Canceled" "White"
    
    $choice = Read-Host "Enter choice (1-4)"
    
    switch ($choice) {
        "1" { return "succeeded()" }
        "2" { return "failed()" }
        "3" { return "succeededOrFailed()" }
        "4" { return "canceled()" }
    }
    
    return $null
}

function Build-VariableCondition {
    $varName = Read-Host "Enter variable name"
    $varValue = Read-Host "Enter variable value"
    
    Write-ColorText "Comparison operator:" "Yellow"
    Write-ColorText "  [1] Equals" "White"
    Write-ColorText "  [2] Not equals" "White"
    Write-ColorText "  [3] Contains" "White"
    
    $choice = Read-Host "Enter choice (1-3)"
    
    $operator = switch ($choice) {
        "1" { "eq" }
        "2" { "ne" }
        "3" { "contains" }
        default { "eq" }
    }
    
    if ($Platform -eq "azuredevops") {
        return "$operator(variables['$varName'], '$varValue')"
    } else {
        $ghOperator = switch ($operator) {
            "eq" { "==" }
            "ne" { "!=" }
            "contains" { "contains" }
        }
        
        if ($operator -eq "contains") {
            return "contains(env.$varName, '$varValue')"
        } else {
            return "env.$varName $ghOperator '$varValue'"
        }
    }
}

function Build-TimeCondition {
    Write-ColorText "Time-based condition options:" "Yellow"
    Write-ColorText "  [1] Scheduled build only" "White"
    Write-ColorText "  [2] Specific day of week" "White"
    Write-ColorText "  [3] Manual trigger only" "White"
    
    $choice = Read-Host "Enter choice (1-3)"
    
    switch ($choice) {
        "1" {
            if ($Platform -eq "azuredevops") {
                return "eq(variables['Build.Reason'], 'Schedule')"
            } else {
                return "github.event_name == 'schedule'"
            }
        }
        "2" {
            $day = Read-Host "Enter day of week (e.g., Monday, Friday)"
            if ($Platform -eq "azuredevops") {
                return "and(eq(variables['Build.Reason'], 'Schedule'), eq(format('{0:dddd}', pipeline.startTime), '$day'))"
            } else {
                return "github.event_name == 'schedule' && format('{0:dddd}', github.event.schedule) == '$day'"
            }
        }
        "3" {
            if ($Platform -eq "azuredevops") {
                return "eq(variables['Build.Reason'], 'Manual')"
            } else {
                return "github.event_name == 'workflow_dispatch'"
            }
        }
    }
    
    return $null
}

function Build-FileCondition {
    $path = Read-Host "Enter file/folder path pattern (e.g., src/, *.cs, docs/**)"
    
    if ($Platform -eq "azuredevops") {
        return "contains(variables['Build.SourcesDirectory'], '$path')"
    } else {
        return "contains(github.event.head_commit.modified, '$path')"
    }
}

function Build-CustomCondition {
    Write-ColorText "Enter custom condition expression:" "Yellow"
    Write-ColorText "Examples:" "Gray"
    Write-ColorText "  and(succeeded(), eq(variables['Environment'], 'prod'))" "Gray"
    Write-ColorText "  or(eq(variables['Build.Reason'], 'Manual'), eq(variables['Build.Reason'], 'PullRequest'))" "Gray"
    
    $condition = Read-Host "Custom condition"
    return $condition
}

function Build-ComplexCondition {
    param([object]$Config)
    
    Write-Host ""
    Write-ColorText "🔧 Complex Condition Builder" "Cyan"
    Write-Host ""
    
    $conditions = @()
    $operators = @()
    
    do {
        Write-ColorText "Building condition part $($conditions.Count + 1)..." "INFO"
        $condition = Build-SimpleCondition $Config
        
        if ($condition) {
            $conditions += $condition.condition
            
            if ($conditions.Count -gt 1) {
                Write-ColorText "Combine with previous condition using:" "Yellow"
                Write-ColorText "  [1] AND" "White"
                Write-ColorText "  [2] OR" "White"
                
                $opChoice = Read-Host "Enter choice (1-2)"
                $operator = if ($opChoice -eq "2") { "or" } else { "and" }
                $operators += $operator
            }
        }
        
        $continue = Read-Host "Add another condition? (y/N)"
    } while ($continue -eq 'y' -or $continue -eq 'Y')
    
    if ($conditions.Count -eq 1) {
        return @{
            "type" = "complex"
            "condition" = $conditions[0]
            "description" = "Complex condition with single part"
            "platform" = $Platform
        }
    } elseif ($conditions.Count -gt 1) {
        $complexCondition = $conditions[0]
        for ($i = 1; $i -lt $conditions.Count; $i++) {
            $complexCondition = "$($operators[$i-1])($complexCondition, $($conditions[$i]))"
        }
        
        return @{
            "type" = "complex"
            "condition" = $complexCondition
            "description" = "Complex condition with $($conditions.Count) parts"
            "platform" = $Platform
        }
    }
    
    return $null
}

function Validate-Condition {
    $condition = Read-Host "Enter condition to validate"
    
    Write-LogicLog "Validating condition: $condition" "INFO"
    
    # Basic syntax validation
    $errors = @()
    
    # Check for balanced parentheses
    $openParens = ($condition.ToCharArray() | Where-Object { $_ -eq '(' }).Count
    $closeParens = ($condition.ToCharArray() | Where-Object { $_ -eq ')' }).Count
    
    if ($openParens -ne $closeParens) {
        $errors += "Unbalanced parentheses: $openParens open, $closeParens close"
    }
    
    # Check for valid operators
    $validOperators = @("and", "or", "not", "eq", "ne", "gt", "lt", "ge", "le", "succeeded", "failed", "canceled", "succeededOrFailed", "startsWith", "endsWith", "contains", "in")
    
    foreach ($op in $validOperators) {
        if ($condition -match "\b$op\b") {
            Write-LogicLog "Found valid operator: $op" "SUCCESS"
        }
    }
    
    # Check for common mistakes
    if ($condition -match "==" -and $Platform -eq "azuredevops") {
        $errors += "Use 'eq()' instead of '==' for Azure DevOps"
    }
    
    if ($condition -match "\beq\b" -and $Platform -eq "github") {
        $errors += "Use '==' instead of 'eq()' for GitHub Actions"
    }
    
    if ($errors.Count -eq 0) {
        Write-LogicLog "Condition validation passed!" "SUCCESS"
        return $true
    } else {
        Write-LogicLog "Validation errors found:" "ERROR"
        foreach ($error in $errors) {
            Write-LogicLog "  - $error" "ERROR"
        }
        return $false
    }
}

function Show-Examples {
    param([object]$Config)
    
    Write-Host ""
    Write-ColorText "📊 Condition Examples" "Cyan"
    Write-Host ""
    
    foreach ($type in $Config.conditionTypes.Keys) {
        $typeConfig = $Config.conditionTypes.$type
        Write-ColorText "$($typeConfig.name):" "Yellow"
        Write-ColorText "  $($typeConfig.description)" "Gray"
        
        foreach ($example in $typeConfig.examples) {
            Write-ColorText "    • $example" "White"
        }
        Write-Host ""
    }
    
    Write-ColorText "Templates:" "Yellow"
    foreach ($template in $Config.templates.Keys) {
        $templateConfig = $Config.templates.$template
        Write-ColorText "  $($templateConfig.name):" "White"
        Write-ColorText "    $($templateConfig.description)" "Gray"
        Write-ColorText "    Condition: $($templateConfig.condition)" "Cyan"
        Write-Host ""
    }
}

# Main execution
Show-Header

$config = Initialize-ConditionalLogic

if ($Action -eq "menu") {
    do {
        Show-ConditionalMenu
        $choice = Read-Host "Enter your choice (0-6)"
        
        switch ($choice) {
            "1" {
                $result = Build-SimpleCondition $config
                if ($result) {
                    Write-LogicLog "Simple condition created successfully" "SUCCESS"
                }
                Read-Host "Press Enter to continue"
            }
            "2" {
                $result = Build-ComplexCondition $config
                if ($result) {
                    Write-LogicLog "Complex condition created successfully" "SUCCESS"
                }
                Read-Host "Press Enter to continue"
            }
            "3" {
                Write-Host ""
                Write-ColorText "📋 Available Templates:" "Cyan"
                $templates = @($config.templates.Keys)
                for ($i = 0; $i -lt $templates.Count; $i++) {
                    $template = $templates[$i]
                    $templateConfig = $config.templates.$template
                    Write-ColorText "  [$($i + 1)] $($templateConfig.name)" "White"
                    Write-ColorText "      $($templateConfig.description)" "Gray"
                }
                
                $templateChoice = Read-Host "Select template (1-$($templates.Count))"
                if ([int]$templateChoice -ge 1 -and [int]$templateChoice -le $templates.Count) {
                    $selectedTemplate = $templates[[int]$templateChoice - 1]
                    $templateConfig = $config.templates.$selectedTemplate
                    Write-ColorText "Selected condition: $($templateConfig.condition)" "Green"
                }
                Read-Host "Press Enter to continue"
            }
            "4" {
                Validate-Condition
                Read-Host "Press Enter to continue"
            }
            "5" {
                Show-Examples $config
                Read-Host "Press Enter to continue"
            }
            "6" {
                Write-LogicLog "Export functionality coming soon..." "INFO"
                Read-Host "Press Enter to continue"
            }
            "0" {
                Write-LogicLog "Goodbye!" "INFO"
                break
            }
            default {
                Write-LogicLog "Invalid choice. Please try again." "WARN"
                Read-Host "Press Enter to continue"
            }
        }
    } while ($choice -ne "0")
}
