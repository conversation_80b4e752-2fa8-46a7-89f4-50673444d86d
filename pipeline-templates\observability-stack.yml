# Observability Stack for Notify Service API
# Includes Prometheus, Graf<PERSON>, <PERSON><PERSON><PERSON>, and ELK Stack

version: '3.8'

services:
  # Prometheus - Metrics Collection
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: notify-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=15d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    ports:
      - "9090:9090"
    volumes:
      - ./observability/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./observability/prometheus/rules:/etc/prometheus/rules:ro
      - prometheus_data:/prometheus
    networks:
      - observability
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Grafana - Metrics Visualization
  grafana:
    image: grafana/grafana:10.2.0
    container_name: notify-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
      - GF_FEATURE_TOGGLES_ENABLE=publicDashboards
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./observability/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./observability/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - observability
    restart: unless-stopped
    depends_on:
      - prometheus
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AlertManager - Alert Management
  alertmanager:
    image: prom/alertmanager:v0.26.0
    container_name: notify-alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
      - '--cluster.advertise-address=0.0.0.0:9093'
    ports:
      - "9093:9093"
    volumes:
      - ./observability/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
      - alertmanager_data:/alertmanager
    networks:
      - observability
    restart: unless-stopped
    depends_on:
      - prometheus

  # Jaeger - Distributed Tracing
  jaeger:
    image: jaegertracing/all-in-one:1.50
    container_name: notify-jaeger
    environment:
      - COLLECTOR_OTLP_ENABLED=true
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # Jaeger collector HTTP
      - "14250:14250"  # Jaeger collector gRPC
      - "6831:6831/udp"  # Jaeger agent UDP (compact)
      - "6832:6832/udp"  # Jaeger agent UDP (binary)
      - "5778:5778"    # Jaeger agent configs
      - "9411:9411"    # Zipkin compatible endpoint
    volumes:
      - jaeger_data:/tmp
    networks:
      - observability
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:14269/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Elasticsearch - Log Storage
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: notify-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - bootstrap.memory_lock=true
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
      - ./observability/elasticsearch/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml:ro
    networks:
      - observability
    restart: unless-stopped
    ulimits:
      memlock:
        soft: -1
        hard: -1
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Logstash - Log Processing
  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: notify-logstash
    environment:
      - "LS_JAVA_OPTS=-Xmx512m -Xms512m"
    ports:
      - "5044:5044"  # Beats input
      - "5000:5000/tcp"  # TCP input
      - "5000:5000/udp"  # UDP input
      - "9600:9600"  # Logstash monitoring
    volumes:
      - ./observability/logstash/pipeline:/usr/share/logstash/pipeline:ro
      - ./observability/logstash/config/logstash.yml:/usr/share/logstash/config/logstash.yml:ro
    networks:
      - observability
    restart: unless-stopped
    depends_on:
      - elasticsearch
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9600"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kibana - Log Visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: notify-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - SERVER_NAME=kibana
      - SERVER_HOST=0.0.0.0
    ports:
      - "5601:5601"
    volumes:
      - ./observability/kibana/kibana.yml:/usr/share/kibana/config/kibana.yml:ro
    networks:
      - observability
    restart: unless-stopped
    depends_on:
      - elasticsearch
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Filebeat - Log Shipping
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.11.0
    container_name: notify-filebeat
    user: root
    command: filebeat -e -strict.perms=false
    volumes:
      - ./observability/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - filebeat_data:/usr/share/filebeat/data
    networks:
      - observability
    restart: unless-stopped
    depends_on:
      - logstash

  # Node Exporter - System Metrics
  node-exporter:
    image: prom/node-exporter:v1.6.1
    container_name: notify-node-exporter
    command:
      - '--path.rootfs=/host'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "9100:9100"
    volumes:
      - /:/host:ro,rslave
    networks:
      - observability
    restart: unless-stopped
    pid: host

  # cAdvisor - Container Metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.2
    container_name: notify-cadvisor
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    networks:
      - observability
    restart: unless-stopped
    privileged: true
    devices:
      - /dev/kmsg

  # Redis Exporter - Redis Metrics
  redis-exporter:
    image: oliver006/redis_exporter:v1.55.0
    container_name: notify-redis-exporter
    environment:
      - REDIS_ADDR=redis://redis:6379
      - REDIS_PASSWORD=your_redis_password
    ports:
      - "9121:9121"
    networks:
      - observability
    restart: unless-stopped

  # Postgres Exporter - Database Metrics
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:v0.15.0
    container_name: notify-postgres-exporter
    environment:
      - DATA_SOURCE_NAME=********************************************/notify_db?sslmode=disable
    ports:
      - "9187:9187"
    networks:
      - observability
    restart: unless-stopped

  # Blackbox Exporter - Endpoint Monitoring
  blackbox-exporter:
    image: prom/blackbox-exporter:v0.24.0
    container_name: notify-blackbox-exporter
    command:
      - '--config.file=/etc/blackbox_exporter/config.yml'
    ports:
      - "9115:9115"
    volumes:
      - ./observability/blackbox/config.yml:/etc/blackbox_exporter/config.yml:ro
    networks:
      - observability
    restart: unless-stopped

  # Loki - Log Aggregation (Alternative to ELK)
  loki:
    image: grafana/loki:2.9.0
    container_name: notify-loki
    command: -config.file=/etc/loki/local-config.yaml
    ports:
      - "3100:3100"
    volumes:
      - ./observability/loki/loki-config.yml:/etc/loki/local-config.yaml:ro
      - loki_data:/tmp/loki
    networks:
      - observability
    restart: unless-stopped

  # Promtail - Log Collection for Loki
  promtail:
    image: grafana/promtail:2.9.0
    container_name: notify-promtail
    command: -config.file=/etc/promtail/config.yml
    volumes:
      - ./observability/promtail/promtail-config.yml:/etc/promtail/config.yml:ro
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - observability
    restart: unless-stopped
    depends_on:
      - loki

  # Tempo - Distributed Tracing (Alternative to Jaeger)
  tempo:
    image: grafana/tempo:2.3.0
    container_name: notify-tempo
    command: [ "-config.file=/etc/tempo.yaml" ]
    volumes:
      - ./observability/tempo/tempo.yml:/etc/tempo.yaml:ro
      - tempo_data:/tmp/tempo
    ports:
      - "3200:3200"   # Tempo
      - "4317:4317"   # OTLP gRPC
      - "4318:4318"   # OTLP HTTP
    networks:
      - observability
    restart: unless-stopped

  # OpenTelemetry Collector
  otel-collector:
    image: otel/opentelemetry-collector-contrib:0.89.0
    container_name: notify-otel-collector
    command: ["--config=/etc/otel-collector-config.yaml"]
    volumes:
      - ./observability/otel/otel-collector-config.yaml:/etc/otel-collector-config.yaml:ro
    ports:
      - "4317:4317"   # OTLP gRPC receiver
      - "4318:4318"   # OTLP HTTP receiver
      - "8888:8888"   # Prometheus metrics
      - "8889:8889"   # Prometheus exporter metrics
      - "13133:13133" # Health check
    networks:
      - observability
    restart: unless-stopped
    depends_on:
      - jaeger
      - prometheus

volumes:
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  alertmanager_data:
    driver: local
  jaeger_data:
    driver: local
  elasticsearch_data:
    driver: local
  filebeat_data:
    driver: local
  loki_data:
    driver: local
  tempo_data:
    driver: local

networks:
  observability:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
