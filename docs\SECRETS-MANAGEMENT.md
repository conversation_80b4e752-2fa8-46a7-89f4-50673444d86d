# 🔐 Secrets & Configuration Management Guide

This guide covers comprehensive secrets and configuration management for the Notify Service API across different environments.

## 📋 Table of Contents

- [Overview](#overview)
- [Configuration Files](#configuration-files)
- [Interactive Configuration](#interactive-configuration)
- [Environment-Specific Setup](#environment-specific-setup)
- [Security Best Practices](#security-best-practices)
- [Production Deployment](#production-deployment)
- [Troubleshooting](#troubleshooting)

## 🎯 Overview

The Notify Service API uses a multi-layered configuration approach:

1. **Environment Variables** (`.env` file) - Non-sensitive configuration
2. **Secrets File** (`secrets.json`) - Sensitive data for development
3. **Azure Key Vault** - Production secrets management
4. **Configuration Manager** - Interactive setup tool

## 📁 Configuration Files

### **Environment Variables (.env)**

Contains non-sensitive configuration that can be shared:

```env
# Application Configuration
ASPNETCORE_ENVIRONMENT=Development
API_PORT=8080

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_DB=NotifyDb

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# Docker Configuration
DOCKER_REGISTRY=notifyregistry.azurecr.io
IMAGE_NAME=notify-service-api
```

### **Secrets File (secrets.json)**

Contains sensitive data for local development:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=NotifyDb;Username=postgres;Password=your-password",
    "IdentityConnection": "Host=localhost;Database=NotifyIdentityDb;Username=postgres;Password=your-password"
  },
  "JWTSettings": {
    "Key": "your-super-secret-jwt-key-minimum-32-characters",
    "Issuer": "NotifyServiceAPI",
    "Audience": "NotifyServiceUsers",
    "DurationInMinutes": 60
  },
  "MailSettings": {
    "SmtpUser": "<EMAIL>",
    "SmtpPass": "your-app-password"
  }
}
```

## 🎛️ Interactive Configuration

### **Quick Setup**

Launch the interactive configuration manager:

```powershell
# Interactive setup with guided prompts
.\scripts\config-manager.ps1
```

### **Configuration Options**

#### **1. Quick Development Setup**
```powershell
# Guided setup for local development
.\scripts\config-manager.ps1 -Local
```

#### **2. Database Configuration**
- PostgreSQL host, port, credentials
- Database names and connection strings
- Connection pooling settings

#### **3. Redis Configuration**
- Redis host and port
- Optional password authentication
- Database selection and cache settings

#### **4. Email/SMTP Configuration**
- **Gmail Setup:**
  - Host: `smtp.gmail.com:587`
  - Enable 2FA and generate App Password
  - Use App Password (not regular password)

- **SendGrid Setup:**
  - Host: `smtp.sendgrid.net:587`
  - Username: `apikey`
  - Password: Your SendGrid API key

- **Custom SMTP:**
  - Configure any SMTP provider
  - Support for TLS/SSL

#### **5. JWT & Security**
- Automatic secure key generation
- Configurable token expiration
- Issuer and audience settings

#### **6. Docker Configuration**
- Container registry settings
- Port mappings
- Image naming conventions

#### **7. Azure Deployment**
- Subscription and resource group
- Web app and container registry
- Deployment automation settings

## 🌍 Environment-Specific Setup

### **Development Environment**

```powershell
# Quick development setup
.\scripts\config-manager.ps1 -Local

# What it configures:
# - Local PostgreSQL (localhost:5432)
# - Local Redis (localhost:6379)
# - Development JWT keys
# - Console email provider (for testing)
```

### **Docker Environment**

```powershell
# Docker-specific configuration
.\scripts\config-manager.ps1 -Docker

# What it configures:
# - Container networking
# - Service discovery
# - Volume mappings
# - Environment variable injection
```

### **Production Environment**

```powershell
# Production configuration (requires manual review)
.\scripts\config-manager.ps1 -Production

# What it configures:
# - Azure Key Vault integration
# - Production database connections
# - Secure JWT keys
# - Production SMTP settings
# - Monitoring and logging
```

## 🛡️ Security Best Practices

### **Development Security**

1. **Never commit secrets to Git**
   ```bash
   # .gitignore includes:
   .env
   secrets.json
   *.pfx
   *.key
   ```

2. **Use environment-specific files**
   ```
   .env.development
   .env.staging
   .env.production
   ```

3. **Rotate development keys regularly**
   ```powershell
   # Generate new JWT key
   .\scripts\config-manager.ps1
   # Select "JWT & Security" option
   ```

### **Production Security**

1. **Use Azure Key Vault**
   ```json
   {
     "KeyVault": {
       "VaultUri": "https://notify-keyvault.vault.azure.net/"
     }
   }
   ```

2. **Managed Identity**
   ```csharp
   // In production, use Managed Identity
   services.AddAzureKeyVault(
       new Uri(configuration["KeyVault:VaultUri"]),
       new DefaultAzureCredential()
   );
   ```

3. **Environment Variables Only**
   ```bash
   # Production uses only environment variables
   export ConnectionStrings__DefaultConnection="..."
   export JWTSettings__Key="..."
   ```

## ☁️ Production Deployment

### **Azure App Service Configuration**

```bash
# Set application settings via Azure CLI
az webapp config appsettings set \
  --name notify-service-api \
  --resource-group notify-service-rg \
  --settings \
    ASPNETCORE_ENVIRONMENT="Production" \
    JWTSettings__Key="@Microsoft.KeyVault(SecretUri=https://notify-keyvault.vault.azure.net/secrets/jwt-key/)" \
    ConnectionStrings__DefaultConnection="@Microsoft.KeyVault(SecretUri=https://notify-keyvault.vault.azure.net/secrets/db-connection/)"
```

### **Azure Key Vault Setup**

```bash
# Create Key Vault
az keyvault create \
  --name notify-keyvault \
  --resource-group notify-service-rg \
  --location eastus

# Add secrets
az keyvault secret set \
  --vault-name notify-keyvault \
  --name "jwt-key" \
  --value "your-production-jwt-key"

az keyvault secret set \
  --vault-name notify-keyvault \
  --name "db-connection" \
  --value "your-production-connection-string"
```

### **Docker Production Configuration**

```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  api:
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=${DB_CONNECTION_STRING}
      - JWTSettings__Key=${JWT_SECRET_KEY}
      - MailSettings__SmtpPass=${SMTP_PASSWORD}
```

## 🔧 Configuration Export/Import

### **Export Configuration**

```powershell
# Export current configuration
.\scripts\config-manager.ps1
# Select "Export Configuration"

# Creates: config-exports/config-development-20241219-143022.json
```

### **Import Configuration**

```powershell
# Import from exported file
.\scripts\config-manager.ps1
# Select "Import Configuration"
# Choose from available export files
```

### **Team Sharing**

```powershell
# Share non-sensitive configuration
git add .env.template
git commit -m "Update configuration template"

# Team members can then:
cp .env.template .env
# Edit .env with their specific values
```

## 🧪 Testing Configuration

### **Validate Settings**

```powershell
# Test database connection
dotnet ef database update --dry-run

# Test Redis connection
redis-cli -h localhost -p 6379 ping

# Test SMTP settings
# Use the built-in health checks at /health
```

### **Health Checks**

The API includes comprehensive health checks:

```
GET /health
{
  "status": "Healthy",
  "checks": {
    "database": "Healthy",
    "redis": "Healthy",
    "smtp": "Healthy"
  }
}
```

## 🚨 Troubleshooting

### **Common Issues**

1. **JWT Key Too Short**
   ```
   Error: JWT key must be at least 32 characters
   Solution: Use config manager to generate secure key
   ```

2. **Database Connection Failed**
   ```
   Error: Connection refused
   Solution: Check PostgreSQL is running and credentials are correct
   ```

3. **Redis Connection Failed**
   ```
   Error: No connection could be made
   Solution: Verify Redis is running and password is correct
   ```

4. **SMTP Authentication Failed**
   ```
   Error: Authentication failed
   Solution: Use App Password for Gmail, verify SMTP settings
   ```

### **Debug Configuration**

```powershell
# View current configuration (masks sensitive data)
.\scripts\config-manager.ps1
# Select "View Current Configuration"

# Check environment variables
Get-ChildItem Env: | Where-Object Name -like "*NOTIFY*"

# Validate secrets file
Test-Path "secrets.json"
Get-Content "secrets.json" | ConvertFrom-Json
```

## 📞 Support

For configuration issues:

1. Check the [Deployment Guide](DEPLOYMENT.md)
2. Review the [Architecture Documentation](ARCHITECTURE.md)
3. Use the interactive configuration manager
4. Check application logs for specific error messages

---

**Remember**: Never commit real secrets to version control! 🔐
