# ═══════════════════════════════════════════════════════════════════════════════
# 🎭 Notify Service API - Pipeline Orchestrator
# ═══════════════════════════════════════════════════════════════════════════════
# Master script to orchestrate all pipeline operations, deployment, and monitoring

param(
    [string]$Action = "menu",           # menu, deploy, monitor, setup, config
    [string]$Environment = "",          # dev, staging, prod
    [string]$Branch = "main",           # Git branch
    [switch]$Local,                     # Use local pipeline emulator
    [switch]$Remote,                    # Deploy to remote servers
    [switch]$Monitor,                   # Start monitoring
    [switch]$Force,                     # Force operations
    [switch]$DryRun,                    # Dry run mode
    [switch]$Help                       # Show help
)

# Configuration
$ProjectRoot = Split-Path -Parent (Split-Path -Parent $MyInvocation.MyCommand.Path)
$ScriptsDir = Join-Path $ProjectRoot "scripts"

# Colors
$Colors = @{
    Red = "Red"; Green = "Green"; Yellow = "Yellow"; Blue = "Blue"
    Cyan = "Cyan"; Magenta = "Magenta"; Gray = "Gray"; White = "White"
}

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    Write-Host $Text -ForegroundColor $Colors[$Color]
}

function Write-OrchestratorLog {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARN" { "Yellow" }
        "SUCCESS" { "Green" }
        default { "Cyan" }
    }
    Write-ColorText "[$timestamp] [$Level] $Message" $color
}

function Show-Header {
    Clear-Host
    Write-Host ""
    Write-ColorText "    🎭🎭🎭 PIPELINE ORCHESTRATOR 🎭🎭🎭" "Cyan"
    Write-Host ""
    Write-ColorText "    🚀 Complete Pipeline Management Suite" "Yellow"
    Write-ColorText "    🔧 Build • Deploy • Monitor • Manage" "Gray"
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "Gray"
}

function Show-Help {
    Write-ColorText "🎯 Pipeline Orchestrator - Complete Pipeline Management" "Yellow"
    Write-Host ""
    Write-ColorText "USAGE:" "White"
    Write-ColorText "  .\scripts\pipeline-orchestrator.ps1 [OPTIONS]" "Gray"
    Write-Host ""
    Write-ColorText "ACTIONS:" "White"
    Write-ColorText "  menu       Interactive menu (default)" "Gray"
    Write-ColorText "  deploy     Deploy to environment" "Gray"
    Write-ColorText "  monitor    Start monitoring" "Gray"
    Write-ColorText "  setup      Setup pipeline infrastructure" "Gray"
    Write-ColorText "  config     Manage configuration" "Gray"
    Write-Host ""
    Write-ColorText "OPTIONS:" "White"
    Write-ColorText "  -Environment   Target environment (dev/staging/prod)" "Gray"
    Write-ColorText "  -Branch        Git branch to deploy (default: main)" "Gray"
    Write-ColorText "  -Local         Use local pipeline emulator" "Gray"
    Write-ColorText "  -Remote        Deploy to remote servers" "Gray"
    Write-ColorText "  -Monitor       Start monitoring after deployment" "Gray"
    Write-ColorText "  -Force         Force operations" "Gray"
    Write-ColorText "  -DryRun        Dry run mode" "Gray"
    Write-ColorText "  -Help          Show this help" "Gray"
    Write-Host ""
    Write-ColorText "EXAMPLES:" "White"
    Write-ColorText "  .\scripts\pipeline-orchestrator.ps1                           # Interactive menu" "Gray"
    Write-ColorText "  .\scripts\pipeline-orchestrator.ps1 -Action deploy -Environment dev -Local" "Gray"
    Write-ColorText "  .\scripts\pipeline-orchestrator.ps1 -Action monitor -Environment prod" "Gray"
    Write-Host ""
}

function Invoke-ScriptSafely {
    param(
        [string]$ScriptPath,
        [string[]]$Arguments = @(),
        [string]$Description = ""
    )
    
    if (-not (Test-Path $ScriptPath)) {
        Write-OrchestratorLog "Script not found: $ScriptPath" "ERROR"
        return $false
    }
    
    try {
        Write-OrchestratorLog "Starting: $Description" "INFO"
        
        if ($DryRun) {
            Write-OrchestratorLog "DRY RUN: Would execute: $ScriptPath $($Arguments -join ' ')" "INFO"
            return $true
        }
        
        $process = Start-Process -FilePath "powershell.exe" -ArgumentList @("-File", $ScriptPath) + $Arguments -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-OrchestratorLog "Completed successfully: $Description" "SUCCESS"
            return $true
        } else {
            Write-OrchestratorLog "Failed with exit code $($process.ExitCode): $Description" "ERROR"
            return $false
        }
    } catch {
        Write-OrchestratorLog "Exception in $Description`: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Start-LocalPipeline {
    param([string]$Environment, [string]$Branch)
    
    Write-OrchestratorLog "Starting local pipeline for $Environment environment..." "INFO"
    
    $emulatorScript = Join-Path $ScriptsDir "local-pipeline-emulator.ps1"
    $arguments = @()
    
    if ($Environment) { $arguments += @("-Environment", $Environment) }
    if ($DryRun) { $arguments += "-DryRun" }
    
    return Invoke-ScriptSafely -ScriptPath $emulatorScript -Arguments $arguments -Description "Local Pipeline Emulation"
}

function Start-RemoteDeployment {
    param([string]$Environment, [string]$Branch)
    
    Write-OrchestratorLog "Starting remote deployment to $Environment..." "INFO"
    
    # First, trigger the Azure DevOps pipeline
    $pipelineScript = Join-Path $ScriptsDir "pipeline-manager.ps1"
    $pipelineArgs = @("-Action", "trigger", "-Environment", $Environment, "-Branch", $Branch)
    if ($DryRun) { $pipelineArgs += "-DryRun" }
    
    $pipelineSuccess = Invoke-ScriptSafely -ScriptPath $pipelineScript -Arguments $pipelineArgs -Description "Azure DevOps Pipeline Trigger"
    
    if (-not $pipelineSuccess) {
        Write-OrchestratorLog "Pipeline trigger failed, aborting deployment" "ERROR"
        return $false
    }
    
    # Then configure remote servers if needed
    $remoteScript = Join-Path $ScriptsDir "remote-deploy-setup.ps1"
    $remoteArgs = @("-Action", "deploy", "-Environment", $Environment)
    if ($Force) { $remoteArgs += "-Force" }
    if ($DryRun) { $remoteArgs += "-DryRun" }
    
    return Invoke-ScriptSafely -ScriptPath $remoteScript -Arguments $remoteArgs -Description "Remote Server Deployment"
}

function Start-Monitoring {
    param([string]$Environment)
    
    Write-OrchestratorLog "Starting monitoring for $Environment..." "INFO"
    
    $monitorScript = Join-Path $ScriptsDir "pipeline-monitor.ps1"
    $monitorArgs = @("-Action", "monitor", "-Continuous", "-Dashboard", "-Alerts")
    
    if ($Environment) { $monitorArgs += @("-Environment", $Environment) }
    
    return Invoke-ScriptSafely -ScriptPath $monitorScript -Arguments $monitorArgs -Description "Pipeline Monitoring"
}

function Setup-PipelineInfrastructure {
    Write-OrchestratorLog "Setting up pipeline infrastructure..." "INFO"
    
    $success = $true
    
    # Setup OAuth and secrets
    $oauthScript = Join-Path $ScriptsDir "oauth-secrets-manager.ps1"
    if (-not (Invoke-ScriptSafely -ScriptPath $oauthScript -Arguments @("-Action", "setup") -Description "OAuth Setup")) {
        $success = $false
    }
    
    # Configure pipeline variables
    $configScript = Join-Path $ScriptsDir "pipeline-config-manager.ps1"
    if (-not (Invoke-ScriptSafely -ScriptPath $configScript -Arguments @("-Action", "init") -Description "Pipeline Configuration")) {
        $success = $false
    }
    
    return $success
}

function Show-MainMenu {
    Write-Host ""
    Write-ColorText "🎭 Pipeline Orchestrator - Main Menu" "Cyan"
    Write-Host ""
    Write-ColorText "    [1] 🚀 Deploy Application" "Green"
    Write-ColorText "        └─ Deploy to development, staging, or production"
    Write-Host ""
    Write-ColorText "    [2] 🏗️ Local Pipeline Emulator" "Blue"
    Write-ColorText "        └─ Run pipeline steps locally for testing"
    Write-Host ""
    Write-ColorText "    [3] 📊 Start Monitoring" "Yellow"
    Write-ColorText "        └─ Monitor deployments and service health"
    Write-Host ""
    Write-ColorText "    [4] ⚙️ Manage Configuration" "Magenta"
    Write-ColorText "        └─ Configure variables, secrets, and settings"
    Write-Host ""
    Write-ColorText "    [5] 🌐 Remote Server Management" "Cyan"
    Write-ColorText "        └─ Setup and manage remote deployment servers"
    Write-Host ""
    Write-ColorText "    [6] 🔐 OAuth & Secrets" "Gray"
    Write-ColorText "        └─ Manage authentication and secrets"
    Write-Host ""
    Write-ColorText "    [7] 🔧 Setup Infrastructure" "White"
    Write-ColorText "        └─ Initial setup of pipeline infrastructure"
    Write-Host ""
    Write-ColorText "    [0] 🚪 Exit" "Gray"
    Write-Host ""
}

function Show-DeploymentMenu {
    Write-Host ""
    Write-ColorText "🚀 Deployment Options" "Green"
    Write-Host ""
    Write-ColorText "    [1] 🧪 Development Environment" "Blue"
    Write-ColorText "        └─ Deploy to dev environment"
    Write-Host ""
    Write-ColorText "    [2] 🎭 Staging Environment" "Yellow"
    Write-ColorText "        └─ Deploy to staging environment"
    Write-Host ""
    Write-ColorText "    [3] 🏭 Production Environment" "Red"
    Write-ColorText "        └─ Deploy to production environment"
    Write-Host ""
    Write-ColorText "    [4] 🏠 Local Emulation" "Cyan"
    Write-ColorText "        └─ Run pipeline locally"
    Write-Host ""
    Write-ColorText "    [0] ⬅️ Back to Main Menu" "Gray"
    Write-Host ""
}

# Main execution
if ($Help) {
    Show-Header
    Show-Help
    exit 0
}

Show-Header

# Handle command line actions
if ($Action -ne "menu") {
    switch ($Action.ToLower()) {
        "deploy" {
            if (-not $Environment) {
                Write-OrchestratorLog "Environment parameter required for deploy action" "ERROR"
                exit 1
            }
            
            $success = if ($Local) {
                Start-LocalPipeline -Environment $Environment -Branch $Branch
            } else {
                Start-RemoteDeployment -Environment $Environment -Branch $Branch
            }
            
            if ($success -and $Monitor) {
                Start-Monitoring -Environment $Environment
            }
            
            exit $(if ($success) { 0 } else { 1 })
        }
        "monitor" {
            Start-Monitoring -Environment $Environment
            exit 0
        }
        "setup" {
            $success = Setup-PipelineInfrastructure
            exit $(if ($success) { 0 } else { 1 })
        }
        "config" {
            $configScript = Join-Path $ScriptsDir "pipeline-config-manager.ps1"
            Invoke-ScriptSafely -ScriptPath $configScript -Description "Configuration Management"
            exit 0
        }
        default {
            Write-OrchestratorLog "Unknown action: $Action" "ERROR"
            Show-Help
            exit 1
        }
    }
}

# Interactive menu
do {
    Show-MainMenu
    $choice = Read-Host "Enter your choice (0-7)"
    
    switch ($choice) {
        "1" {
            do {
                Show-DeploymentMenu
                $deployChoice = Read-Host "Enter your choice (0-4)"
                
                switch ($deployChoice) {
                    "1" { 
                        $success = Start-RemoteDeployment -Environment "dev" -Branch $Branch
                        if ($success) {
                            $monitor = Read-Host "Start monitoring? (Y/n)"
                            if ($monitor -ne 'n' -and $monitor -ne 'N') {
                                Start-Monitoring -Environment "dev"
                            }
                        }
                    }
                    "2" { 
                        $success = Start-RemoteDeployment -Environment "staging" -Branch $Branch
                        if ($success) {
                            $monitor = Read-Host "Start monitoring? (Y/n)"
                            if ($monitor -ne 'n' -and $monitor -ne 'N') {
                                Start-Monitoring -Environment "staging"
                            }
                        }
                    }
                    "3" { 
                        Write-ColorText "⚠️ Production deployment requires confirmation" "Yellow"
                        $confirm = Read-Host "Are you sure you want to deploy to PRODUCTION? (yes/no)"
                        if ($confirm -eq "yes") {
                            $success = Start-RemoteDeployment -Environment "prod" -Branch $Branch
                            if ($success) {
                                Start-Monitoring -Environment "prod"
                            }
                        }
                    }
                    "4" { Start-LocalPipeline -Environment "dev" -Branch $Branch }
                    "0" { break }
                    default { Write-OrchestratorLog "Invalid choice" "WARN" }
                }
                
                if ($deployChoice -ne "0") {
                    Read-Host "Press Enter to continue"
                }
            } while ($deployChoice -ne "0")
        }
        "2" {
            $env = Read-Host "Enter environment (dev/staging/prod)"
            if (-not $env) { $env = "dev" }
            Start-LocalPipeline -Environment $env -Branch $Branch
            Read-Host "Press Enter to continue"
        }
        "3" {
            $env = Read-Host "Enter environment to monitor (dev/staging/prod/all)"
            if (-not $env) { $env = "all" }
            Start-Monitoring -Environment $env
            Read-Host "Press Enter to continue"
        }
        "4" {
            $configScript = Join-Path $ScriptsDir "pipeline-config-manager.ps1"
            Invoke-ScriptSafely -ScriptPath $configScript -Description "Configuration Management"
            Read-Host "Press Enter to continue"
        }
        "5" {
            $remoteScript = Join-Path $ScriptsDir "remote-deploy-setup.ps1"
            Invoke-ScriptSafely -ScriptPath $remoteScript -Description "Remote Server Management"
            Read-Host "Press Enter to continue"
        }
        "6" {
            $oauthScript = Join-Path $ScriptsDir "oauth-secrets-manager.ps1"
            Invoke-ScriptSafely -ScriptPath $oauthScript -Description "OAuth & Secrets Management"
            Read-Host "Press Enter to continue"
        }
        "7" {
            Setup-PipelineInfrastructure
            Read-Host "Press Enter to continue"
        }
        "0" {
            Write-OrchestratorLog "Goodbye!" "INFO"
            break
        }
        default {
            Write-OrchestratorLog "Invalid choice. Please try again." "WARN"
            Read-Host "Press Enter to continue"
        }
    }
} while ($choice -ne "0")
