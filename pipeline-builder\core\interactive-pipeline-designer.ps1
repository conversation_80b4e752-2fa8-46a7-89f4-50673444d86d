# ═══════════════════════════════════════════════════════════════════════════════
# 🎨 Interactive Pipeline Designer
# ═══════════════════════════════════════════════════════════════════════════════
# User-friendly interface for designing CI/CD pipelines with visual workflow builder

param(
    [string]$Action = "menu",           # menu, design, load, save, export
    [string]$PipelineFile = "",         # Pipeline file to load/save
    [string]$Platform = "azuredevops",  # azuredevops, github
    [string]$OutputPath = "",           # Output directory
    [switch]$Interactive,               # Interactive mode
    [switch]$Json                       # JSON output
)

# Configuration
$ScriptRoot = Split-Path -Parent $MyInvocation.MyCommand.Path
$DesignerConfigFile = Join-Path $ScriptRoot "../config/pipeline-designer-config.json"
$WorkspaceDir = Join-Path $ScriptRoot "../workspace"
$TemplatesDir = Join-Path $ScriptRoot "../templates"

# Ensure directories exist
@($WorkspaceDir, (Split-Path -Parent $DesignerConfigFile)) | ForEach-Object {
    if (-not (Test-Path $_)) {
        New-Item -ItemType Directory -Path $_ | Out-Null
    }
}

# Colors
$Colors = @{
    Red = "Red"; Green = "Green"; Yellow = "Yellow"; Blue = "Blue"
    Cyan = "Cyan"; Magenta = "Magenta"; Gray = "Gray"; White = "White"
    DarkGray = "DarkGray"; DarkGreen = "DarkGreen"; DarkBlue = "DarkBlue"
}

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    if (-not $Json) { Write-Host $Text -ForegroundColor $Colors[$Color] }
}

function Write-DesignerLog {
    param([string]$Message, [string]$Level = "INFO")

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    if (-not $Json) {
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            default { "Cyan" }
        }
        Write-ColorText "[$timestamp] [$Level] $Message" $color
    }
}

function Show-Header {
    if ($Json) { return }

    Clear-Host
    Write-Host ""
    Write-ColorText "    🎨🎨🎨 INTERACTIVE PIPELINE DESIGNER 🎨🎨🎨" "Cyan"
    Write-Host ""
    Write-ColorText "    🖱️ Visual Builder • Drag & Drop • Menu-Driven Design" "Yellow"
    Write-ColorText "    🏗️ Azure DevOps • GitHub Actions • Custom Workflows" "Gray"
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "Gray"
}

function Initialize-Designer {
    Write-DesignerLog "Initializing pipeline designer..." "INFO"

    if (-not (Test-Path $DesignerConfigFile)) {
        $defaultConfig = @{
            "designerSettings" = @{
                "defaultPlatform" = "azuredevops"
                "autoSave" = $true
                "showPreview" = $true
                "validateOnChange" = $true
                "theme" = "default"
            }
            "componentLibrary" = @{
                "triggers" = @{
                    "push" = @{
                        "name" = "Push Trigger"
                        "description" = "Trigger on code push to repository"
                        "icon" = "📤"
                        "category" = "triggers"
                        "platforms" = @("azuredevops", "github")
                        "properties" = @{
                            "branches" = @("main", "develop", "feature/*")
                            "paths" = @("src/**", "tests/**")
                        }
                    }
                    "pullRequest" = @{
                        "name" = "Pull Request Trigger"
                        "description" = "Trigger on pull request events"
                        "icon" = "🔀"
                        "category" = "triggers"
                        "platforms" = @("azuredevops", "github")
                        "properties" = @{
                            "branches" = @("main", "develop")
                            "types" = @("opened", "synchronize", "reopened")
                        }
                    }
                    "schedule" = @{
                        "name" = "Scheduled Trigger"
                        "description" = "Trigger on schedule/cron"
                        "icon" = "⏰"
                        "category" = "triggers"
                        "platforms" = @("azuredevops", "github")
                        "properties" = @{
                            "cron" = "0 2 * * *"
                            "timezone" = "UTC"
                        }
                    }
                }
                "stages" = @{
                    "build" = @{
                        "name" = "Build Stage"
                        "description" = "Compile and build application"
                        "icon" = "🔨"
                        "category" = "stages"
                        "platforms" = @("azuredevops", "github")
                        "properties" = @{
                            "displayName" = "Build"
                            "dependsOn" = @()
                            "condition" = "succeeded()"
                        }
                    }
                    "test" = @{
                        "name" = "Test Stage"
                        "description" = "Run automated tests"
                        "icon" = "🧪"
                        "category" = "stages"
                        "platforms" = @("azuredevops", "github")
                        "properties" = @{
                            "displayName" = "Test"
                            "dependsOn" = @("Build")
                            "condition" = "succeeded()"
                        }
                    }
                    "deploy" = @{
                        "name" = "Deploy Stage"
                        "description" = "Deploy to target environment"
                        "icon" = "🚀"
                        "category" = "stages"
                        "platforms" = @("azuredevops", "github")
                        "properties" = @{
                            "displayName" = "Deploy"
                            "dependsOn" = @("Test")
                            "condition" = "and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))"
                        }
                    }
                }
                "jobs" = @{
                    "buildJob" = @{
                        "name" = "Build Job"
                        "description" = "Build application artifacts"
                        "icon" = "⚙️"
                        "category" = "jobs"
                        "platforms" = @("azuredevops", "github")
                        "properties" = @{
                            "displayName" = "Build Application"
                            "pool" = "ubuntu-latest"
                            "variables" = @{}
                        }
                    }
                    "testJob" = @{
                        "name" = "Test Job"
                        "description" = "Run unit and integration tests"
                        "icon" = "✅"
                        "category" = "jobs"
                        "platforms" = @("azuredevops", "github")
                        "properties" = @{
                            "displayName" = "Run Tests"
                            "pool" = "ubuntu-latest"
                            "dependsOn" = @("BuildJob")
                        }
                    }
                    "deployJob" = @{
                        "name" = "Deploy Job"
                        "description" = "Deploy to target environment"
                        "icon" = "🌐"
                        "category" = "jobs"
                        "platforms" = @("azuredevops", "github")
                        "properties" = @{
                            "displayName" = "Deploy Application"
                            "pool" = "ubuntu-latest"
                            "environment" = "production"
                        }
                    }
                }
                "tasks" = @{
                    "checkout" = @{
                        "name" = "Checkout Code"
                        "description" = "Checkout source code from repository"
                        "icon" = "📥"
                        "category" = "tasks"
                        "platforms" = @("azuredevops", "github")
                        "properties" = @{
                            "clean" = $true
                            "fetchDepth" = 1
                        }
                    }
                    "dotnetBuild" = @{
                        "name" = ".NET Build"
                        "description" = "Build .NET application"
                        "icon" = "🔷"
                        "category" = "tasks"
                        "platforms" = @("azuredevops", "github")
                        "properties" = @{
                            "command" = "build"
                            "projects" = "**/*.csproj"
                            "configuration" = "Release"
                        }
                    }
                    "dotnetTest" = @{
                        "name" = ".NET Test"
                        "description" = "Run .NET tests"
                        "icon" = "🧪"
                        "category" = "tasks"
                        "platforms" = @("azuredevops", "github")
                        "properties" = @{
                            "command" = "test"
                            "projects" = "**/*Test*.csproj"
                            "configuration" = "Release"
                        }
                    }
                    "dockerBuild" = @{
                        "name" = "Docker Build"
                        "description" = "Build Docker image"
                        "icon" = "🐳"
                        "category" = "tasks"
                        "platforms" = @("azuredevops", "github")
                        "properties" = @{
                            "dockerfile" = "Dockerfile"
                            "context" = "."
                            "tags" = @("latest")
                        }
                    }
                    "azureWebApp" = @{
                        "name" = "Azure Web App Deploy"
                        "description" = "Deploy to Azure Web App"
                        "icon" = "☁️"
                        "category" = "tasks"
                        "platforms" = @("azuredevops", "github")
                        "properties" = @{
                            "appName" = ""
                            "resourceGroup" = ""
                            "package" = "$(Pipeline.Workspace)/drop/**/*.zip"
                        }
                    }
                    "sftpDeploy" = @{
                        "name" = "SFTP Deploy"
                        "description" = "Deploy via SFTP"
                        "icon" = "📁"
                        "category" = "tasks"
                        "platforms" = @("azuredevops", "github")
                        "properties" = @{
                            "server" = ""
                            "username" = ""
                            "remotePath" = "/var/www/html"
                            "localPath" = "./artifacts"
                        }
                    }
                }
            }
            "workflowTemplates" = @{
                "simpleCI" = @{
                    "name" = "Simple CI"
                    "description" = "Basic continuous integration pipeline"
                    "components" = @("push", "buildJob", "testJob")
                }
                "fullCICD" = @{
                    "name" = "Full CI/CD"
                    "description" = "Complete CI/CD pipeline with deployment"
                    "components" = @("push", "pullRequest", "build", "test", "deploy")
                }
                "dockerPipeline" = @{
                    "name" = "Docker Pipeline"
                    "description" = "Docker-based CI/CD pipeline"
                    "components" = @("push", "dockerBuild", "deploy")
                }
            }
        }

        $defaultConfig | ConvertTo-Json -Depth 10 | Set-Content -Path $DesignerConfigFile -Encoding UTF8
        Write-DesignerLog "Designer configuration created" "SUCCESS"
    }

    return Get-Content $DesignerConfigFile | ConvertFrom-Json
}

function Show-DesignerMenu {
    Write-Host ""
    Write-ColorText "🎨 Interactive Pipeline Designer Menu" "Cyan"
    Write-Host ""
    Write-ColorText "    [1] 🆕 Create New Pipeline" "Green"
    Write-ColorText "        └─ Start with blank canvas or template"
    Write-Host ""
    Write-ColorText "    [2] 📂 Load Existing Pipeline" "Blue"
    Write-ColorText "        └─ Load and edit existing pipeline file"
    Write-Host ""
    Write-ColorText "    [3] 🎯 Quick Start Wizard" "Yellow"
    Write-ColorText "        └─ Guided pipeline creation with templates"
    Write-Host ""
    Write-ColorText "    [4] 🧩 Component Library" "Magenta"
    Write-ColorText "        └─ Browse available pipeline components"
    Write-Host ""
    Write-ColorText "    [5] 💾 Save Current Pipeline" "Cyan"
    Write-ColorText "        └─ Save pipeline to file"
    Write-Host ""
    Write-ColorText "    [6] 📤 Export Pipeline" "White"
    Write-ColorText "        └─ Export to Azure DevOps or GitHub Actions format"
    Write-Host ""
    Write-ColorText "    [0] 🚪 Exit" "Gray"
    Write-Host ""
}

function Show-ComponentLibrary {
    param([object]$Config)

    Write-Host ""
    Write-ColorText "🧩 Pipeline Component Library" "Cyan"
    Write-Host ""

    $categories = @("triggers", "stages", "jobs", "tasks")

    foreach ($category in $categories) {
        $categoryName = $category.Substring(0,1).ToUpper() + $category.Substring(1)
        Write-ColorText "${categoryName}:" "Yellow"

        $components = $Config.componentLibrary.$category
        foreach ($componentKey in $components.PSObject.Properties.Name) {
            $component = $components.$componentKey
            Write-ColorText "  $($component.icon) $($component.name)" "White"
            Write-ColorText "    $($component.description)" "Gray"
            Write-ColorText "    Platforms: $($component.platforms -join ', ')" "DarkGray"
        }
        Write-Host ""
    }
}

function Start-PipelineDesigner {
    param([object]$Config, [string]$Platform)

    Write-Host ""
    Write-ColorText "🎨 Starting Pipeline Designer for $Platform" "Cyan"
    Write-Host ""

    $pipeline = @{
        "platform" = $Platform
        "name" = ""
        "description" = ""
        "triggers" = @()
        "variables" = @{}
        "stages" = @()
        "jobs" = @()
        "steps" = @()
        "metadata" = @{
            "created" = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
            "version" = "1.0"
            "designer" = "Interactive Pipeline Designer"
        }
    }

    # Get basic pipeline information
    $pipeline.name = Read-Host "Enter pipeline name"
    $pipeline.description = Read-Host "Enter pipeline description (optional)"

    Write-Host ""
    Write-ColorText "Pipeline Designer - Visual Builder" "Cyan"
    Write-ColorText "═══════════════════════════════════" "Gray"

    do {
        Show-PipelineCanvas $pipeline
        Write-Host ""
        Write-ColorText "Designer Actions:" "Yellow"
        Write-ColorText "  [1] Add Trigger" "White"
        Write-ColorText "  [2] Add Stage" "White"
        Write-ColorText "  [3] Add Job" "White"
        Write-ColorText "  [4] Add Task/Step" "White"
        Write-ColorText "  [5] Set Variables" "White"
        Write-ColorText "  [6] Preview Pipeline" "White"
        Write-ColorText "  [7] Validate Pipeline" "White"
        Write-ColorText "  [8] Save Pipeline" "White"
        Write-ColorText "  [0] Finish Design" "Gray"

        $choice = Read-Host "Enter choice (0-8)"

        switch ($choice) {
            "1" { $pipeline = Add-TriggerComponent $pipeline $Config }
            "2" { $pipeline = Add-StageComponent $pipeline $Config }
            "3" { $pipeline = Add-JobComponent $pipeline $Config }
            "4" { $pipeline = Add-TaskComponent $pipeline $Config }
            "5" { $pipeline = Set-PipelineVariables $pipeline }
            "6" { Show-PipelinePreview $pipeline }
            "7" { Test-PipelineDesign $pipeline }
            "8" { Save-PipelineDesign $pipeline }
            "0" { break }
            default { Write-DesignerLog "Invalid choice. Please try again." "WARN" }
        }

        if ($choice -ne "0") {
            Read-Host "Press Enter to continue"
        }
    } while ($choice -ne "0")

    return $pipeline
}

function Show-PipelineCanvas {
    param([object]$Pipeline)

    Write-Host ""
    Write-ColorText "📋 Pipeline Canvas: $($Pipeline.name)" "Cyan"
    Write-ColorText "Platform: $($Pipeline.platform)" "Gray"
    if ($Pipeline.description) {
        Write-ColorText "Description: $($Pipeline.description)" "Gray"
    }
    Write-Host ""

    # Show triggers
    if ($Pipeline.triggers.Count -gt 0) {
        Write-ColorText "🎯 Triggers:" "Yellow"
        foreach ($trigger in $Pipeline.triggers) {
            Write-ColorText "  📤 $($trigger.type) - $($trigger.name)" "White"
        }
        Write-Host ""
    }

    # Show variables
    if ($Pipeline.variables.Count -gt 0) {
        Write-ColorText "📊 Variables:" "Yellow"
        foreach ($varName in $Pipeline.variables.Keys) {
            Write-ColorText "  📊 $varName = $($Pipeline.variables[$varName])" "White"
        }
        Write-Host ""
    }

    # Show stages
    if ($Pipeline.stages.Count -gt 0) {
        Write-ColorText "🏗️ Stages:" "Yellow"
        foreach ($stage in $Pipeline.stages) {
            Write-ColorText "  🔨 $($stage.name)" "White"
            if ($stage.dependsOn.Count -gt 0) {
                Write-ColorText "    Depends on: $($stage.dependsOn -join ', ')" "Gray"
            }
        }
        Write-Host ""
    }

    # Show jobs
    if ($Pipeline.jobs.Count -gt 0) {
        Write-ColorText "⚙️ Jobs:" "Yellow"
        foreach ($job in $Pipeline.jobs) {
            Write-ColorText "  ⚙️ $($job.name)" "White"
            if ($job.dependsOn.Count -gt 0) {
                Write-ColorText "    Depends on: $($job.dependsOn -join ', ')" "Gray"
            }
        }
        Write-Host ""
    }

    # Show steps/tasks
    if ($Pipeline.steps.Count -gt 0) {
        Write-ColorText "📝 Steps/Tasks:" "Yellow"
        foreach ($step in $Pipeline.steps) {
            Write-ColorText "  📝 $($step.name)" "White"
            Write-ColorText "    Type: $($step.type)" "Gray"
        }
        Write-Host ""
    }

    if ($Pipeline.triggers.Count -eq 0 -and $Pipeline.stages.Count -eq 0 -and $Pipeline.jobs.Count -eq 0 -and $Pipeline.steps.Count -eq 0) {
        Write-ColorText "  (Empty pipeline - add components to get started)" "Gray"
    }
}

function Add-TriggerComponent {
    param([object]$Pipeline, [object]$Config)

    Write-Host ""
    Write-ColorText "🎯 Add Trigger Component" "Cyan"
    Write-Host ""

    $triggers = $Config.componentLibrary.triggers
    $triggerKeys = @($triggers.PSObject.Properties.Name)

    Write-ColorText "Available triggers:" "Yellow"
    for ($i = 0; $i -lt $triggerKeys.Count; $i++) {
        $trigger = $triggers.($triggerKeys[$i])
        Write-ColorText "  [$($i + 1)] $($trigger.icon) $($trigger.name)" "White"
        Write-ColorText "      $($trigger.description)" "Gray"
    }

    $choice = Read-Host "Select trigger (1-$($triggerKeys.Count))"
    if ([int]$choice -ge 1 -and [int]$choice -le $triggerKeys.Count) {
        $selectedKey = $triggerKeys[[int]$choice - 1]
        $selectedTrigger = $triggers.$selectedKey

        $newTrigger = @{
            "type" = $selectedKey
            "name" = $selectedTrigger.name
            "properties" = @{}
        }

        # Configure trigger properties
        Write-Host ""
        Write-ColorText "Configure $($selectedTrigger.name):" "Yellow"

        foreach ($propName in $selectedTrigger.properties.PSObject.Properties.Name) {
            $defaultValue = $selectedTrigger.properties.$propName
            if ($defaultValue -is [array]) {
                $defaultValue = $defaultValue -join ", "
            }

            $userValue = Read-Host "  $propName (default: $defaultValue)"
            if ($userValue) {
                if ($userValue -contains ",") {
                    $newTrigger.properties[$propName] = $userValue -split "," | ForEach-Object { $_.Trim() }
                } else {
                    $newTrigger.properties[$propName] = $userValue
                }
            } else {
                $newTrigger.properties[$propName] = $selectedTrigger.properties.$propName
            }
        }

        $Pipeline.triggers += $newTrigger
        Write-DesignerLog "Added trigger: $($selectedTrigger.name)" "SUCCESS"
    } else {
        Write-DesignerLog "Invalid selection" "ERROR"
    }

    return $Pipeline
}

function Add-StageComponent {
    param([object]$Pipeline, [object]$Config)

    Write-Host ""
    Write-ColorText "🏗️ Add Stage Component" "Cyan"
    Write-Host ""

    $stages = $Config.componentLibrary.stages
    $stageKeys = @($stages.PSObject.Properties.Name)

    Write-ColorText "Available stages:" "Yellow"
    for ($i = 0; $i -lt $stageKeys.Count; $i++) {
        $stage = $stages.($stageKeys[$i])
        Write-ColorText "  [$($i + 1)] $($stage.icon) $($stage.name)" "White"
        Write-ColorText "      $($stage.description)" "Gray"
    }

    $choice = Read-Host "Select stage (1-$($stageKeys.Count))"
    if ([int]$choice -ge 1 -and [int]$choice -le $stageKeys.Count) {
        $selectedKey = $stageKeys[[int]$choice - 1]
        $selectedStage = $stages.$selectedKey

        $newStage = @{
            "type" = $selectedKey
            "name" = $selectedStage.name
            "displayName" = $selectedStage.properties.displayName
            "dependsOn" = @()
            "condition" = $selectedStage.properties.condition
            "jobs" = @()
        }

        # Configure stage properties
        Write-Host ""
        Write-ColorText "Configure $($selectedStage.name):" "Yellow"

        $stageName = Read-Host "  Stage name (default: $($selectedStage.properties.displayName))"
        if ($stageName) {
            $newStage.name = $stageName
            $newStage.displayName = $stageName
        }

        # Configure dependencies
        if ($Pipeline.stages.Count -gt 0) {
            Write-ColorText "  Available stages for dependencies:" "Gray"
            for ($i = 0; $i -lt $Pipeline.stages.Count; $i++) {
                Write-ColorText "    [$($i + 1)] $($Pipeline.stages[$i].name)" "White"
            }

            $depInput = Read-Host "  Dependencies (comma-separated numbers, or press Enter for none)"
            if ($depInput) {
                $depNumbers = $depInput -split "," | ForEach-Object { [int]$_.Trim() }
                foreach ($depNum in $depNumbers) {
                    if ($depNum -ge 1 -and $depNum -le $Pipeline.stages.Count) {
                        $newStage.dependsOn += $Pipeline.stages[$depNum - 1].name
                    }
                }
            }
        }

        $Pipeline.stages += $newStage
        Write-DesignerLog "Added stage: $($newStage.name)" "SUCCESS"
    } else {
        Write-DesignerLog "Invalid selection" "ERROR"
    }

    return $Pipeline
}

function Add-JobComponent {
    param([object]$Pipeline, [object]$Config)

    Write-Host ""
    Write-ColorText "⚙️ Add Job Component" "Cyan"
    Write-Host ""

    $jobs = $Config.componentLibrary.jobs
    $jobKeys = @($jobs.PSObject.Properties.Name)

    Write-ColorText "Available jobs:" "Yellow"
    for ($i = 0; $i -lt $jobKeys.Count; $i++) {
        $job = $jobs.($jobKeys[$i])
        Write-ColorText "  [$($i + 1)] $($job.icon) $($job.name)" "White"
        Write-ColorText "      $($job.description)" "Gray"
    }

    $choice = Read-Host "Select job (1-$($jobKeys.Count))"
    if ([int]$choice -ge 1 -and [int]$choice -le $jobKeys.Count) {
        $selectedKey = $jobKeys[[int]$choice - 1]
        $selectedJob = $jobs.$selectedKey

        $newJob = @{
            "type" = $selectedKey
            "name" = $selectedJob.name
            "displayName" = $selectedJob.properties.displayName
            "pool" = $selectedJob.properties.pool
            "dependsOn" = @()
            "steps" = @()
        }

        # Configure job properties
        Write-Host ""
        Write-ColorText "Configure $($selectedJob.name):" "Yellow"

        $jobName = Read-Host "  Job name (default: $($selectedJob.properties.displayName))"
        if ($jobName) {
            $newJob.name = $jobName
            $newJob.displayName = $jobName
        }

        $pool = Read-Host "  Agent pool (default: $($selectedJob.properties.pool))"
        if ($pool) {
            $newJob.pool = $pool
        }

        # Configure dependencies
        if ($Pipeline.jobs.Count -gt 0) {
            Write-ColorText "  Available jobs for dependencies:" "Gray"
            for ($i = 0; $i -lt $Pipeline.jobs.Count; $i++) {
                Write-ColorText "    [$($i + 1)] $($Pipeline.jobs[$i].name)" "White"
            }

            $depInput = Read-Host "  Dependencies (comma-separated numbers, or press Enter for none)"
            if ($depInput) {
                $depNumbers = $depInput -split "," | ForEach-Object { [int]$_.Trim() }
                foreach ($depNum in $depNumbers) {
                    if ($depNum -ge 1 -and $depNum -le $Pipeline.jobs.Count) {
                        $newJob.dependsOn += $Pipeline.jobs[$depNum - 1].name
                    }
                }
            }
        }

        $Pipeline.jobs += $newJob
        Write-DesignerLog "Added job: $($newJob.name)" "SUCCESS"
    } else {
        Write-DesignerLog "Invalid selection" "ERROR"
    }

    return $Pipeline
}
}