# 🚀 Notify Service API - Deployment & Running Guide

This comprehensive guide covers everything you need to deploy and run the Notify Service API in different environments.

## 📋 Table of Contents

- [Prerequisites](#prerequisites)
- [Local Development Setup](#local-development-setup)
- [Database Setup](#database-setup)
- [Configuration](#configuration)
- [Running the Application](#running-the-application)
- [Docker Deployment](#docker-deployment)
- [Production Deployment](#production-deployment)
- [Monitoring & Health Checks](#monitoring--health-checks)
- [Troubleshooting](#troubleshooting)

## 🔧 Prerequisites

### Required Software

| Component | Version | Download Link |
|-----------|---------|---------------|
| .NET SDK | 8.0+ | [Download .NET 8](https://dotnet.microsoft.com/download/dotnet/8.0) |
| PostgreSQL | 15+ | [Download PostgreSQL](https://www.postgresql.org/download/) |
| Redis | 6+ | [Download Redis](https://redis.io/download) |
| Git | Latest | [Download Git](https://git-scm.com/downloads) |

### Optional Tools

- **Docker Desktop** - For containerized deployment
- **Visual Studio 2022** or **VS Code** - For development
- **pgAdmin** - PostgreSQL management tool
- **Redis Commander** - Redis management tool

## 🏠 Local Development Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd notify-service-api
```

### 2. Verify .NET Installation

```bash
dotnet --version
# Should output: 8.0.x or higher
```

### 3. Restore Dependencies

```bash
dotnet restore
```

### 4. Build the Solution

```bash
dotnet build
```

## 🗄️ Database Setup

### PostgreSQL Setup

#### Option A: Local PostgreSQL Installation

1. **Install PostgreSQL 15+**
   - Download and install from [postgresql.org](https://www.postgresql.org/download/)
   - Remember the superuser password during installation

2. **Create Databases**
   ```sql
   -- Connect to PostgreSQL as superuser
   psql -U postgres
   
   -- Create application database
   CREATE DATABASE "NotifyDb";
   
   -- Create identity database
   CREATE DATABASE "NotifyIdentityDb";
   
   -- Create application user (optional)
   CREATE USER notify_user WITH PASSWORD 'your_secure_password';
   GRANT ALL PRIVILEGES ON DATABASE "NotifyDb" TO notify_user;
   GRANT ALL PRIVILEGES ON DATABASE "NotifyIdentityDb" TO notify_user;
   
   -- Exit psql
   \q
   ```

#### Option B: Docker PostgreSQL

```bash
# Run PostgreSQL in Docker
docker run --name notify-postgres \
  -e POSTGRES_DB=NotifyDb \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=yourpassword \
  -p 5432:5432 \
  -d postgres:15

# Create second database for Identity
docker exec -it notify-postgres psql -U postgres -c "CREATE DATABASE \"NotifyIdentityDb\";"
```

### Redis Setup

#### Option A: Local Redis Installation

**Windows (using Chocolatey):**
```bash
choco install redis-64
redis-server
```

**macOS (using Homebrew):**
```bash
brew install redis
brew services start redis
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
```

#### Option B: Docker Redis

```bash
docker run --name notify-redis \
  -p 6379:6379 \
  -d redis:7-alpine
```

## ⚙️ Configuration

### 1. Create Development Configuration

Create `appsettings.Development.json` in the `Presentations/WebApi` directory:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=NotifyDb;Username=postgres;Password=yourpassword;Include Error Detail=true;",
    "IdentityConnection": "Host=localhost;Port=5432;Database=NotifyIdentityDb;Username=postgres;Password=yourpassword;Include Error Detail=true;"
  },
  "JWTSettings": {
    "Key": "your-super-secret-jwt-key-here-minimum-32-characters-long",
    "Issuer": "NotifyServiceAPI",
    "Audience": "NotifyServiceUsers",
    "DurationInMinutes": 60
  },
  "MailSettings": {
    "EmailFrom": "<EMAIL>",
    "SmtpHost": "smtp.gmail.com",
    "SmtpPort": 587,
    "SmtpUser": "<EMAIL>",
    "SmtpPass": "your-app-password",
    "DisplayName": "Notify Service"
  },
  "RedisSettings": {
    "RedisConnectionString": "localhost:6379",
    "CacheTime": 30,
    "RedisDatabaseId": 0
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Information"
    }
  },
  "AllowedHosts": "*"
}
```

### 2. Environment Variables (Optional)

For sensitive data, use environment variables:

```bash
# Windows (PowerShell)
$env:ConnectionStrings__DefaultConnection="Host=localhost;Database=NotifyDb;Username=postgres;Password=yourpassword"
$env:JWTSettings__Key="your-super-secret-jwt-key-here"

# Linux/macOS (Bash)
export ConnectionStrings__DefaultConnection="Host=localhost;Database=NotifyDb;Username=postgres;Password=yourpassword"
export JWTSettings__Key="your-super-secret-jwt-key-here"
```

### 3. Email Configuration

#### Gmail Setup
1. Enable 2-Factor Authentication
2. Generate an App Password
3. Use the App Password in `SmtpPass`

#### SendGrid Setup
```json
{
  "MailSettings": {
    "SmtpHost": "smtp.sendgrid.net",
    "SmtpPort": 587,
    "SmtpUser": "apikey",
    "SmtpPass": "your-sendgrid-api-key"
  }
}
```

## 🎛️ Enhanced Development Tools

### **Cross-Platform Development Tools (Recommended)**

We've created beautiful, interactive development tools that work across all platforms:

#### **🚀 Universal Launcher**
```powershell
# Cross-platform launcher (auto-detects your OS)
.\scripts\dev-setup.ps1

# Specific actions
.\scripts\dev-setup.ps1 -Menu     # Interactive menu
.\scripts\dev-setup.ps1 -Start    # Quick start environment
.\scripts\dev-setup.ps1 -Config   # Configuration manager
.\scripts\dev-setup.ps1 -Stop     # Stop services
```

#### **🎛️ Platform-Specific Menus**
```powershell
# Windows (PowerShell)
.\scripts\dev-menu.ps1

# Linux/macOS (Bash)
./scripts/unix/dev-menu.sh
```

**Features:**
- 🚀 **One-click environment setup** with Docker containers
- ⚙️ **Configuration manager** for environment variables and secrets
- 🔧 **Automatic migrations** creation and application
- 🛑 **Service management** (start, stop, restart, cleanup)
- 🌐 **Quick access** to documentation and health checks
- 📊 **Real-time status** monitoring
- 🖥️ **Cross-platform support** (Windows, Linux, macOS)
- 🔍 **Dependency checking** with auto-installation options
- 📦 **Package manager integration** (winget, brew, apt, yum)

### **Configuration Manager**

Manage all your environment variables, secrets, and configuration across platforms:

#### **Windows (PowerShell)**
```powershell
# Interactive configuration setup
.\scripts\config-manager.ps1

# Quick development setup
.\scripts\config-manager.ps1 -Local

# Docker-specific configuration
.\scripts\config-manager.ps1 -Docker
```

#### **Linux/macOS (Bash)**
```bash
# Interactive configuration setup
./scripts/unix/config-manager.sh

# Quick development setup (basic version)
./scripts/unix/config-manager.sh
```

**Configuration Options:**
- 🗄️ **Database settings** (PostgreSQL connection strings)
- 🔴 **Redis configuration** (host, port, password)
- 📧 **Email/SMTP settings** (Gmail, SendGrid, custom)
- 🔐 **JWT & security** (secret keys, token settings)
- 🐳 **Docker configuration** (registry, ports, images)
- ☁️ **Azure deployment** (subscription, resource groups)
- 📊 **Monitoring** (Slack webhooks, Application Insights)

### **Automated Development Setup**

Start your complete development environment with one command across all platforms:

#### **Windows (PowerShell)**
```powershell
# Full setup with migrations
.\scripts\start-dev-services.ps1

# Stop services
.\scripts\stop-dev-services.ps1

# Complete cleanup
.\scripts\stop-dev-services.ps1 -Remove -RemoveVolumes
```

#### **Linux/macOS (Bash)**
```bash
# Full setup with migrations
./scripts/unix/start-dev-services.sh

# Stop services
./scripts/unix/stop-dev-services.sh

# Complete cleanup
./scripts/unix/stop-dev-services.sh --remove --remove-volumes
```

**What it does:**
- ✅ **Validates** Docker installation
- 🐳 **Starts** PostgreSQL and Redis containers
- 🗄️ **Creates** databases automatically
- 🔧 **Applies** Entity Framework migrations
- 🧪 **Tests** all connections
- 📋 **Provides** comprehensive status reporting

## 🏃‍♂️ Running the Application

### 1. Apply Database Migrations

```bash
# Navigate to the WebApi project
cd Presentations/WebApi

# Apply migrations
dotnet ef database update --project ../../Libraries/Data
dotnet ef database update --project ../../Libraries/Identity
```

### 2. Run the Application

#### Development Mode
```bash
# From solution root
dotnet run --project Presentations/WebApi

# Or from WebApi directory
cd Presentations/WebApi
dotnet run
```

#### Watch Mode (Auto-reload)
```bash
dotnet watch run --project Presentations/WebApi
```

### 3. Verify Installation

Once running, verify these endpoints:

- **API Documentation**: `https://localhost:5001/scalar`
- **Health Check**: `https://localhost:5001/health`
- **OpenAPI Spec**: `https://localhost:5001/openapi/v1.json`

### 4. Test the API

#### Register a User
```bash
curl -X POST "https://localhost:5001/api/account/register" \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "userName": "johndoe",
    "password": "SecurePassword123!",
    "confirmPassword": "SecurePassword123!"
  }'
```

#### Authenticate
```bash
curl -X POST "https://localhost:5001/api/account/authenticate" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePassword123!"
  }'
```

## 🐳 Docker Deployment

### 1. Create Dockerfile

Create `Dockerfile` in the solution root:

```dockerfile
# Build stage
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy solution file
COPY Notify.sln ./

# Copy project files
COPY Libraries/Models/Models.csproj Libraries/Models/
COPY Libraries/Data/Data.csproj Libraries/Data/
COPY Libraries/Services/Services.csproj Libraries/Services/
COPY Libraries/Core/Core.csproj Libraries/Core/
COPY Libraries/Identity/Identity.csproj Libraries/Identity/
COPY Libraries/Caching/Caching.csproj Libraries/Caching/
COPY Presentations/WebApi/WebApi.csproj Presentations/WebApi/

# Restore dependencies
RUN dotnet restore

# Copy source code
COPY . .

# Build application
RUN dotnet build -c Release --no-restore

# Publish application
RUN dotnet publish Presentations/WebApi/WebApi.csproj -c Release -o /app/publish --no-restore

# Runtime stage
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS runtime
WORKDIR /app

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Copy published application
COPY --from=build /app/publish .

# Expose ports
EXPOSE 80
EXPOSE 443

# Set environment
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:80/health || exit 1

# Start application
ENTRYPOINT ["dotnet", "WebApi.dll"]
```

### 2. Create Docker Compose

Create `docker-compose.yml` in the solution root:

```yaml
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: notify-postgres
    environment:
      POSTGRES_DB: NotifyDb
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-defaultpassword}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - notify-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: notify-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - notify-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Notify Service API
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: notify-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=NotifyDb;Username=postgres;Password=${POSTGRES_PASSWORD:-defaultpassword}
      - ConnectionStrings__IdentityConnection=Host=postgres;Database=NotifyIdentityDb;Username=postgres;Password=${POSTGRES_PASSWORD:-defaultpassword}
      - RedisSettings__RedisConnectionString=redis:6379
      - JWTSettings__Key=${JWT_SECRET_KEY}
    ports:
      - "8080:80"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - notify-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  notify-network:
    driver: bridge
```

### 3. Create Database Initialization Script

Create `scripts/init-db.sql`:

```sql
-- Create Identity database
CREATE DATABASE "NotifyIdentityDb";
```

### 4. Create Environment File

Create `.env` file:

```env
POSTGRES_PASSWORD=your_secure_postgres_password
JWT_SECRET_KEY=your-super-secret-jwt-key-here-minimum-32-characters-long
MAIL_SMTP_PASSWORD=your_email_app_password
```

### 5. Run with Docker Compose

```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f api

# Stop services
docker-compose down

# Stop and remove volumes
docker-compose down -v
```

## 🌐 Production Deployment

### Azure App Service

#### 1. Prepare for Azure

```bash
# Install Azure CLI
# Windows: winget install Microsoft.AzureCLI
# macOS: brew install azure-cli
# Linux: curl -sL https://aka.ms/InstallAzureCLI | sudo bash

# Login to Azure
az login

# Create resource group
az group create --name notify-service-rg --location eastus

# Create App Service plan
az appservice plan create \
  --name notify-service-plan \
  --resource-group notify-service-rg \
  --sku B1 \
  --is-linux

# Create web app
az webapp create \
  --name notify-service-api \
  --resource-group notify-service-rg \
  --plan notify-service-plan \
  --runtime "DOTNETCORE:8.0"
```

#### 2. Configure Database

```bash
# Create PostgreSQL server
az postgres flexible-server create \
  --name notify-postgres-server \
  --resource-group notify-service-rg \
  --location eastus \
  --admin-user notifyadmin \
  --admin-password YourSecurePassword123! \
  --sku-name Standard_B1ms \
  --tier Burstable \
  --storage-size 32

# Create databases
az postgres flexible-server db create \
  --resource-group notify-service-rg \
  --server-name notify-postgres-server \
  --database-name NotifyDb

az postgres flexible-server db create \
  --resource-group notify-service-rg \
  --server-name notify-postgres-server \
  --database-name NotifyIdentityDb

# Create Redis cache
az redis create \
  --name notify-redis-cache \
  --resource-group notify-service-rg \
  --location eastus \
  --sku Basic \
  --vm-size c0
```

#### 3. Configure App Settings

```bash
# Set connection strings
az webapp config connection-string set \
  --name notify-service-api \
  --resource-group notify-service-rg \
  --connection-string-type PostgreSQL \
  --settings DefaultConnection="Host=notify-postgres-server.postgres.database.azure.com;Database=NotifyDb;Username=notifyadmin;Password=YourSecurePassword123!;SSL Mode=Require;"

# Set app settings
az webapp config appsettings set \
  --name notify-service-api \
  --resource-group notify-service-rg \
  --settings \
    JWTSettings__Key="your-super-secret-jwt-key-here" \
    RedisSettings__RedisConnectionString="notify-redis-cache.redis.cache.windows.net:6380,password=your-redis-key,ssl=True"
```

#### 4. Deploy Application

```bash
# Create deployment package
dotnet publish Presentations/WebApi/WebApi.csproj -c Release -o ./publish

# Create zip file
cd publish
zip -r ../deploy.zip .
cd ..

# Deploy to Azure
az webapp deployment source config-zip \
  --name notify-service-api \
  --resource-group notify-service-rg \
  --src deploy.zip
```

### AWS Elastic Beanstalk

#### 1. Install EB CLI

```bash
# Install EB CLI
pip install awsebcli

# Initialize EB application
eb init notify-service-api --platform "64bit Amazon Linux 2 v2.2.0 running .NET Core" --region us-east-1
```

#### 2. Create Environment

```bash
# Create environment
eb create production --database.engine postgres --database.username notifyuser

# Set environment variables
eb setenv \
  ASPNETCORE_ENVIRONMENT=Production \
  JWTSettings__Key="your-super-secret-jwt-key" \
  ConnectionStrings__DefaultConnection="your-postgres-connection-string"
```

#### 3. Deploy

```bash
# Deploy application
eb deploy

# Open in browser
eb open
```

## 📊 Monitoring & Health Checks

### Built-in Health Checks

The application includes comprehensive health checks:

- **Database Connectivity**: PostgreSQL connection test
- **Redis Connectivity**: Redis ping test
- **Application Health**: General application status

### Endpoints

- **Health Check**: `GET /health`
- **Detailed Health**: `GET /health/detailed`

### Custom Monitoring

#### Application Insights (Azure)

```bash
# Add Application Insights
az monitor app-insights component create \
  --app notify-service-insights \
  --location eastus \
  --resource-group notify-service-rg

# Get instrumentation key
az monitor app-insights component show \
  --app notify-service-insights \
  --resource-group notify-service-rg \
  --query instrumentationKey
```

Add to `appsettings.json`:

```json
{
  "ApplicationInsights": {
    "InstrumentationKey": "your-instrumentation-key"
  }
}
```

#### Prometheus & Grafana

Create `docker-compose.monitoring.yml`:

```yaml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana

volumes:
  grafana_data:
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Database Connection Issues

**Problem**: `Npgsql.NpgsqlException: Connection refused`

**Solutions**:
```bash
# Check PostgreSQL is running
sudo systemctl status postgresql  # Linux
brew services list | grep postgres  # macOS

# Check connection string format
# Correct: Host=localhost;Database=NotifyDb;Username=postgres;Password=yourpassword
# Incorrect: Server=localhost;Database=NotifyDb;...

# Test connection manually
psql -h localhost -U postgres -d NotifyDb
```

#### 2. Redis Connection Issues

**Problem**: `StackExchange.Redis.RedisConnectionException`

**Solutions**:
```bash
# Check Redis is running
redis-cli ping  # Should return PONG

# Check Redis configuration
redis-cli config get bind
redis-cli config get protected-mode

# Test connection
redis-cli -h localhost -p 6379
```

#### 3. JWT Token Issues

**Problem**: `401 Unauthorized` responses

**Solutions**:
- Ensure JWT key is at least 32 characters
- Check token expiration time
- Verify issuer and audience settings
- Check token format in Authorization header: `Bearer <token>`

#### 4. Email Sending Issues

**Problem**: SMTP authentication failures

**Solutions**:
```json
// Gmail: Use App Password, not regular password
{
  "MailSettings": {
    "SmtpHost": "smtp.gmail.com",
    "SmtpPort": 587,
    "SmtpUser": "<EMAIL>",
    "SmtpPass": "your-16-character-app-password"
  }
}
```

#### 5. Migration Issues

**Problem**: `Unable to create an object of type 'ApplicationDbContext'`

**Solutions**:
```bash
# Specify startup project explicitly
dotnet ef database update --project Libraries/Data --startup-project Presentations/WebApi

# Check connection string in appsettings.json
# Ensure DefaultConnection is properly configured
```

### Logging and Debugging

#### Enable Detailed Logging

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft.EntityFrameworkCore": "Information",
      "Microsoft.AspNetCore": "Information"
    }
  }
}
```

#### View Application Logs

```bash
# Docker logs
docker logs notify-api -f

# Azure App Service logs
az webapp log tail --name notify-service-api --resource-group notify-service-rg

# Local development
# Logs are written to console and configured Serilog sinks
```

### Performance Optimization

#### Database Optimization

```sql
-- Add indexes for frequently queried columns
CREATE INDEX idx_loginlog_useremail ON "LoginLogs" ("UserEmail");
CREATE INDEX idx_loginlog_logintime ON "LoginLogs" ("LoginTime");

-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM "LoginLogs" WHERE "UserEmail" = '<EMAIL>';
```

#### Redis Optimization

```bash
# Monitor Redis performance
redis-cli --latency-history -i 1

# Check memory usage
redis-cli info memory

# Optimize Redis configuration
redis-cli config set maxmemory-policy allkeys-lru
```

## 🎯 Next Steps

After successful deployment:

1. **Set up CI/CD Pipeline** - Automate deployments
2. **Configure Monitoring** - Set up alerts and dashboards
3. **Security Hardening** - Implement additional security measures
4. **Performance Testing** - Load test your API
5. **Backup Strategy** - Set up database backups
6. **Documentation** - Update API documentation

## 📞 Support

For additional help:

- Check the [Architecture Documentation](ARCHITECTURE.md)
- Review the [Changelog](../CHANGELOG.md)
- Open an issue in the repository
- Contact the development team

---

**Happy Deploying! 🚀**
```
