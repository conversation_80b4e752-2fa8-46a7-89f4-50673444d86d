# 🏗️ Universal Pipeline Builder

A comprehensive CI/CD pipeline generator that supports Azure DevOps, GitHub Actions, and now includes advanced FTP/SFTP deployment capabilities with SSH key management.

## 🚀 Features

### ✨ **Core Pipeline Generation**
- **Azure DevOps Pipelines**: Complete YAML pipelines with stages, jobs, and deployment
- **GitHub Actions**: Full workflows with jobs, matrix builds, and environments
- **Smart Repository Analysis**: Automatic project type and framework detection
- **Cross-Platform Support**: Windows (PowerShell) and Unix/Linux/macOS (Bash)
- **Deployment Target Detection**: Automatically detect and configure deployment targets
- **Conditional Logic Builder**: Visual interface for complex pipeline conditions
- **Template System**: Reusable pipeline templates for different scenarios
- **Pipeline Validation**: Comprehensive syntax and logic validation

### 🌐 **FTP/SFTP Deployment Support**
- **FTP/FTPS**: Traditional file transfer protocol support
- **SFTP**: Secure file transfer over SSH
- **SCP**: Secure copy protocol for file transfers
- **Rsync**: Efficient file synchronization with compression
- **SSH Key Management**: Generate, manage, and configure SSH keys
- **Connection Testing**: Verify connectivity before deployment
- **Automated Backups**: Pre-deployment backup creation
- **Security Auditing**: Track SSH key usage and access

### 🎯 **Advanced Features**
- **Canary Deployments**: Progressive traffic routing with automated rollback
- **Blue-Green Deployments**: Zero-downtime deployments
- **Chaos Engineering**: Resilience testing with controlled failures
- **Observability Stack**: Prometheus, Grafana, Jaeger integration
- **SLI/SLO Monitoring**: Service level indicators and objectives
- **Acceptance Testing**: Code coverage, benchmark testing, quality gates
- **Security Scanning**: SAST, DAST, dependency scanning, compliance checks

## 📁 Directory Structure

```
pipeline-builder/
├── pipeline-builder.ps1          # Main entry point
├── core/                          # Core modules
│   └── ftp-sftp-deployment.ps1   # FTP/SFTP deployment module
├── config/                        # Configuration files
│   ├── pipeline-builder-config.json
│   └── ftp-sftp-config.json
├── templates/                     # Pipeline templates
│   ├── azure-devops/
│   ├── github-actions/
│   └── ftp-deployment/
├── ssh-keys/                      # SSH key storage
├── logs/                          # Deployment logs
└── generated-pipelines/           # Generated pipeline files
```

## 🚀 Quick Start

### Windows (PowerShell)

```powershell
# Navigate to pipeline builder
cd pipeline-builder

# Interactive mode with all features
.\pipeline-builder.ps1 -Interactive

# Generate pipelines for both platforms
.\pipeline-builder.ps1 -Action build -Platform both

# FTP/SFTP deployment management
.\pipeline-builder.ps1 -Action sftp
```

### Unix/Linux/macOS (Bash)

```bash
# Make script executable
chmod +x pipeline-builder.ps1

# Interactive mode
./pipeline-builder.ps1 -Interactive

# Generate GitHub Actions workflow
./pipeline-builder.ps1 -Action build -Platform github

# FTP/SFTP deployment
./pipeline-builder.ps1 -Action ftp
```

## 🌐 FTP/SFTP Deployment

### SSH Key Management

Generate and manage SSH keys for secure deployments:

```powershell
# Launch FTP/SFTP module
.\pipeline-builder.ps1 -Action sftp

# Select option 1: SSH Key Management
# Generate new SSH key pair
# Key types: RSA, Ed25519, ECDSA
```

### Supported Protocols

#### FTP/FTPS
- **Port**: 21 (FTP), 990 (FTPS)
- **Authentication**: Username/password, anonymous
- **Features**: Directory listing, file upload/download
- **Security**: Optional SSL/TLS encryption

#### SFTP
- **Port**: 22 (SSH)
- **Authentication**: Password, public key, keyboard-interactive
- **Features**: File operations, permissions, symbolic links
- **Security**: SSH encryption by default

#### SCP
- **Port**: 22 (SSH)
- **Authentication**: Password, public key
- **Features**: Secure file copying, recursive transfers
- **Security**: SSH encryption

#### Rsync
- **Port**: 873 (rsync), 22 (SSH)
- **Authentication**: Password, public key, anonymous
- **Features**: Incremental sync, compression, checksum verification
- **Security**: SSH tunnel support

### Configuration Example

```json
{
  "environments": {
    "prod": {
      "sftp": {
        "host": "prod-sftp.company.com",
        "port": 22,
        "username": "prod_user",
        "keyFile": "prod_rsa",
        "remotePath": "/var/www/production",
        "strictHostKeyChecking": true,
        "backup": {
          "enabled": true,
          "path": "/var/backups/webapp",
          "retention": 7
        }
      }
    }
  }
}
```

## 🏗️ Generated Pipeline Examples

### Azure DevOps with SFTP Deployment

```yaml
- stage: DeployProd
  displayName: 'Deploy to PROD'
  dependsOn: Build
  jobs:
  - deployment: DeployProd
    environment: 'prod'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: SSH@0
            displayName: 'Deploy via SFTP'
            inputs:
              sshEndpoint: 'SFTP-prod'
              runOptions: 'commands'
              commands: |
                # Create backup
                if [ -d "/var/www/prod" ]; then
                  sudo cp -r /var/www/prod /var/backups/webapp-$(date +%Y%m%d-%H%M%S)
                fi
                
                # Create directory if not exists
                sudo mkdir -p /var/www/prod
                
                # Set permissions
                sudo chown -R www-data:www-data /var/www/prod

          - task: CopyFilesOverSSH@0
            displayName: 'Copy files via SFTP'
            inputs:
              sshEndpoint: 'SFTP-prod'
              sourceFolder: '$(Pipeline.Workspace)/drop'
              contents: '**'
              targetFolder: '/var/www/prod'
              overwrite: true
```

### GitHub Actions with SFTP Deployment

```yaml
  deploy-prod:
    name: Deploy to Prod
    runs-on: ubuntu-latest
    needs: build
    environment: prod
    
    steps:
    - name: Setup SSH Key
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY_PROD }}

    - name: Deploy via SFTP
      uses: wlixcc/SFTP-Deploy-Action@v1.2.4
      with:
        server: ${{ secrets.SSH_HOST_PROD }}
        username: ${{ secrets.SSH_USER_PROD }}
        ssh_private_key: ${{ secrets.SSH_PRIVATE_KEY_PROD }}
        local_path: './artifacts/*'
        remote_path: '/var/www/prod'
        sftpArgs: '-o ConnectTimeout=5'

    - name: Set permissions via SSH
      run: |
        ssh -o StrictHostKeyChecking=no ${{ secrets.SSH_USER_PROD }}@${{ secrets.SSH_HOST_PROD }} \
          "sudo chown -R www-data:www-data /var/www/prod && sudo chmod -R 755 /var/www/prod"
```

## 🔐 Security Features

### SSH Key Security
- **Key Encryption**: Private keys can be encrypted with passphrases
- **Key Rotation**: Configurable key rotation schedules
- **Access Auditing**: Track SSH key usage and access
- **Backup**: Automatic key backup and recovery

### Deployment Security
- **Backup Creation**: Automatic backups before deployment
- **Permission Management**: Proper file and directory permissions
- **Connection Verification**: Test connections before deployment
- **Rollback Support**: Quick rollback to previous versions

## 📊 Monitoring & Logging

### Deployment Logs
- **Centralized Logging**: All deployment activities logged
- **Structured Format**: JSON-formatted logs for analysis
- **Retention Policies**: Configurable log retention
- **Error Tracking**: Detailed error reporting and debugging

### Health Checks
- **Post-Deployment Validation**: Automatic health checks
- **Custom Endpoints**: Configurable health check URLs
- **Retry Logic**: Automatic retry on health check failures
- **Alerting**: Integration with notification systems

## 🛠️ Command Line Reference

### Main Pipeline Builder

```powershell
.\pipeline-builder.ps1 [OPTIONS]

-Action <string>        # menu, analyze, build, validate, export, ftp, sftp
-Platform <string>      # azuredevops, github, both
-ProjectType <string>   # auto, dotnet, node, python, docker
-OutputPath <string>    # Output directory for generated files
-Interactive           # Interactive mode
-Force                 # Overwrite existing files
-Validate              # Validate generated pipelines
-Json                  # JSON output
```

### FTP/SFTP Module

```powershell
.\core\ftp-sftp-deployment.ps1 [OPTIONS]

-Action <string>        # menu, deploy, test, configure
-Protocol <string>      # ftp, sftp, scp, rsync
-Environment <string>   # dev, staging, prod
-TestConnection        # Test connection only
-DryRun                # Dry run mode
```

## 🔧 Configuration Management

### Pipeline Configuration
- **Global Settings**: Default pipeline configurations
- **Environment-Specific**: Per-environment customizations
- **Template Management**: Reusable pipeline templates
- **Version Control**: Configuration versioning and history

### FTP/SFTP Configuration
- **Connection Settings**: Server details and authentication
- **Deployment Options**: Transfer settings and exclusions
- **Security Policies**: SSH key management and rotation
- **Backup Strategies**: Automated backup and retention

## 🆘 Troubleshooting

### Common Issues

1. **SSH Connection Failures**
   - Verify SSH key permissions (600 for private key)
   - Check host key verification settings
   - Validate network connectivity and firewall rules

2. **FTP Transfer Errors**
   - Confirm passive/active mode settings
   - Check file permissions and ownership
   - Verify server directory structure

3. **Permission Denied**
   - Ensure proper user permissions on target server
   - Check sudo access for permission changes
   - Validate file ownership and group settings

### Debug Mode

Enable verbose logging for troubleshooting:

```powershell
# Verbose pipeline generation
.\pipeline-builder.ps1 -Verbose -Action build

# Debug FTP/SFTP deployment
.\core\ftp-sftp-deployment.ps1 -Action test -Protocol sftp -Environment dev
```

## 📚 Additional Resources

- [FTP/SFTP Deployment Guide](docs/ftp-sftp-deployment-guide.md)
- [SSH Key Management Best Practices](docs/ssh-key-management.md)
- [Advanced Pipeline Features](docs/advanced-pipeline-features.md)
- [Security Configuration](docs/security-configuration.md)
- [Troubleshooting Guide](docs/troubleshooting.md)

## 🤝 Contributing

To extend the pipeline builder:

1. **Add New Protocols**: Extend FTP/SFTP module with new protocols
2. **Create Templates**: Add new pipeline templates
3. **Enhance Security**: Improve SSH key management and security
4. **Add Monitoring**: Extend logging and monitoring capabilities

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
