#!/bin/bash

# ═══════════════════════════════════════════════════════════════════════════════
# 🏗️ Universal Pipeline Builder (Unix/Linux/macOS)
# ═══════════════════════════════════════════════════════════════════════════════
# Generate Azure DevOps Pipelines and GitHub Actions based on repository analysis

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
BUILDER_CONFIG_FILE="$PROJECT_ROOT/pipeline-builder-config.json"
TEMPLATES_DIR="$PROJECT_ROOT/pipeline-templates"
OUTPUT_DIR="$PROJECT_ROOT/generated-pipelines"

# Default options
ACTION="menu"
PLATFORM=""
PROJECT_TYPE=""
OUTPUT_PATH=""
INTERACTIVE=false
FORCE=false
VALIDATE=false
JSON=false

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
GRAY='\033[0;37m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --action)
            ACTION="$2"
            shift 2
            ;;
        --platform)
            PLATFORM="$2"
            shift 2
            ;;
        --project-type)
            PROJECT_TYPE="$2"
            shift 2
            ;;
        --output)
            OUTPUT_PATH="$2"
            shift 2
            ;;
        --interactive)
            INTERACTIVE=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --validate)
            VALIDATE=true
            shift
            ;;
        --json)
            JSON=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "OPTIONS:"
            echo "  --action ACTION        Action to perform (menu, analyze, build, export)"
            echo "  --platform PLATFORM   Target platform (azuredevops, github, both)"
            echo "  --project-type TYPE    Project type (auto, dotnet, node, python, docker)"
            echo "  --output PATH          Output directory for generated files"
            echo "  --interactive          Interactive mode"
            echo "  --force                Overwrite existing files"
            echo "  --validate             Validate generated pipelines"
            echo "  --json                 JSON output"
            echo "  --help                 Show this help"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Set output directory
if [[ -n "$OUTPUT_PATH" ]]; then
    OUTPUT_DIR="$OUTPUT_PATH"
fi

# Ensure directories exist
mkdir -p "$TEMPLATES_DIR" "$OUTPUT_DIR"

write_color_text() {
    local text=$1
    local color=${2:-$WHITE}
    if [[ "$JSON" != "true" ]]; then
        echo -e "${color}${text}${NC}"
    fi
}

write_builder_log() {
    local message=$1
    local level=${2:-"INFO"}
    
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    if [[ "$JSON" != "true" ]]; then
        local color
        case $level in
            "ERROR") color=$RED ;;
            "WARN") color=$YELLOW ;;
            "SUCCESS") color=$GREEN ;;
            *) color=$CYAN ;;
        esac
        write_color_text "[$timestamp] [$level] $message" "$color"
    fi
}

show_header() {
    if [[ "$JSON" == "true" ]]; then
        return
    fi
    
    clear
    echo ""
    write_color_text "    🏗️🏗️🏗️ UNIVERSAL PIPELINE BUILDER 🏗️🏗️🏗️" "$CYAN"
    echo ""
    write_color_text "    🚀 Azure DevOps • GitHub Actions • Smart Detection" "$YELLOW"
    write_color_text "    🔧 Auto-Analysis • Templates • Validation" "$GRAY"
    echo ""
    write_color_text "═══════════════════════════════════════════════════════════════════════════════" "$GRAY"
}

initialize_builder_configuration() {
    write_builder_log "Initializing pipeline builder configuration..." "INFO"
    
    if [[ ! -f "$BUILDER_CONFIG_FILE" ]]; then
        cat > "$BUILDER_CONFIG_FILE" << 'EOF'
{
  "projectTypes": {
    "dotnet": {
      "name": ".NET Application",
      "detectionFiles": ["*.csproj", "*.sln", "global.json"],
      "buildCommands": ["dotnet restore", "dotnet build", "dotnet test", "dotnet publish"],
      "dockerSupport": true,
      "testFrameworks": ["xunit", "nunit", "mstest"]
    },
    "node": {
      "name": "Node.js Application",
      "detectionFiles": ["package.json", "yarn.lock", "package-lock.json"],
      "buildCommands": ["npm install", "npm run build", "npm test"],
      "dockerSupport": true,
      "testFrameworks": ["jest", "mocha", "jasmine"]
    },
    "python": {
      "name": "Python Application",
      "detectionFiles": ["requirements.txt", "setup.py", "pyproject.toml", "Pipfile"],
      "buildCommands": ["pip install -r requirements.txt", "python -m pytest"],
      "dockerSupport": true,
      "testFrameworks": ["pytest", "unittest", "nose"]
    },
    "docker": {
      "name": "Docker Application",
      "detectionFiles": ["Dockerfile", "docker-compose.yml", "docker-compose.yaml"],
      "buildCommands": ["docker build", "docker push"],
      "dockerSupport": true
    }
  },
  "deploymentTargets": {
    "azure": {
      "name": "Azure",
      "services": ["webapp", "containerinstances", "kubernetes", "functions"],
      "authMethods": ["serviceprincipal", "managedidentity"]
    },
    "aws": {
      "name": "Amazon Web Services",
      "services": ["ec2", "ecs", "lambda", "beanstalk"],
      "authMethods": ["accesskey", "role"]
    },
    "kubernetes": {
      "name": "Kubernetes",
      "services": ["deployment", "service", "ingress"],
      "authMethods": ["kubeconfig", "token"]
    },
    "docker": {
      "name": "Docker Registry",
      "services": ["registry", "hub"],
      "authMethods": ["username", "token"]
    }
  },
  "platforms": {
    "azuredevops": {
      "name": "Azure DevOps",
      "fileName": "azure-pipelines.yml",
      "syntax": "yaml"
    },
    "github": {
      "name": "GitHub Actions",
      "fileName": ".github/workflows/ci-cd.yml",
      "syntax": "yaml"
    }
  }
}
EOF
        write_builder_log "Default configuration created" "SUCCESS"
    fi
}

analyze_repository() {
    write_builder_log "Analyzing repository structure..." "INFO"
    
    local project_type="unknown"
    local has_docker=false
    local has_tests=false
    local detected_files=()
    local languages=()
    
    # Check for .NET
    if find "$PROJECT_ROOT" -name "*.csproj" -o -name "*.sln" -o -name "global.json" | grep -q .; then
        project_type="dotnet"
        languages+=("csharp")
        write_builder_log "Detected project type: .NET Application" "SUCCESS"
    fi
    
    # Check for Node.js
    if [[ -f "$PROJECT_ROOT/package.json" ]]; then
        project_type="node"
        languages+=("javascript")
        write_builder_log "Detected project type: Node.js Application" "SUCCESS"
    fi
    
    # Check for Python
    if [[ -f "$PROJECT_ROOT/requirements.txt" ]] || [[ -f "$PROJECT_ROOT/setup.py" ]] || [[ -f "$PROJECT_ROOT/pyproject.toml" ]]; then
        project_type="python"
        languages+=("python")
        write_builder_log "Detected project type: Python Application" "SUCCESS"
    fi
    
    # Check for Docker
    if [[ -f "$PROJECT_ROOT/Dockerfile" ]] || [[ -f "$PROJECT_ROOT/docker-compose.yml" ]]; then
        has_docker=true
        write_builder_log "Docker support detected" "INFO"
    fi
    
    # Check for tests
    if find "$PROJECT_ROOT" -name "*test*" -o -name "*Test*" -o -name "*spec*" | grep -q .; then
        has_tests=true
        write_builder_log "Test files detected" "INFO"
    fi
    
    # Output analysis results
    if [[ "$JSON" == "true" ]]; then
        cat << EOF
{
  "projectType": "$project_type",
  "hasDocker": $has_docker,
  "hasTests": $has_tests,
  "languages": ["${languages[*]}"],
  "detectedFiles": ["${detected_files[*]}"]
}
EOF
    else
        echo "Project Type: $project_type"
        echo "Has Docker: $has_docker"
        echo "Has Tests: $has_tests"
        echo "Languages: ${languages[*]}"
    fi
}

generate_azure_devops_pipeline() {
    local project_type=$1
    local has_docker=$2
    local has_tests=$3
    
    write_builder_log "Generating Azure DevOps pipeline..." "INFO"
    
    local output_file="$OUTPUT_DIR/azure-pipelines.yml"
    
    cat > "$output_file" << EOF
# Azure DevOps Pipeline - Generated by Pipeline Builder
# Project Type: $project_type
# Generated: $(date '+%Y-%m-%d %H:%M:%S')

trigger:
  branches:
    include:
      - main
      - develop
      - release/*
      - feature/*

pr:
  branches:
    include:
      - main
      - develop

variables:
  buildConfiguration: 'Release'
  vmImageName: 'ubuntu-latest'

stages:
- stage: Build
  displayName: 'Build and Test'
  jobs:
  - job: Build
    displayName: 'Build job'
    pool:
      vmImage: \$(vmImageName)
    steps:
EOF

    # Add build steps based on project type
    case $project_type in
        "dotnet")
            cat >> "$output_file" << 'EOF'

    - task: UseDotNet@2
      displayName: 'Use .NET SDK'
      inputs:
        packageType: 'sdk'
        version: '8.0.x'
    
    - task: DotNetCoreCLI@2
      displayName: 'Restore packages'
      inputs:
        command: 'restore'
        projects: '**/*.csproj'
    
    - task: DotNetCoreCLI@2
      displayName: 'Build solution'
      inputs:
        command: 'build'
        projects: '**/*.csproj'
        arguments: '--configuration $(buildConfiguration) --no-restore'
EOF
            
            if [[ "$has_tests" == "true" ]]; then
                cat >> "$output_file" << 'EOF'

    - task: DotNetCoreCLI@2
      displayName: 'Run tests'
      inputs:
        command: 'test'
        projects: '**/*Test*.csproj'
        arguments: '--configuration $(buildConfiguration) --no-build --collect:"XPlat Code Coverage"'
EOF
            fi
            
            cat >> "$output_file" << 'EOF'

    - task: DotNetCoreCLI@2
      displayName: 'Publish application'
      inputs:
        command: 'publish'
        projects: '**/*.csproj'
        arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)'
        zipAfterPublish: true
EOF
            ;;
        "node")
            cat >> "$output_file" << 'EOF'

    - task: NodeTool@0
      displayName: 'Use Node.js'
      inputs:
        versionSpec: '18.x'
    
    - script: npm install
      displayName: 'Install dependencies'
    
    - script: npm run build
      displayName: 'Build application'
EOF
            
            if [[ "$has_tests" == "true" ]]; then
                cat >> "$output_file" << 'EOF'

    - script: npm test
      displayName: 'Run tests'
EOF
            fi
            ;;
    esac

    # Add Docker build if detected
    if [[ "$has_docker" == "true" ]]; then
        cat >> "$output_file" << 'EOF'

    - task: Docker@2
      displayName: 'Build Docker image'
      inputs:
        command: 'build'
        dockerfile: 'Dockerfile'
        tags: '$(Build.BuildId)'
EOF
    fi

    cat >> "$output_file" << 'EOF'

    - task: PublishBuildArtifacts@1
      displayName: 'Publish artifacts'
      inputs:
        pathToPublish: '$(Build.ArtifactStagingDirectory)'
        artifactName: 'drop'
EOF

    write_builder_log "Azure DevOps pipeline generated: $output_file" "SUCCESS"
}

show_pipeline_menu() {
    echo ""
    write_color_text "🏗️ Pipeline Builder Menu" "$CYAN"
    echo ""
    write_color_text "    [1] 🔍 Analyze Repository" "$GREEN"
    write_color_text "        └─ Scan repository structure and detect project type"
    echo ""
    write_color_text "    [2] 🚀 Build Azure DevOps Pipeline" "$BLUE"
    write_color_text "        └─ Generate Azure DevOps YAML pipeline"
    echo ""
    write_color_text "    [3] ⚡ Build GitHub Actions Workflow" "$YELLOW"
    write_color_text "        └─ Generate GitHub Actions workflow"
    echo ""
    write_color_text "    [4] 📋 Build Both Platforms" "$MAGENTA"
    write_color_text "        └─ Generate pipelines for both platforms"
    echo ""
    write_color_text "    [0] 🚪 Exit" "$GRAY"
    echo ""
}

# Main execution
show_header
initialize_builder_configuration

if [[ "$ACTION" == "menu" ]]; then
    while true; do
        show_pipeline_menu
        read -p "Enter your choice (0-4): " choice
        
        case $choice in
            1)
                analyze_repository
                read -p "Press Enter to continue"
                ;;
            2)
                analysis=$(analyze_repository)
                project_type=$(echo "$analysis" | grep "Project Type:" | cut -d' ' -f3)
                has_docker=$(echo "$analysis" | grep "Has Docker:" | cut -d' ' -f3)
                has_tests=$(echo "$analysis" | grep "Has Tests:" | cut -d' ' -f3)
                
                generate_azure_devops_pipeline "$project_type" "$has_docker" "$has_tests"
                read -p "Press Enter to continue"
                ;;
            3)
                write_builder_log "GitHub Actions generation functionality coming soon..." "INFO"
                read -p "Press Enter to continue"
                ;;
            4)
                write_builder_log "Multi-platform generation functionality coming soon..." "INFO"
                read -p "Press Enter to continue"
                ;;
            0)
                write_builder_log "Goodbye!" "INFO"
                break
                ;;
            *)
                write_builder_log "Invalid choice. Please try again." "WARN"
                read -p "Press Enter to continue"
                ;;
        esac
    done
else
    case $ACTION in
        "analyze")
            analyze_repository
            ;;
        "build")
            if [[ -z "$PLATFORM" ]]; then
                write_builder_log "Platform parameter required for build action (azuredevops, github, or both)" "ERROR"
                exit 1
            fi
            
            analysis=$(analyze_repository)
            project_type=$(echo "$analysis" | grep "Project Type:" | cut -d' ' -f3)
            has_docker=$(echo "$analysis" | grep "Has Docker:" | cut -d' ' -f3)
            has_tests=$(echo "$analysis" | grep "Has Tests:" | cut -d' ' -f3)
            
            case $PLATFORM in
                "azuredevops")
                    generate_azure_devops_pipeline "$project_type" "$has_docker" "$has_tests"
                    ;;
                "github")
                    write_builder_log "GitHub Actions generation functionality coming soon..." "INFO"
                    ;;
                "both")
                    generate_azure_devops_pipeline "$project_type" "$has_docker" "$has_tests"
                    write_builder_log "GitHub Actions generation functionality coming soon..." "INFO"
                    ;;
            esac
            ;;
        *)
            write_builder_log "Unknown action: $ACTION" "ERROR"
            write_builder_log "Available actions: menu, analyze, build" "INFO"
            exit 1
            ;;
    esac
fi
