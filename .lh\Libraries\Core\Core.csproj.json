{"sourceFile": "Libraries/Core/Core.csproj", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1751296538901, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751371783691, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,14 +1,15 @@\n <Project Sdk=\"Microsoft.NET.Sdk\">\n   <PropertyGroup>\n     <TargetFramework>net9.0</TargetFramework>\n+    <ImplicitUsings>disable</ImplicitUsings>\n   </PropertyGroup>\n   <ItemGroup>\n     <PackageReference Include=\"MailKit\" Version=\"2.11.1\" />\n     <PackageReference Include=\"MimeKit\" Version=\"2.11.0\" />\n     <PackageReference Include=\"Swashbuckle.AspNetCore\" Version=\"6.1.1\" />\n-    <PackageReference Include=\"Microsoft.AspNetCore.SpaServices.Extensions\" Version=\"9.0.6\" />\n-    <PackageReference Include=\"Microsoft.AspNetCore.SpaServices\" Version=\"9.0.6\" />\n+    <PackageReference Include=\"Microsoft.AspNetCore.SpaServices.Extensions\" Version=\"5.0.0-preview.8.20414.8\" />\n+    <PackageReference Include=\"Microsoft.AspNetCore.SpaServices\" Version=\"5.0.0-preview.8.20414.8\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.SqlServer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.Extensions.Configuration\" Version=\"8.0.0\" />\n   </ItemGroup>\n   <ItemGroup>\n"}, {"date": 1751372493397, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,10 +6,10 @@\n   <ItemGroup>\n     <PackageReference Include=\"MailKit\" Version=\"2.11.1\" />\n     <PackageReference Include=\"MimeKit\" Version=\"2.11.0\" />\n     <PackageReference Include=\"Swashbuckle.AspNetCore\" Version=\"6.1.1\" />\n-    <PackageReference Include=\"Microsoft.AspNetCore.SpaServices.Extensions\" Version=\"5.0.0-preview.8.20414.8\" />\n-    <PackageReference Include=\"Microsoft.AspNetCore.SpaServices\" Version=\"5.0.0-preview.8.20414.8\" />\n+    <PackageReference Include=\"Microsoft.AspNetCore.SpaServices.Extensions\" Version=\"9.0.6\" />\n+    <PackageReference Include=\"Microsoft.AspNetCore.SpaServices\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.SqlServer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.Extensions.Configuration\" Version=\"8.0.0\" />\n   </ItemGroup>\n   <ItemGroup>\n"}, {"date": 1751372504804, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,11 +3,11 @@\n     <TargetFramework>net9.0</TargetFramework>\n     <ImplicitUsings>disable</ImplicitUsings>\n   </PropertyGroup>\n   <ItemGroup>\n-    <PackageReference Include=\"MailKit\" Version=\"2.11.1\" />\n-    <PackageReference Include=\"MimeKit\" Version=\"2.11.0\" />\n-    <PackageReference Include=\"Swashbuckle.AspNetCore\" Version=\"6.1.1\" />\n+    <PackageReference Include=\"MailKit\" Version=\"4.9.0\" />\n+    <PackageReference Include=\"MimeKit\" Version=\"4.9.0\" />\n+    <PackageReference Include=\"Swashbuckle.AspNetCore\" Version=\"7.2.0\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.SpaServices.Extensions\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.SpaServices\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.SqlServer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.Extensions.Configuration\" Version=\"8.0.0\" />\n"}], "date": 1751296538901, "name": "Commit-0", "content": "<Project Sdk=\"Microsoft.NET.Sdk\">\n  <PropertyGroup>\n    <TargetFramework>net9.0</TargetFramework>\n  </PropertyGroup>\n  <ItemGroup>\n    <PackageReference Include=\"MailKit\" Version=\"2.11.1\" />\n    <PackageReference Include=\"MimeKit\" Version=\"2.11.0\" />\n    <PackageReference Include=\"Swashbuckle.AspNetCore\" Version=\"6.1.1\" />\n    <PackageReference Include=\"Microsoft.AspNetCore.SpaServices.Extensions\" Version=\"9.0.6\" />\n    <PackageReference Include=\"Microsoft.AspNetCore.SpaServices\" Version=\"9.0.6\" />\n    <PackageReference Include=\"Microsoft.EntityFrameworkCore.SqlServer\" Version=\"9.0.6\" />\n    <PackageReference Include=\"Microsoft.Extensions.Configuration\" Version=\"8.0.0\" />\n  </ItemGroup>\n  <ItemGroup>\n    <ProjectReference Include=\"..\\Data\\Data.csproj\" />\n    <ProjectReference Include=\"..\\Models\\Models.csproj\" />\n    <ProjectReference Include=\"..\\Services\\Services.csproj\" />\n  </ItemGroup>\n</Project>\n"}]}