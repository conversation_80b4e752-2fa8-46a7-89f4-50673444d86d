#!/bin/bash

# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 Notify Service API - Enhanced Local Development Setup (Unix/Linux/macOS)
# ═══════════════════════════════════════════════════════════════════════════════
# Comprehensive setup script with auto-installation, environment management, and Docker support

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
ENV_FILE="$PROJECT_ROOT/.env"
ENV_TEMPLATE="$PROJECT_ROOT/.env.template"

# Default options
AUTO_INSTALL=false
SKIP_DOCKER=false
USE_LOCAL=false
FORCE=false
QUIET=false

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
GRAY='\033[0;37m'
DARK_GRAY='\033[1;30m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Functions
write_color_text() {
    local text=$1
    local color=${2:-$WHITE}
    if [[ "$QUIET" != "true" ]]; then
        echo -e "${color}${text}${NC}"
    fi
}

write_step() {
    local message=$1
    local status=${2:-"INFO"}
    
    if [[ "$QUIET" == "true" && "$status" != "ERROR" ]]; then
        return
    fi
    
    local timestamp=$(date '+%H:%M:%S')
    case $status in
        "SUCCESS") echo -e "[$timestamp] ✅ $message" ;;
        "ERROR")   echo -e "[$timestamp] ❌ $message" ;;
        "WARNING") echo -e "[$timestamp] ⚠️  $message" ;;
        "INFO")    echo -e "[$timestamp] 🔄 $message" ;;
        "INPUT")   echo -e "[$timestamp] 📝 $message" ;;
    esac
}

write_section() {
    local title=$1
    if [[ "$QUIET" == "true" ]]; then
        return
    fi
    echo ""
    echo -e "${DARK_GRAY}┌─────────────────────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${WHITE}│ $(printf "%-75s" "$title") │${NC}"
    echo -e "${DARK_GRAY}└─────────────────────────────────────────────────────────────────────────────┘${NC}"
}

show_header() {
    if [[ "$QUIET" == "true" ]]; then
        return
    fi
    clear
    echo ""
    echo -e "${CYAN}    ███╗   ██╗ ██████╗ ████████╗██╗███████╗██╗   ██╗${NC}"
    echo -e "${CYAN}    ████╗  ██║██╔═══██╗╚══██╔══╝██║██╔════╝╚██╗ ██╔╝${NC}"
    echo -e "${CYAN}    ██╔██╗ ██║██║   ██║   ██║   ██║█████╗   ╚████╔╝ ${NC}"
    echo -e "${CYAN}    ██║╚██╗██║██║   ██║   ██║   ██║██╔══╝    ╚██╔╝  ${NC}"
    echo -e "${CYAN}    ██║ ╚████║╚██████╔╝   ██║   ██║██║        ██║   ${NC}"
    echo -e "${CYAN}    ╚═╝  ╚═══╝ ╚═════╝    ╚═╝   ╚═╝╚═╝        ╚═╝   ${NC}"
    echo ""
    echo -e "${YELLOW}    🚀 Enhanced Local Development Setup${NC}"
    echo -e "${GRAY}    🔧 Auto-Install • Environment • Docker • Validation${NC}"
    echo ""
    echo -e "${DARK_GRAY}═══════════════════════════════════════════════════════════════════════════════${NC}"
}

show_help() {
    write_color_text "🎯 Enhanced Local Development Setup" "$YELLOW"
    echo ""
    write_color_text "USAGE:" "$WHITE"
    write_color_text "  ./scripts/unix/setup-local-dev.sh [OPTIONS]" "$GRAY"
    echo ""
    write_color_text "OPTIONS:" "$WHITE"
    write_color_text "  --auto-install   Automatically install missing dependencies" "$GRAY"
    write_color_text "  --skip-docker    Skip Docker-based setup (use local services)" "$GRAY"
    write_color_text "  --use-local      Use local PostgreSQL/Redis instead of Docker" "$GRAY"
    write_color_text "  --force          Force reinstallation of existing components" "$GRAY"
    write_color_text "  --quiet          Minimal output (errors only)" "$GRAY"
    write_color_text "  --help           Show this help message" "$GRAY"
    echo ""
    write_color_text "EXAMPLES:" "$WHITE"
    write_color_text "  ./scripts/unix/setup-local-dev.sh                    # Interactive setup" "$GRAY"
    write_color_text "  ./scripts/unix/setup-local-dev.sh --auto-install     # Auto-install dependencies" "$GRAY"
    write_color_text "  ./scripts/unix/setup-local-dev.sh --use-local        # Use local services" "$GRAY"
    write_color_text "  ./scripts/unix/setup-local-dev.sh --quiet            # Silent setup" "$GRAY"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --auto-install)
            AUTO_INSTALL=true
            shift
            ;;
        --skip-docker)
            SKIP_DOCKER=true
            shift
            ;;
        --use-local)
            USE_LOCAL=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --quiet)
            QUIET=true
            shift
            ;;
        --help)
            show_header
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

test_dependency() {
    local name=$1
    local command=$2
    local install_command=${3:-""}
    local download_url=${4:-""}
    local required=${5:-true}
    
    write_step "Checking $name..." "INFO"
    
    if command -v ${command%% *} &> /dev/null; then
        if eval "$command" &> /dev/null; then
            write_step "$name is installed and accessible" "SUCCESS"
            return 0
        fi
    fi
    
    if $required; then
        write_step "$name is not installed or not accessible" "ERROR"
        
        if [[ "$AUTO_INSTALL" == "true" && -n "$install_command" ]]; then
            write_step "Attempting to install $name automatically..." "INFO"
            if eval "$install_command"; then
                write_step "$name installed successfully" "SUCCESS"
                return 0
            else
                write_step "Failed to install $name automatically" "ERROR"
            fi
        fi
        
        if [[ -n "$download_url" ]]; then
            write_color_text "    📥 Manual installation required" "$YELLOW"
            write_color_text "    🔗 Download from: $download_url" "$CYAN"
        fi
        
        if [[ "$AUTO_INSTALL" != "true" && -n "$install_command" ]]; then
            write_color_text "    💡 Auto-install option available" "$CYAN"
            write_color_text "    🔄 Run with --auto-install to install automatically" "$GRAY"
        fi
        
        return 1
    else
        write_step "$name is not installed (optional)" "WARNING"
        return 0
    fi
}

initialize_environment() {
    write_section "🔧 Environment Configuration"
    
    # Check if .env file exists
    if [[ ! -f "$ENV_FILE" ]]; then
        write_step "No .env file found, creating from template..." "INFO"
        
        if [[ -f "$ENV_TEMPLATE" ]]; then
            cp "$ENV_TEMPLATE" "$ENV_FILE"
            write_step ".env file created from template" "SUCCESS"
        else
            # Create basic .env file
            cat > "$ENV_FILE" << 'EOF'
# Notify Service API - Environment Configuration
# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=NotifyDb

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Application Configuration
ASPNETCORE_ENVIRONMENT=Development
ASPNETCORE_URLS=https://localhost:5001;http://localhost:5000

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ISSUER=NotifyServiceAPI
JWT_AUDIENCE=NotifyServiceAPI

# Logging
LOG_LEVEL=Information
EOF
            write_step "Default .env file created" "SUCCESS"
        fi
    else
        write_step "Environment file found" "SUCCESS"
    fi
    
    # Load environment variables
    if [[ -f "$ENV_FILE" ]]; then
        write_step "Loading environment variables..." "INFO"
        set -a
        source "$ENV_FILE"
        set +a
        write_step "Environment variables loaded" "SUCCESS"
    fi
}

test_docker_setup() {
    write_section "🐳 Docker Environment Check"

    if [[ "$SKIP_DOCKER" == "true" ]]; then
        write_step "Skipping Docker setup (--skip-docker specified)" "WARNING"
        return 1
    fi

    # Check if Docker is installed and running
    local docker_install=""
    if [[ "$OSTYPE" == "darwin"* ]]; then
        docker_install="brew install --cask docker"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if command -v apt-get &> /dev/null; then
            docker_install="sudo apt-get update && sudo apt-get install -y docker.io"
        elif command -v yum &> /dev/null; then
            docker_install="sudo yum install -y docker"
        fi
    fi

    if ! test_dependency "Docker" "docker --version" "$docker_install" "https://www.docker.com/products/docker-desktop"; then
        return 1
    fi

    # Check if Docker daemon is running
    write_step "Checking Docker daemon..." "INFO"
    if docker info &> /dev/null; then
        write_step "Docker daemon is running" "SUCCESS"
        return 0
    else
        write_step "Docker daemon is not running" "ERROR"
        write_color_text "    🔄 Please start Docker and try again" "$YELLOW"
        return 1
    fi
}

show_header
write_step "Initializing enhanced local development setup..." "INFO"

write_section "🔍 Dependency Check & Installation"

# Check dependencies
dependencies_ok=true

# Check .NET SDK
dotnet_install=""
if [[ "$OSTYPE" == "darwin"* ]]; then
    dotnet_install="brew install --cask dotnet"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    dotnet_install="wget https://dot.net/v1/dotnet-install.sh -O dotnet-install.sh && chmod +x dotnet-install.sh && ./dotnet-install.sh --channel 8.0"
fi

if ! test_dependency ".NET 8 SDK" "dotnet --version" "$dotnet_install" "https://dotnet.microsoft.com/download/dotnet/8.0"; then
    dependencies_ok=false
fi

# Check Git (optional)
git_install=""
if [[ "$OSTYPE" == "darwin"* ]]; then
    git_install="brew install git"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    if command -v apt-get &> /dev/null; then
        git_install="sudo apt-get update && sudo apt-get install -y git"
    elif command -v yum &> /dev/null; then
        git_install="sudo yum install -y git"
    fi
fi

test_dependency "Git" "git --version" "$git_install" "https://git-scm.com/download" false

if ! $dependencies_ok; then
    write_step "Required dependencies are missing. Please install them and run this script again." "ERROR"
    exit 1
fi

# Initialize environment
initialize_environment

# Setup Docker or local services
use_docker=false
if test_docker_setup; then
    use_docker=true
fi

if [[ "$USE_LOCAL" == "true" || "$use_docker" == "false" ]]; then
    write_section "🔧 Local Services Setup"

    # Check PostgreSQL
    postgres_install=""
    if [[ "$OSTYPE" == "darwin"* ]]; then
        postgres_install="brew install postgresql@15"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if command -v apt-get &> /dev/null; then
            postgres_install="sudo apt-get update && sudo apt-get install -y postgresql postgresql-contrib"
        elif command -v yum &> /dev/null; then
            postgres_install="sudo yum install -y postgresql postgresql-server"
        fi
    fi

    if test_dependency "PostgreSQL" "psql -U postgres -c \"SELECT version();\"" "$postgres_install" "https://www.postgresql.org/download/"; then
        write_step "Creating databases..." "INFO"
        create_db_script="$SCRIPT_DIR/../create-databases.sql"
        if [[ -f "$create_db_script" ]]; then
            if psql -U postgres -f "$create_db_script" &> /dev/null; then
                write_step "Databases created successfully" "SUCCESS"
            else
                write_step "Database creation failed or databases already exist" "WARNING"
            fi
        else
            write_step "Database creation script not found" "WARNING"
        fi
    fi

    # Check Redis
    redis_install=""
    if [[ "$OSTYPE" == "darwin"* ]]; then
        redis_install="brew install redis"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if command -v apt-get &> /dev/null; then
            redis_install="sudo apt-get update && sudo apt-get install -y redis-server"
        elif command -v yum &> /dev/null; then
            redis_install="sudo yum install -y redis"
        fi
    fi

    test_dependency "Redis" "redis-cli ping" "$redis_install" "https://redis.io/download"

else
    write_section "🐳 Docker Services Setup"

    write_step "Starting PostgreSQL container..." "INFO"
    postgres_password=${POSTGRES_PASSWORD:-"postgres"}
    if docker run -d --name notify-postgres -e POSTGRES_PASSWORD="$postgres_password" -e POSTGRES_DB=postgres -p 5432:5432 postgres:15-alpine &> /dev/null; then
        write_step "PostgreSQL container started successfully" "SUCCESS"
    else
        write_step "PostgreSQL container already exists, attempting to start..." "WARNING"
        if docker start notify-postgres &> /dev/null; then
            write_step "Existing PostgreSQL container started" "SUCCESS"
        else
            write_step "Failed to start PostgreSQL container" "ERROR"
        fi
    fi

    write_step "Starting Redis container..." "INFO"
    redis_password=${REDIS_PASSWORD:-""}
    redis_cmd=""
    if [[ -n "$redis_password" ]]; then
        redis_cmd="redis-server --requirepass $redis_password"
    fi

    if docker run -d --name notify-redis -p 6379:6379 redis:7-alpine $redis_cmd &> /dev/null; then
        write_step "Redis container started successfully" "SUCCESS"
    else
        write_step "Redis container already exists, attempting to start..." "WARNING"
        if docker start notify-redis &> /dev/null; then
            write_step "Existing Redis container started" "SUCCESS"
        else
            write_step "Failed to start Redis container" "ERROR"
        fi
    fi

    # Wait for services to be ready
    write_step "Waiting for services to initialize..." "INFO"
    sleep 5

    # Create databases in Docker PostgreSQL
    write_step "Creating databases in PostgreSQL container..." "INFO"
    databases=("NotifyDb" "NotifyIdentityDb")
    for db in "${databases[@]}"; do
        if docker exec notify-postgres psql -U postgres -c "CREATE DATABASE \"$db\";" &> /dev/null; then
            write_step "Database '$db' created successfully" "SUCCESS"
        else
            write_step "Database '$db' already exists or creation failed" "WARNING"
        fi
    done
fi

write_section "🧪 Service Validation"

# Test connections
if [[ "$use_docker" == "true" && "$USE_LOCAL" != "true" ]]; then
    # Test Docker services
    write_step "Testing PostgreSQL connection..." "INFO"
    if docker exec notify-postgres psql -U postgres -c "SELECT 1;" &> /dev/null; then
        write_step "PostgreSQL connection successful" "SUCCESS"
    else
        write_step "PostgreSQL connection failed" "ERROR"
    fi

    write_step "Testing Redis connection..." "INFO"
    redis_ping=$(docker exec notify-redis redis-cli ping 2>/dev/null || echo "")
    if [[ "$redis_ping" == "PONG" ]]; then
        write_step "Redis connection successful" "SUCCESS"
    else
        write_step "Redis connection failed" "ERROR"
    fi
else
    # Test local services
    write_step "Testing local PostgreSQL connection..." "INFO"
    if psql -U postgres -c "SELECT 1;" &> /dev/null; then
        write_step "Local PostgreSQL connection successful" "SUCCESS"
    else
        write_step "Local PostgreSQL connection failed" "ERROR"
    fi

    write_step "Testing local Redis connection..." "INFO"
    redis_ping=$(redis-cli ping 2>/dev/null || echo "")
    if [[ "$redis_ping" == "PONG" ]]; then
        write_step "Local Redis connection successful" "SUCCESS"
    else
        write_step "Local Redis connection failed" "ERROR"
    fi
fi

write_section "🎉 Setup Complete!"

echo ""
write_color_text "    🚀 Your development environment is ready!" "$GREEN"
echo ""
write_color_text "    📊 Services Status:" "$WHITE"
if [[ "$use_docker" == "true" && "$USE_LOCAL" != "true" ]]; then
    write_color_text "    ├─ 🗄️  PostgreSQL: Docker container (localhost:5432)" "$GRAY"
    write_color_text "    └─ 🔴 Redis: Docker container (localhost:6379)" "$GRAY"
else
    write_color_text "    ├─ 🗄️  PostgreSQL: Local service (localhost:5432)" "$GRAY"
    write_color_text "    └─ 🔴 Redis: Local service (localhost:6379)" "$GRAY"
fi
echo ""
write_color_text "    🏃‍♂️ Next Steps:" "$WHITE"
write_color_text "    ├─ Run the API: dotnet run --project Presentations/WebApi" "$CYAN"
write_color_text "    ├─ API Docs: https://localhost:5001/scalar" "$CYAN"
write_color_text "    └─ Health Check: https://localhost:5001/health" "$CYAN"
echo ""
if [[ "$use_docker" == "true" && "$USE_LOCAL" != "true" ]]; then
    write_color_text "    🛑 To stop Docker services later:" "$WHITE"
    write_color_text "    └─ docker stop notify-postgres notify-redis" "$RED"
fi
echo ""
write_color_text "═══════════════════════════════════════════════════════════════════════════════" "$DARK_GRAY"
