{"sourceFile": "Libraries/Data/Data.csproj", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751296538902, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751296538902, "name": "Commit-0", "content": "<Project Sdk=\"Microsoft.NET.Sdk\">\n  <PropertyGroup>\n    <TargetFramework>net9.0</TargetFramework>\n  </PropertyGroup>\n  <ItemGroup>\n    <ProjectReference Include=\"..\\Models\\Models.csproj\" />\n  </ItemGroup>\n  <PropertyGroup>\n    <LangVersion>preview</LangVersion>\n  </PropertyGroup>\n  <ItemGroup>\n    <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\n    <PackageReference Include=\"Microsoft.EntityFrameworkCore.Design\" Version=\"9.0.6\" />\n    <PackageReference Include=\"Microsoft.EntityFrameworkCore.SqlServer\" Version=\"9.0.6\" />\n    <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.4\" />\n  </ItemGroup>\n</Project>\n"}]}