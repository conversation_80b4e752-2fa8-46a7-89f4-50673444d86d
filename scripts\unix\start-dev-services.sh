#!/bin/bash

# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 Notify Service API - Development Environment Setup (Unix/Linux/macOS)
# ═══════════════════════════════════════════════════════════════════════════════
# This script starts PostgreSQL and Redis using Docker and applies migrations

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
GRAY='\033[0;37m'
DARK_GRAY='\033[1;30m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
POSTGRES_CONTAINER="notify-postgres"
REDIS_CONTAINER="notify-redis"
DATABASE_NAMES=("NotifyDb" "NotifyIdentityDb")

# ASCII Art Header
echo ""
echo -e "${CYAN}    ███╗   ██╗ ██████╗ ████████╗██╗███████╗██╗   ██╗${NC}"
echo -e "${CYAN}    ████╗  ██║██╔═══██╗╚══██╔══╝██║██╔════╝╚██╗ ██╔╝${NC}"
echo -e "${CYAN}    ██╔██╗ ██║██║   ██║   ██║   ██║█████╗   ╚████╔╝ ${NC}"
echo -e "${CYAN}    ██║╚██╗██║██║   ██║   ██║   ██║██╔══╝    ╚██╔╝  ${NC}"
echo -e "${CYAN}    ██║ ╚████║╚██████╔╝   ██║   ██║██║        ██║   ${NC}"
echo -e "${CYAN}    ╚═╝  ╚═══╝ ╚═════╝    ╚═╝   ╚═╝╚═╝        ╚═╝   ${NC}"
echo ""
echo -e "${YELLOW}    🔧 Development Environment Setup${NC}"
echo -e "${GRAY}    📦 Docker • PostgreSQL • Redis • Migrations${NC}"
echo ""
echo -e "${DARK_GRAY}═══════════════════════════════════════════════════════════════════════════════${NC}"

# Functions
log_step() {
    local message=$1
    local status=${2:-"INFO"}
    local timestamp=$(date '+%H:%M:%S')
    
    case $status in
        "SUCCESS") echo -e "[$timestamp] ✅ $message" ;;
        "ERROR")   echo -e "[$timestamp] ❌ $message" ;;
        "WARNING") echo -e "[$timestamp] ⚠️  $message" ;;
        "INFO")    echo -e "[$timestamp] 🔄 $message" ;;
        "WAIT")    echo -e "[$timestamp] ⏳ $message" ;;
    esac
}

log_section() {
    local title=$1
    echo ""
    echo -e "${DARK_GRAY}┌─────────────────────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${WHITE}│ $(printf "%-75s" "$title") │${NC}"
    echo -e "${DARK_GRAY}└─────────────────────────────────────────────────────────────────────────────┘${NC}"
}

test_dependency() {
    local name=$1
    local command=$2
    local install_command=${3:-""}
    local download_url=${4:-""}
    local required=${5:-true}
    
    log_step "Checking $name..." "INFO"
    
    if command -v ${command%% *} &> /dev/null; then
        if $command &> /dev/null; then
            log_step "$name is installed and accessible" "SUCCESS"
            return 0
        fi
    fi
    
    if $required; then
        log_step "$name is not installed or not accessible" "ERROR"
        
        if [[ -n "$install_command" ]]; then
            echo -e "${CYAN}    💡 Auto-install option available${NC}"
            read -p "    Would you like to install $name automatically? (y/N): " install
            
            if [[ "$install" == "y" || "$install" == "Y" ]]; then
                log_step "Installing $name..." "INFO"
                if eval "$install_command"; then
                    log_step "$name installed successfully" "SUCCESS"
                    return 0
                else
                    log_step "Failed to install $name automatically" "ERROR"
                fi
            fi
        fi
        
        if [[ -n "$download_url" ]]; then
            echo -e "${YELLOW}    📥 Manual installation required${NC}"
            echo -e "${CYAN}    🔗 Download from: $download_url${NC}"
        fi
        
        echo ""
        return 1
    else
        log_step "$name is not installed (optional)" "WARNING"
        return 0
    fi
}

load_environment_variables() {
    local env_file="$PROJECT_ROOT/.env"
    if [[ -f "$env_file" ]]; then
        log_step "Loading environment variables from .env file..." "INFO"
        # Export variables from .env file
        set -a
        source "$env_file"
        set +a
        log_step "Environment variables loaded successfully" "SUCCESS"
    else
        log_step "No .env file found, using default values" "WARNING"
        log_step "Run ./scripts/unix/config-manager.sh to configure environment" "INFO"
    fi
}

# Load environment variables
log_step "Initializing development environment setup..." "INFO"
load_environment_variables

# Update configuration with environment variables
POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-"postgres"}
POSTGRES_USER=${POSTGRES_USER:-"postgres"}
POSTGRES_HOST=${POSTGRES_HOST:-"localhost"}
REDIS_PASSWORD=${REDIS_PASSWORD:-""}

log_section "🔍 Dependency Check & Installation"

# Check dependencies
dependencies_ok=true

# Check Docker
if ! test_dependency "Docker" "docker version" "" "https://www.docker.com/products/docker-desktop"; then
    dependencies_ok=false
fi

# Check .NET SDK
dotnet_install=""
if [[ "$OSTYPE" == "darwin"* ]]; then
    dotnet_install="brew install --cask dotnet"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    dotnet_install="wget https://dot.net/v1/dotnet-install.sh -O dotnet-install.sh && chmod +x dotnet-install.sh && ./dotnet-install.sh --channel 8.0"
fi

if ! test_dependency ".NET 8 SDK" "dotnet --version" "$dotnet_install" "https://dotnet.microsoft.com/download/dotnet/8.0"; then
    dependencies_ok=false
fi

# Check Git (optional)
git_install=""
if [[ "$OSTYPE" == "darwin"* ]]; then
    git_install="brew install git"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    if command -v apt-get &> /dev/null; then
        git_install="sudo apt-get update && sudo apt-get install -y git"
    elif command -v yum &> /dev/null; then
        git_install="sudo yum install -y git"
    fi
fi

test_dependency "Git" "git --version" "$git_install" "https://git-scm.com/download" false

# Check package managers
if [[ "$OSTYPE" == "darwin"* ]]; then
    test_dependency "Homebrew" "brew --version" '/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"' "https://brew.sh" false
fi

if ! $dependencies_ok; then
    log_step "Required dependencies are missing. Please install them and run this script again." "ERROR"
    exit 1
fi

log_section "🐳 Docker Environment Check"

log_step "Verifying Docker is running..." "INFO"
if ! docker info &> /dev/null; then
    log_step "Docker daemon is not running" "ERROR"
    echo -e "${YELLOW}    🔄 Please start Docker and try again${NC}"
    exit 1
fi
log_step "Docker daemon is running" "SUCCESS"

log_section "🗄️ PostgreSQL Database Setup"

log_step "Starting PostgreSQL container..." "INFO"
if docker run -d \
    --name "$POSTGRES_CONTAINER" \
    -e POSTGRES_PASSWORD="$POSTGRES_PASSWORD" \
    -e POSTGRES_DB=postgres \
    -p 5432:5432 \
    postgres:15-alpine &> /dev/null; then
    log_step "PostgreSQL container started successfully" "SUCCESS"
else
    log_step "PostgreSQL container already exists, attempting to start..." "WARNING"
    if docker start "$POSTGRES_CONTAINER" &> /dev/null; then
        log_step "Existing PostgreSQL container started" "SUCCESS"
    else
        log_step "Failed to start PostgreSQL container" "ERROR"
        exit 1
    fi
fi

log_section "🔴 Redis Cache Setup"

log_step "Starting Redis container..." "INFO"
if [[ -n "$REDIS_PASSWORD" ]]; then
    redis_cmd="redis-server --requirepass $REDIS_PASSWORD"
else
    redis_cmd=""
fi

if docker run -d \
    --name "$REDIS_CONTAINER" \
    -p 6379:6379 \
    redis:7-alpine $redis_cmd &> /dev/null; then
    log_step "Redis container started successfully" "SUCCESS"
else
    log_step "Redis container already exists, attempting to start..." "WARNING"
    if docker start "$REDIS_CONTAINER" &> /dev/null; then
        log_step "Existing Redis container started" "SUCCESS"
    else
        log_step "Failed to start Redis container" "ERROR"
        exit 1
    fi
fi

log_section "⏳ Service Initialization"

log_step "Waiting for services to initialize..." "WAIT"
sleep 5

# Wait for PostgreSQL to be ready
max_attempts=30
attempt=0
while [[ $attempt -lt $max_attempts ]]; do
    ((attempt++))
    log_step "Checking PostgreSQL readiness (attempt $attempt/$max_attempts)..." "INFO"
    if docker exec "$POSTGRES_CONTAINER" pg_isready -U postgres &> /dev/null; then
        log_step "PostgreSQL is ready!" "SUCCESS"
        break
    fi
    sleep 2
done

if [[ $attempt -ge $max_attempts ]]; then
    log_step "PostgreSQL failed to become ready within timeout" "ERROR"
    exit 1
fi

log_section "🏗️ Database Creation"

for db_name in "${DATABASE_NAMES[@]}"; do
    log_step "Creating database: $db_name..." "INFO"
    if docker exec "$POSTGRES_CONTAINER" psql -U postgres -c "CREATE DATABASE \"$db_name\";" &> /dev/null; then
        log_step "Database '$db_name' created successfully" "SUCCESS"
    else
        log_step "Database '$db_name' already exists or creation failed" "WARNING"
    fi
done

log_section "🔧 Entity Framework Migrations"

# Check if .NET SDK is available
log_step "Checking .NET SDK..." "INFO"
if command -v dotnet &> /dev/null && dotnet --version &> /dev/null; then
    dotnet_version=$(dotnet --version)
    log_step ".NET SDK found: $dotnet_version" "SUCCESS"
else
    log_step ".NET SDK not found, skipping migrations" "WARNING"
    skip_migrations=true
fi

if [[ "$skip_migrations" != "true" ]]; then
    # Navigate to project root
    cd "$PROJECT_ROOT"

    log_step "Restoring NuGet packages..." "INFO"
    if dotnet restore &> /dev/null; then
        log_step "NuGet packages restored successfully" "SUCCESS"
    else
        log_step "Failed to restore NuGet packages" "ERROR"
    fi

    # Check if migrations exist, if not create them
    log_step "Checking for existing migrations..." "INFO"

    data_project_path="Libraries/Data"
    identity_project_path="Libraries/Identity"
    webapi_project_path="Presentations/WebApi"

    # Check Data migrations
    if [[ ! -d "$data_project_path/Migrations" ]]; then
        log_step "Creating initial Data migration..." "INFO"
        if dotnet ef migrations add InitialCreate --project "$data_project_path" --startup-project "$webapi_project_path" --context ApplicationDbContext; then
            log_step "Data migration created successfully" "SUCCESS"
        else
            log_step "Failed to create Data migration" "ERROR"
        fi
    else
        log_step "Data migrations already exist" "SUCCESS"
    fi

    # Check Identity migrations
    if [[ ! -d "$identity_project_path/Migrations" ]]; then
        log_step "Creating initial Identity migration..." "INFO"
        if dotnet ef migrations add InitialCreate --project "$identity_project_path" --startup-project "$webapi_project_path" --context IdentityContext; then
            log_step "Identity migration created successfully" "SUCCESS"
        else
            log_step "Failed to create Identity migration" "ERROR"
        fi
    else
        log_step "Identity migrations already exist" "SUCCESS"
    fi

    # Apply migrations
    log_step "Applying Data migrations..." "INFO"
    if dotnet ef database update --project "$data_project_path" --startup-project "$webapi_project_path" --context ApplicationDbContext; then
        log_step "Data migrations applied successfully" "SUCCESS"
    else
        log_step "Failed to apply Data migrations" "ERROR"
    fi

    log_step "Applying Identity migrations..." "INFO"
    if dotnet ef database update --project "$identity_project_path" --startup-project "$webapi_project_path" --context IdentityContext; then
        log_step "Identity migrations applied successfully" "SUCCESS"
    else
        log_step "Failed to apply Identity migrations" "ERROR"
    fi
fi

log_section "🧪 Connection Testing"

# Test PostgreSQL
log_step "Testing PostgreSQL connection..." "INFO"
if docker exec "$POSTGRES_CONTAINER" psql -U postgres -c "SELECT 1;" &> /dev/null; then
    log_step "PostgreSQL connection successful" "SUCCESS"
else
    log_step "PostgreSQL connection failed" "ERROR"
fi

# Test Redis
log_step "Testing Redis connection..." "INFO"
if redis_ping=$(docker exec "$REDIS_CONTAINER" redis-cli ping 2>/dev/null) && [[ "$redis_ping" == "PONG" ]]; then
    log_step "Redis connection successful" "SUCCESS"
else
    log_step "Redis connection failed" "ERROR"
fi

log_section "🎉 Setup Complete!"

echo ""
echo -e "${GREEN}    🚀 Your development environment is ready!${NC}"
echo ""
echo -e "${WHITE}    📊 Services Status:${NC}"
echo -e "${GRAY}    ├─ 🗄️  PostgreSQL: ${CYAN}localhost:5432${NC}"
echo -e "${GRAY}    │   ├─ Username: ${YELLOW}postgres${NC}"
echo -e "${GRAY}    │   ├─ Password: ${YELLOW}$POSTGRES_PASSWORD${NC}"
echo -e "${GRAY}    │   └─ Databases: ${YELLOW}NotifyDb, NotifyIdentityDb${NC}"
echo -e "${GRAY}    └─ 🔴 Redis: ${CYAN}localhost:6379${NC}"
echo ""
echo -e "${WHITE}    🏃‍♂️ Next Steps:${NC}"
echo -e "${GRAY}    ├─ Run the API: ${GREEN}dotnet run --project Presentations/WebApi${NC}"
echo -e "${GRAY}    ├─ API Docs: ${CYAN}https://localhost:5001/scalar${NC}"
echo -e "${GRAY}    └─ Health Check: ${CYAN}https://localhost:5001/health${NC}"
echo ""
echo -e "${WHITE}    🛑 To stop services later:${NC}"
echo -e "${GRAY}    └─ ${RED}docker stop $POSTGRES_CONTAINER $REDIS_CONTAINER${NC}"
echo ""
echo -e "${DARK_GRAY}═══════════════════════════════════════════════════════════════════════════════${NC}"
