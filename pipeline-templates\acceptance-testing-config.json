{"acceptanceTesting": {"enabled": true, "description": "Comprehensive acceptance testing configuration for CI/CD pipelines", "codeCoverage": {"minimum": 80, "target": 90, "format": "cobertura", "excludePatterns": ["**/bin/**", "**/obj/**", "**/wwwroot/**", "**/Migrations/**", "**/*Test*.cs", "**/*Spec*.cs"], "includePatterns": ["**/src/**", "**/Controllers/**", "**/Services/**", "**/Models/**"], "failBelowThreshold": true, "generateReport": true, "reportFormats": ["html", "xml", "json"], "branchCoverage": {"minimum": 75, "enabled": true}, "lineCoverage": {"minimum": 80, "enabled": true}}, "benchmarkTesting": {"enabled": true, "framework": "BenchmarkDotNet", "configuration": "Release", "thresholds": {"maxExecutionTime": 5000, "maxMemoryUsage": 100, "performanceRegression": 10, "throughputMinimum": 1000}, "compareBaseline": true, "baselineFile": "performance-baseline.json", "categories": ["API", "Database", "<PERSON><PERSON>", "Serialization", "Computation"], "exportFormats": ["json", "html", "csv"], "warmupIterations": 3, "measurementIterations": 10, "memoryDiagnoser": true, "statisticalTest": "<PERSON><PERSON><PERSON><PERSON>", "outlierMode": "RemoveUpper"}, "integrationTesting": {"enabled": true, "testContainers": {"enabled": true, "services": [{"name": "postgres", "image": "postgres:15-alpine", "environment": {"POSTGRES_PASSWORD": "test", "POSTGRES_DB": "testdb"}, "ports": ["5432:5432"], "healthCheck": {"test": ["CMD-SHELL", "pg_isready -U postgres"], "interval": "10s", "timeout": "5s", "retries": 5}}, {"name": "redis", "image": "redis:7-alpine", "ports": ["6379:6379"], "healthCheck": {"test": ["CMD", "redis-cli", "ping"], "interval": "10s", "timeout": "3s", "retries": 3}}]}, "databaseTests": {"enabled": true, "migrationTests": true, "seedDataTests": true, "transactionTests": true, "concurrencyTests": true}, "apiTests": {"enabled": true, "authenticationTests": true, "authorizationTests": true, "validationTests": true, "errorHandlingTests": true, "rateLimitingTests": true}, "externalServiceTests": {"enabled": true, "mockExternalServices": true, "contractTesting": true, "circuitBreakerTests": true}}, "endToEndTesting": {"enabled": false, "framework": "Playwright", "browsers": ["chromium", "firefox", "webkit"], "parallel": true, "maxParallel": 3, "retries": 2, "timeout": 30000, "baseUrl": "http://localhost:5000", "testData": {"useFixtures": true, "cleanupAfterTests": true, "isolateTests": true}, "screenshots": {"onFailure": true, "onSuccess": false, "fullPage": true}, "video": {"enabled": true, "onFailure": true, "quality": "medium"}, "accessibility": {"enabled": true, "standard": "WCAG2.1", "level": "AA"}, "performance": {"enabled": true, "metrics": ["FCP", "LCP", "CLS", "FID"], "thresholds": {"FCP": 2000, "LCP": 4000, "CLS": 0.1, "FID": 100}}}, "loadTesting": {"enabled": false, "framework": "k6", "scenarios": [{"name": "baseline", "executor": "constant-vus", "vus": 10, "duration": "30s"}, {"name": "stress", "executor": "ramping-vus", "stages": [{"duration": "2m", "target": 100}, {"duration": "5m", "target": 100}, {"duration": "2m", "target": 200}, {"duration": "5m", "target": 200}, {"duration": "2m", "target": 0}]}], "thresholds": {"http_req_duration": ["p(95)<500"], "http_req_failed": ["rate<0.01"], "http_reqs": ["rate>100"]}}, "securityTesting": {"enabled": true, "staticAnalysis": {"enabled": true, "tools": ["CodeQL", "SonarQube", "Semgrep"], "failOnHighSeverity": true, "failOnMediumSeverity": false}, "dependencyScanning": {"enabled": true, "tools": ["Snyk", "OWASP Dependency Check"], "failOnHighVulnerability": true, "allowedLicenses": ["MIT", "Apache-2.0", "BSD-3-<PERSON><PERSON>"]}, "containerScanning": {"enabled": true, "tools": ["Trivy", "<PERSON>"], "scanBaseImages": true, "failOnCritical": true}, "dynamicTesting": {"enabled": false, "tools": ["OWASP ZAP"], "targetUrl": "http://localhost:5000", "authenticationRequired": true}}, "qualityGates": {"sonarQube": {"enabled": false, "serverUrl": "https://sonarcloud.io", "organization": "your-org", "projectKey": "notify-service-api", "qualityGate": "Sonar way", "conditions": {"coverage": {"operator": "LT", "threshold": 80, "onNewCode": true}, "duplicatedLines": {"operator": "GT", "threshold": 3, "onNewCode": true}, "maintainabilityRating": {"operator": "GT", "threshold": "A", "onNewCode": true}, "reliabilityRating": {"operator": "GT", "threshold": "A", "onNewCode": true}, "securityRating": {"operator": "GT", "threshold": "A", "onNewCode": true}, "securityHotspots": {"operator": "GT", "threshold": 0, "onNewCode": true}}}, "codeClimate": {"enabled": false, "maintainabilityThreshold": "A", "testCoverageThreshold": 80, "duplicationThreshold": 5, "complexityThreshold": 10}, "customGates": [{"name": "API Response Time", "condition": "average_response_time < 200ms", "required": true}, {"name": "Memory Usage", "condition": "max_memory_usage < 512MB", "required": true}, {"name": "Error Rate", "condition": "error_rate < 0.1%", "required": true}]}, "reporting": {"enabled": true, "formats": ["html", "json", "junit"], "publishToCI": true, "archiveArtifacts": true, "retentionDays": 30, "notifications": {"onFailure": true, "onSuccess": false, "onRegression": true, "channels": ["slack", "email"]}, "dashboards": {"enabled": true, "tools": ["<PERSON><PERSON>", "Azure DevOps Analytics"], "metrics": ["test_execution_time", "code_coverage_trend", "performance_trend", "failure_rate"]}}, "environments": {"dev": {"codeCoverageMinimum": 70, "benchmarkingEnabled": true, "e2eTestingEnabled": false, "loadTestingEnabled": false}, "staging": {"codeCoverageMinimum": 80, "benchmarkingEnabled": true, "e2eTestingEnabled": true, "loadTestingEnabled": true}, "prod": {"codeCoverageMinimum": 85, "benchmarkingEnabled": false, "e2eTestingEnabled": false, "loadTestingEnabled": false, "smokeTestsOnly": true}}, "parallelization": {"enabled": true, "maxParallelJobs": 4, "testSharding": {"enabled": true, "strategy": "by_test_file", "shardCount": 4}}, "retryPolicy": {"enabled": true, "maxRetries": 2, "retryOnFailure": true, "retryDelay": "30s", "exponentialBackoff": true}, "cleanup": {"enabled": true, "cleanupAfterTests": true, "cleanupOnFailure": true, "preserveArtifactsOnFailure": true}}}