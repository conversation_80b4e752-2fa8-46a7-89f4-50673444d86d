# 🚀 Notify Service API - Complete Developer Guide

Welcome to the **Notify Service API**, a modern, scalable notification service built with **.NET 9.0**. This comprehensive guide will help you understand, set up, and extend this powerful API that handles multiple notification channels including email, SMS, and push notifications.

## 📋 Table of Contents

- [🎯 What is Notify Service API?](#what-is-notify-service-api)
- [✨ Key Features](#key-features)
- [🏗️ Architecture Overview](#architecture-overview)
- [🚀 Quick Start Guide](#quick-start-guide)
- [⚙️ Configuration Guide](#configuration-guide)
- [🔧 Development Setup](#development-setup)
- [📚 API Documentation](#api-documentation)
- [🧪 Testing](#testing)
- [🚢 Deployment](#deployment)
- [🔄 Recent Updates](#recent-updates)

## 🎯 What is Notify Service API?

The Notify Service API is a **enterprise-grade notification microservice** designed to handle all your application's communication needs. Whether you need to send welcome emails, SMS alerts, push notifications, or track user login activities, this service provides a unified, reliable, and scalable solution.

### Why Choose This API?
- **🔒 Secure**: Built-in JWT authentication and authorization
- **⚡ Fast**: Redis caching for optimal performance
- **📊 Reliable**: PostgreSQL for data persistence and integrity
- **🔍 Observable**: Comprehensive logging and health monitoring
- **🧩 Modular**: Clean architecture with separation of concerns
- **📈 Scalable**: Designed for microservices architecture

## ⚙️ Enhanced Development Experience

### **🎛️ Interactive Configuration Manager**

Our advanced configuration system makes setup effortless:

```powershell
.\scripts\config-manager.ps1
```

**Configuration Categories:**
- 🗄️ **Database** - PostgreSQL connection strings and settings
- 🔴 **Redis** - Cache configuration with optional authentication
- 📧 **Email** - SMTP settings for Gmail, SendGrid, or custom providers
- 🔐 **Security** - JWT keys, authentication, and security settings
- 🐳 **Docker** - Container registry, ports, and image configuration
- ☁️ **Azure** - Deployment settings for Azure App Service
- 📊 **Monitoring** - Slack webhooks, Application Insights, logging

**Features:**
- ✅ **Smart defaults** - Works out of the box
- 🔐 **Secure input** - Masked password entry
- 💾 **Export/Import** - Save and share configurations
- 🎯 **Environment-specific** - Development, staging, production
- 📋 **Validation** - Ensures all settings are correct

### **🚀 Automated Environment Setup**

Beautiful, comprehensive development environment automation:

```powershell
.\scripts\start-dev-services.ps1  # Start everything
.\scripts\stop-dev-services.ps1   # Stop services
.\scripts\dev-menu.ps1            # Interactive menu
```

**What it handles:**
- 🐳 **Docker containers** - PostgreSQL, Redis with health checks
- 🗄️ **Database creation** - Automatic database and schema setup
- 🔧 **Migrations** - Auto-detection and application of EF migrations
- 🧪 **Connection testing** - Verifies all services are working
- 📊 **Status monitoring** - Real-time progress with colored output
- 🛡️ **Error handling** - Graceful failure recovery

## ✨ Key Features

### 🔐 **Authentication & Security**
- JWT-based authentication
- Role-based authorization
- Secure password handling
- Email confirmation workflows

### 📧 **Multi-Channel Notifications**
- **Email**: Rich HTML emails with templates
- **SMS**: Bulk SMS with delivery tracking
- **Push Notifications**: Mobile and web push notifications
- **Scheduling**: Schedule notifications for future delivery

### 📊 **Data Management**
- **PostgreSQL**: Primary database with Entity Framework Core
- **Redis**: Distributed caching for performance
- **Audit Logging**: Track user activities and login history

### 🔍 **Monitoring & Health**
- Built-in health checks for all dependencies
- Structured logging with Serilog
- Performance monitoring endpoints

### 🛠️ **Developer Experience**
- **Swagger UI**: Interactive API documentation
- **GraphQL**: Flexible query capabilities
- **Clean Architecture**: Easy to understand and extend
- **Unit Tests**: Comprehensive test coverage

## 🏗️ Architecture Overview

The Notify Service API follows **Clean Architecture** principles, ensuring maintainability, testability, and scalability.

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │   WebApi    │  │   GraphQL   │  │    Swagger UI       │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │  Services   │  │    Core     │  │     Identity        │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  Infrastructure Layer                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │    Data     │  │   Caching   │  │      Models         │ │
│  │(PostgreSQL) │  │   (Redis)   │  │                     │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 📁 **Project Structure**

#### **Libraries** (Core Business Logic)
- **`Models`** - Entity definitions, DTOs, and data contracts
- **`Data`** - Entity Framework Core context, repositories, and migrations
- **`Services`** - Business logic and service implementations
- **`Core`** - Shared services, extensions, and utilities
- **`Identity`** - Authentication, authorization, and user management
- **`Caching`** - Redis caching implementation and strategies

#### **Presentations** (API Layer)
- **`WebApi`** - REST API controllers, middleware, and configuration
- **`GraphQL`** - GraphQL queries, mutations, and schema definitions

#### **Tests** (Quality Assurance)
- **`UnitTests`** - Comprehensive unit test coverage

### 🗄️ **Database Design**

#### **PostgreSQL** (Primary Database)
- **Application Data**: User profiles, notifications, settings
- **Audit Logs**: Login history, activity tracking
- **Entity Framework Core**: Code-first migrations with automatic schema updates

#### **Redis** (Caching Layer)
- **Session Storage**: User sessions and temporary data
- **Performance Cache**: Frequently accessed data
- **Distributed Locking**: Coordination across multiple instances

## 🚀 Quick Start Guide

Get up and running in **2 minutes** with our enhanced development tools!

### **🎛️ Interactive Development Menu (Recommended)**

Launch our beautiful, interactive development environment manager:

```powershell
# One command to rule them all!
.\scripts\dev-menu.ps1
```

**What you get:**
- 🚀 **One-click setup** - Complete environment with Docker + migrations
- ⚙️ **Configuration manager** - Interactive setup for all settings
- 🔧 **Auto-migrations** - Automatic Entity Framework migration handling
- 🛑 **Service management** - Start, stop, restart, cleanup containers
- 📊 **Real-time status** - Live monitoring of all services
- 🌐 **Quick access** - Direct links to documentation and health checks

### **⚡ Super Quick Start (One Command!)**

```powershell
# 🚀 Complete setup from zero to running API
.\scripts\quick-start.ps1

# That's it! This single command will:
# ✅ Check dependencies (install if needed)
# ✅ Start Docker Desktop if not running
# ✅ Configure environment variables
# ✅ Start PostgreSQL and Redis
# ✅ Apply database migrations
# ✅ Launch the API
```

### **🎛️ Alternative Quick Starts**

```powershell
# 1. Interactive menu (recommended for first-time setup)
.\scripts\dev-menu.ps1

# 2. Manual step-by-step
.\scripts\config-manager.ps1    # Configure environment
.\scripts\start-dev-services.ps1 # Start services
dotnet run --project Presentations\WebApi # Run API

# 3. Cross-platform launcher
.\scripts\dev-setup.ps1 -Start  # Auto-detects your OS
```

### 📋 **Prerequisites**

Before you begin, ensure you have the following installed:

- **[.NET 8.0 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)** - Stable LTS version
- **[PostgreSQL 15+](https://www.postgresql.org/download/)** - Database server
- **[Redis 6+](https://redis.io/download)** - Caching server
- **[Git](https://git-scm.com/)** - Version control
- **IDE**: Visual Studio 2022, VS Code, or JetBrains Rider

### ⚡ **Quick Setup**

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd notify-service-api
   ```

2. **Start Dependencies with Docker** (Recommended)
   ```bash
   docker-compose up -d
   ```
   This starts PostgreSQL and Redis with default configurations.

3. **Configure the Application**
   ```bash
   cp appsettings.json appsettings.Development.json
   # Edit appsettings.Development.json with your settings
   ```

4. **Run Database Migrations**
   ```bash
   dotnet ef database update --project Libraries/Data
   ```

5. **Start the API**
   ```bash
   dotnet run --project Presentations/WebApi
   ```

6. **Verify Installation**
   - API: `https://localhost:5001/swagger`
   - Health: `https://localhost:5001/health`

## ⚙️ Configuration Guide

### 🔧 **Environment Configuration**

#### **appsettings.json Structure**
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=NotifyDb;Username=postgres;Password=yourpassword",
    "IdentityConnection": "Host=localhost;Database=NotifyIdentityDb;Username=postgres;Password=yourpassword"
  },
  "JWTSettings": {
    "Key": "your-super-secret-jwt-key-here-minimum-32-characters",
    "Issuer": "NotifyServiceAPI",
    "Audience": "NotifyServiceUsers",
    "DurationInMinutes": 60
  },
  "MailSettings": {
    "EmailFrom": "<EMAIL>",
    "SmtpHost": "smtp.gmail.com",
    "SmtpPort": 587,
    "SmtpUser": "<EMAIL>",
    "SmtpPass": "your-app-password",
    "DisplayName": "Notify Service"
  },
  "RedisSettings": {
    "RedisConnectionString": "localhost:6379",
    "CacheTime": "30",
    "RedisDatabaseId": "0"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    },
    "Seq": {
      "Url": "http://localhost:5341"
    }
  }
}
```

### 🔐 **Security Configuration**

#### **JWT Settings**
- **Key**: Must be at least 32 characters for HS256
- **Issuer**: Your application identifier
- **Audience**: Target audience for tokens
- **Duration**: Token expiration time in minutes

#### **Email Configuration**
- **Gmail**: Use App Passwords for authentication
- **SendGrid**: Use API key in SmtpPass field
- **Custom SMTP**: Configure your SMTP server details

### 🗄️ **Database Configuration**

#### **PostgreSQL Setup**
```sql
-- Create databases
CREATE DATABASE "NotifyDb";
CREATE DATABASE "NotifyIdentityDb";

-- Create user (optional)
CREATE USER notify_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE "NotifyDb" TO notify_user;
GRANT ALL PRIVILEGES ON DATABASE "NotifyIdentityDb" TO notify_user;
```

#### **Connection String Options**
```
Host=localhost;Port=5432;Database=NotifyDb;Username=postgres;Password=yourpassword;
Include Error Detail=true;Pooling=true;MinPoolSize=1;MaxPoolSize=20;
```

## 🔧 Development Setup

### 🛠️ **IDE Configuration**

#### **Visual Studio 2022**
1. Open `Notify.sln`
2. Set `WebApi` as startup project
3. Configure multiple startup projects if needed
4. Install recommended extensions:
   - Entity Framework Core Power Tools
   - GraphQL for .NET

#### **VS Code**
1. Install C# Dev Kit extension
2. Open workspace folder
3. Configure launch.json for debugging
4. Install recommended extensions:
   - C# Dev Kit
   - REST Client
   - Thunder Client

### 🧪 **Running Tests**
```bash
# Run all tests
dotnet test

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"

# Run specific test project
dotnet test Tests/UnitTests
```

### 🔍 **Debugging**

#### **Enable Detailed Logging**
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft.EntityFrameworkCore": "Information"
    }
  }
}
```

#### **Database Debugging**
```bash
# View pending migrations
dotnet ef migrations list --project Libraries/Data

# Generate SQL script
dotnet ef migrations script --project Libraries/Data

# Reset database (development only)
dotnet ef database drop --project Libraries/Data
dotnet ef database update --project Libraries/Data
```
