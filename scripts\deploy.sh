#!/bin/bash

# =========================
# Deployment Script for Notify Service API
# Supports local Docker and Azure deployment
# =========================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DOCKER_REGISTRY="notifyregistry.azurecr.io"
IMAGE_NAME="notify-service-api"
ENVIRONMENT="${1:-local}"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check .env file
    if [ ! -f "$PROJECT_ROOT/.env" ]; then
        log_warning ".env file not found. Creating from template..."
        cp "$PROJECT_ROOT/.env.template" "$PROJECT_ROOT/.env"
        log_warning "Please update .env file with your configuration"
    fi
    
    log_success "Prerequisites check completed"
}

build_image() {
    log_info "Building Docker image..."
    
    cd "$PROJECT_ROOT"
    
    # Build the image
    docker build \
        -f Dockerfile.production \
        -t "$IMAGE_NAME:latest" \
        -t "$IMAGE_NAME:$(date +%Y%m%d-%H%M%S)" \
        --build-arg BUILD_CONFIGURATION=Release \
        .
    
    log_success "Docker image built successfully"
}

deploy_local() {
    log_info "Deploying to local environment..."
    
    cd "$PROJECT_ROOT"
    
    # Stop existing containers
    docker-compose down
    
    # Build and start services
    docker-compose up -d --build
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 30
    
    # Check health
    if curl -f -s http://localhost:8080/health > /dev/null; then
        log_success "Local deployment completed successfully"
        log_info "API available at: http://localhost:8080"
        log_info "Scalar UI available at: http://localhost:8080/scalar"
        log_info "Health check: http://localhost:8080/health"
    else
        log_error "Health check failed"
        docker-compose logs api
        exit 1
    fi
}

deploy_azure() {
    log_info "Deploying to Azure..."
    
    # Check Azure CLI
    if ! command -v az &> /dev/null; then
        log_error "Azure CLI is not installed"
        exit 1
    fi
    
    # Login check
    if ! az account show &> /dev/null; then
        log_info "Please login to Azure..."
        az login
    fi
    
    # Load environment variables
    source "$PROJECT_ROOT/.env"
    
    # Tag image for Azure Container Registry
    docker tag "$IMAGE_NAME:latest" "$DOCKER_REGISTRY/$IMAGE_NAME:latest"
    docker tag "$IMAGE_NAME:latest" "$DOCKER_REGISTRY/$IMAGE_NAME:$(date +%Y%m%d-%H%M%S)"
    
    # Login to ACR
    az acr login --name "${DOCKER_REGISTRY%%.*}"
    
    # Push image
    log_info "Pushing image to Azure Container Registry..."
    docker push "$DOCKER_REGISTRY/$IMAGE_NAME:latest"
    docker push "$DOCKER_REGISTRY/$IMAGE_NAME:$(date +%Y%m%d-%H%M%S)"
    
    # Update Azure Web App
    log_info "Updating Azure Web App..."
    az webapp config container set \
        --name "$AZURE_WEBAPP_NAME" \
        --resource-group "$AZURE_RESOURCE_GROUP" \
        --docker-custom-image-name "$DOCKER_REGISTRY/$IMAGE_NAME:latest"
    
    # Restart the web app
    az webapp restart \
        --name "$AZURE_WEBAPP_NAME" \
        --resource-group "$AZURE_RESOURCE_GROUP"
    
    # Get the URL
    WEBAPP_URL=$(az webapp show \
        --name "$AZURE_WEBAPP_NAME" \
        --resource-group "$AZURE_RESOURCE_GROUP" \
        --query defaultHostName -o tsv)
    
    # Wait for deployment
    log_info "Waiting for deployment to complete..."
    sleep 60
    
    # Health check
    if curl -f -s "https://$WEBAPP_URL/health" > /dev/null; then
        log_success "Azure deployment completed successfully"
        log_info "API available at: https://$WEBAPP_URL"
        log_info "Scalar UI available at: https://$WEBAPP_URL/scalar"
    else
        log_error "Health check failed"
        exit 1
    fi
}

cleanup() {
    log_info "Cleaning up old Docker images..."
    
    # Remove old images (keep last 5)
    docker images "$IMAGE_NAME" --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}" | \
        tail -n +2 | sort -k2 -r | tail -n +6 | awk '{print $1}' | \
        xargs -r docker rmi
    
    log_success "Cleanup completed"
}

show_help() {
    echo "Usage: $0 [ENVIRONMENT]"
    echo ""
    echo "ENVIRONMENT:"
    echo "  local    Deploy to local Docker environment (default)"
    echo "  azure    Deploy to Azure Web App"
    echo "  help     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 local    # Deploy locally"
    echo "  $0 azure    # Deploy to Azure"
    echo ""
}

# Main execution
main() {
    case "$ENVIRONMENT" in
        "local")
            check_prerequisites
            build_image
            deploy_local
            cleanup
            ;;
        "azure")
            check_prerequisites
            build_image
            deploy_azure
            cleanup
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "Unknown environment: $ENVIRONMENT"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
