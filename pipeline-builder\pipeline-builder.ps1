# ═══════════════════════════════════════════════════════════════════════════════
# 🏗️ Universal Pipeline Builder - Enhanced with FTP/SFTP Support
# ═══════════════════════════════════════════════════════════════════════════════
# Comprehensive CI/CD pipeline generator for Azure DevOps and GitHub Actions
# Now includes FTP/SFTP deployment capabilities and SSH key management

param(
    [string]$Action = "menu",           # menu, analyze, build, validate, export, ftp, sftp
    [string]$Platform = "",             # azuredevops, github, both
    [string]$ProjectType = "auto",      # auto, dotnet, node, python, docker
    [string]$OutputPath = "",           # Output directory for generated files
    [switch]$Interactive,               # Interactive mode
    [switch]$Force,                     # Overwrite existing files
    [switch]$Validate,                  # Validate generated pipelines
    [switch]$Json,                      # JSON output
    [switch]$Verbose                    # Verbose logging
)

# Configuration
$ScriptRoot = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptRoot
$CoreModulesDir = Join-Path $ScriptRoot "core"
$ConfigDir = Join-Path $ScriptRoot "config"
$TemplatesDir = Join-Path $ScriptRoot "templates"
$OutputDir = if ($OutputPath) { $OutputPath } else { Join-Path $ScriptRoot "generated-pipelines" }
$BuilderConfigFile = Join-Path $ConfigDir "pipeline-builder-config.json"

# Ensure directories exist
@($CoreModulesDir, $ConfigDir, $TemplatesDir, $OutputDir) | ForEach-Object {
    if (-not (Test-Path $_)) {
        New-Item -ItemType Directory -Path $_ | Out-Null
    }
}

# Colors
$Colors = @{
    Red = "Red"; Green = "Green"; Yellow = "Yellow"; Blue = "Blue"
    Cyan = "Cyan"; Magenta = "Magenta"; Gray = "Gray"; White = "White"
}

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    if (-not $Json) { Write-Host $Text -ForegroundColor $Colors[$Color] }
}

function Write-BuilderLog {
    param([string]$Message, [string]$Level = "INFO")

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    if (-not $Json) {
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            default { "Cyan" }
        }
        Write-ColorText "[$timestamp] [$Level] $Message" $color
    }
}

function Show-Header {
    if ($Json) { return }

    Clear-Host
    Write-Host ""
    Write-ColorText "    🏗️🏗️🏗️ UNIVERSAL PIPELINE BUILDER 🏗️🏗️🏗️" "Cyan"
    Write-Host ""
    Write-ColorText "    🎯 Azure DevOps • GitHub Actions • FTP/SFTP" "Yellow"
    Write-ColorText "    🚀 Advanced Deployments • Monitoring • Security" "Gray"
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "Gray"
}

function Show-MainMenu {
    Write-Host ""
    Write-ColorText "🏗️ Universal Pipeline Builder - Main Menu" "Cyan"
    Write-Host ""
    Write-ColorText "    [1] 🔍 Analyze Repository" "Green"
    Write-ColorText "        └─ Detect project type, frameworks, and dependencies"
    Write-Host ""
    Write-ColorText "    [2] 🏗️ Build CI/CD Pipelines" "Blue"
    Write-ColorText "        └─ Generate Azure DevOps and GitHub Actions pipelines"
    Write-Host ""
    Write-ColorText "    [3] 🌐 FTP/SFTP Deployment" "Yellow"
    Write-ColorText "        └─ Configure and manage FTP/SFTP deployments"
    Write-Host ""
    Write-ColorText "    [4] 🚀 Advanced Features" "Magenta"
    Write-ColorText "        └─ Canary, Blue-Green, Chaos Engineering, Observability"
    Write-Host ""
    Write-ColorText "    [5] ⚙️ Configuration Management" "Cyan"
    Write-ColorText "        └─ Manage pipeline configurations and templates"
    Write-Host ""
    Write-ColorText "    [6] 📊 Pipeline Monitoring" "White"
    Write-ColorText "        └─ Monitor and analyze pipeline performance"
    Write-Host ""
    Write-ColorText "    [0] 🚪 Exit" "Gray"
    Write-Host ""
}

# Handle FTP/SFTP actions directly
if ($Action -eq "ftp" -or $Action -eq "sftp") {
    Show-Header
    Write-Host ""
    Write-ColorText "🌐 Launching FTP/SFTP Deployment Module..." "Green"

    $ftpScript = Join-Path $CoreModulesDir "ftp-sftp-deployment.ps1"
    if (Test-Path $ftpScript) {
        & $ftpScript -Action "menu"
    } else {
        Write-BuilderLog "FTP/SFTP deployment module not found" "ERROR"
    }
    exit
}

# Handle other actions by delegating to original pipeline builder
$OriginalPipelineBuilder = Join-Path $ProjectRoot "scripts/pipeline-builder.ps1"

if ($Action -ne "menu" -and (Test-Path $OriginalPipelineBuilder)) {
    Write-BuilderLog "Executing pipeline builder action: $Action" "INFO"

    # Prepare parameters for original builder
    $params = @{}
    if ($Action) { $params.Action = $Action }
    if ($Platform) { $params.Platform = $Platform }
    if ($ProjectType) { $params.ProjectType = $ProjectType }
    if ($OutputPath) { $params.OutputPath = $OutputPath }
    if ($Interactive) { $params.Interactive = $true }
    if ($Force) { $params.Force = $true }
    if ($Validate) { $params.Validate = $true }
    if ($Json) { $params.Json = $true }

    & $OriginalPipelineBuilder @params
    exit
}

# Main menu loop
Show-Header

do {
    Show-MainMenu
    $choice = Read-Host "Enter your choice (0-6)"

    switch ($choice) {
        "1" {
            Write-Host ""
            Write-ColorText "🔍 Repository Analysis" "Cyan"
            if (Test-Path $OriginalPipelineBuilder) {
                & $OriginalPipelineBuilder -Action "analyze" -Json:$Json
            } else {
                Write-BuilderLog "Pipeline builder not found" "ERROR"
            }
            Read-Host "Press Enter to continue"
        }
        "2" {
            Write-Host ""
            Write-ColorText "🏗️ Pipeline Generation" "Cyan"
            if (Test-Path $OriginalPipelineBuilder) {
                & $OriginalPipelineBuilder -Action "build" -Interactive
            } else {
                Write-BuilderLog "Pipeline builder not found" "ERROR"
            }
            Read-Host "Press Enter to continue"
        }
        "3" {
            Write-Host ""
            Write-ColorText "🌐 FTP/SFTP Deployment" "Cyan"
            $ftpScript = Join-Path $CoreModulesDir "ftp-sftp-deployment.ps1"
            if (Test-Path $ftpScript) {
                & $ftpScript -Action "menu"
            } else {
                Write-BuilderLog "FTP/SFTP deployment module not found" "ERROR"
                Read-Host "Press Enter to continue"
            }
        }
        "4" {
            Write-Host ""
            Write-ColorText "🚀 Advanced Features" "Cyan"
            $advancedScript = Join-Path $ProjectRoot "scripts/advanced-pipeline-features.ps1"
            if (Test-Path $advancedScript) {
                & $advancedScript -Feature "menu"
            } else {
                Write-BuilderLog "Advanced features module not found" "ERROR"
                Read-Host "Press Enter to continue"
            }
        }
        "5" {
            Write-Host ""
            Write-ColorText "⚙️ Configuration Management" "Cyan"
            Write-ColorText "Configuration files location:" "Yellow"
            Write-ColorText "  • Pipeline Config: $BuilderConfigFile" "White"
            Write-ColorText "  • Templates: $TemplatesDir" "White"
            Write-ColorText "  • SSH Keys: $(Join-Path $ScriptRoot 'ssh-keys')" "White"
            Write-ColorText "  • Generated Pipelines: $OutputDir" "White"
            Read-Host "Press Enter to continue"
        }
        "6" {
            Write-Host ""
            Write-ColorText "📊 Pipeline Monitoring" "Cyan"
            $monitorScript = Join-Path $ProjectRoot "scripts/pipeline-monitor.ps1"
            if (Test-Path $monitorScript) {
                & $monitorScript
            } else {
                Write-BuilderLog "Pipeline monitoring module not found" "ERROR"
                Read-Host "Press Enter to continue"
            }
        }
        "0" {
            Write-BuilderLog "Thank you for using Universal Pipeline Builder!" "SUCCESS"
            break
        }
        default {
            Write-BuilderLog "Invalid choice. Please try again." "WARN"
            Read-Host "Press Enter to continue"
        }
    }
} while ($choice -ne "0")
