{"sourceFile": "Libraries/Data.Mongo/Data.Mongo.csproj", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1751296538900, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751296740697, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +1,5 @@\n-﻿<Project Sdk=\"Microsoft.NET.Sdk\">\n+<Project Sdk=\"Microsoft.NET.Sdk\">\n   <PropertyGroup>\n     <TargetFramework>net9.0</TargetFramework>\n   </PropertyGroup>\n   <ItemGroup>\n"}, {"date": 1751296820253, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +1,5 @@\n-﻿<Project Sdk=\"Microsoft.NET.Sdk\">\n+<Project Sdk=\"Microsoft.NET.Sdk\">\n   <PropertyGroup>\n     <TargetFramework>net9.0</TargetFramework>\n   </PropertyGroup>\n   <ItemGroup>\n"}], "date": 1751296538900, "name": "Commit-0", "content": "<Project Sdk=\"Microsoft.NET.Sdk\">\n  <PropertyGroup>\n    <TargetFramework>net9.0</TargetFramework>\n  </PropertyGroup>\n  <ItemGroup>\n    <PackageReference Include=\"MongoDB.Driver\" Version=\"2.19.0\" />\n    <PackageReference Include=\"Microsoft.AspNetCore.SpaServices.Extensions\" Version=\"9.0.6\" />\n    <PackageReference Include=\"Newtonsoft.Json\" Version=\"13.0.3\" />\n  </ItemGroup>\n</Project>\n"}]}