using Caching;
using Core;
using GraphiQl;
using HealthChecks.UI.Client;
using Identity;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

using Scalar.AspNetCore;
using Serilog;
using Services.Interfaces;
using WebApi.Extensions;
using WebApi.Helpers;
using WebApi.Services;

namespace WebApi;

public class Startup
{
    public Startup(IConfiguration configuration)
    {
        Configuration = configuration;
    }

    public IConfiguration Configuration { get; }

    // This method gets called by the runtime. Use this method to add services to the container.
    public void ConfigureServices(IServiceCollection services)
    {
        //   services.AddMongo(Configuration);
        //   services.AddPostgres(Configuration)      
        services.AddLogging(o => o.AddSerilog());
        services.AddRepoServices(Configuration); // Add repository services
        services.AddIdentity(Configuration);
        services.AddSharedServices(Configuration);
        services.AddApplicationPostgreSQL(Configuration); // Ensure PostgreSQL is initialized
        services.AddRepoServices(Configuration);
        services.AddRedis(Configuration);
        services.AddScoped<IAuthenticatedUserService, AuthenticatedUserService>();
        services.AddAppServices(Configuration); // Add application services
        services.AddAutoMapper(typeof(MappingProfiles));
        services.AddCustomSwagger(Configuration);

        services.AddControllers();
        //.AddNewtonsoftJson(options =>
        //options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore
        //);

        services.AddHealthChecks()

            .AddRedis(Configuration.GetSection("RedisSettings:RedisConnectionString").Value,
                name: "RedisHealt-check",
                failureStatus: HealthStatus.Unhealthy,
                tags: new string[] { "api", "Redis" })

            .AddSqlServer(Configuration.GetConnectionString("IdentityConnection"),
                name: "identityDb-check",
                failureStatus: HealthStatus.Unhealthy,
                tags: new string[] { "api", "SqlDb" })

            .AddSqlServer(Configuration.GetConnectionString("DefaultConnection"),
                name: "applicationDb-check",
                failureStatus: HealthStatus.Unhealthy,
                tags: new string[] { "api", "SqlDb" });


        services.AddAuthorization(options =>
        {
            options.AddPolicy("OnlyAdmins", policy => policy.RequireRole("SuperAdmin", "Admin"));
        });

    }

    // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILoggerFactory loggerFactory)
    {
        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        //app.UseSerilogRequestLogging();
        loggerFactory.AddSerilog();

        app.UseHttpsRedirection();

        app.UseRouting();
        app.UseGraphiQl();
        app.UseAuthentication();
        app.UseAuthorization();

        //error middleware
        app.UseErrorHandlingMiddleware();

        app.UseSwagger(options =>
        {
            options.RouteTemplate = "/openapi/{documentName}.json";
        });

        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
            endpoints.MapScalarApiReference(options =>
            {
                options
                    .WithTitle("Notify Service API")
                    .WithTheme(ScalarTheme.Purple)
                    .WithSidebar(true)
                    .WithDarkMode(false)
                    .WithDefaultHttpClient(ScalarTarget.CSharp, ScalarClient.HttpClient);
            });
            endpoints.MapHealthChecks("/health", new HealthCheckOptions()
            {
                Predicate = _ => true,
                ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
            });
        });
    }
}
