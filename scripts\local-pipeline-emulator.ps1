# ═══════════════════════════════════════════════════════════════════════════════
# 🏗️ Notify Service API - Local Pipeline Emulator
# ═══════════════════════════════════════════════════════════════════════════════
# Emulates Azure DevOps pipeline steps locally for testing and development

param(
    [string]$Stage = "all",             # all, build, test, docker, deploy
    [string]$Environment = "dev",       # dev, staging, prod
    [string]$Configuration = "Debug",   # Debug, Release
    [switch]$SkipTests,                 # Skip test execution
    [switch]$SkipDocker,                # Skip Docker build
    [switch]$DryRun,                    # Dry run mode
    [switch]$Verbose,                   # Verbose output
    [switch]$Interactive,               # Interactive mode with prompts
    [switch]$Json                       # JSON output
)

# Configuration
$ProjectRoot = Split-Path -Parent (Split-Path -Parent $MyInvocation.MyCommand.Path)
$PipelineConfigFile = Join-Path $ProjectRoot "local-pipeline-config.json"
$LogsDir = Join-Path $ProjectRoot "logs"
$ArtifactsDir = Join-Path $ProjectRoot "artifacts"

# Ensure directories exist
@($LogsDir, $ArtifactsDir) | ForEach-Object {
    if (-not (Test-Path $_)) {
        New-Item -ItemType Directory -Path $_ | Out-Null
    }
}

# Pipeline state
$PipelineState = @{
    "StartTime" = Get-Date
    "CurrentStage" = ""
    "CurrentStep" = ""
    "StagesCompleted" = @()
    "StepResults" = @{}
    "Artifacts" = @{}
    "Variables" = @{}
    "Errors" = @()
}

# Colors
$Colors = @{
    Red = "Red"; Green = "Green"; Yellow = "Yellow"; Blue = "Blue"
    Cyan = "Cyan"; Magenta = "Magenta"; Gray = "Gray"; White = "White"
}

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    if (-not $Json) { Write-Host $Text -ForegroundColor $Colors[$Color] }
}

function Write-PipelineLog {
    param([string]$Message, [string]$Level = "INFO", [string]$Stage = "", [string]$Step = "")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level]"
    
    if ($Stage) { $logEntry += " [$Stage]" }
    if ($Step) { $logEntry += " [$Step]" }
    
    $logEntry += " $Message"
    
    # Write to console
    if (-not $Json) {
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            "STEP" { "Cyan" }
            "STAGE" { "Magenta" }
            default { "White" }
        }
        Write-ColorText $logEntry $color
    }
    
    # Write to log file
    $logFile = Join-Path $LogsDir "pipeline-emulator-$(Get-Date -Format 'yyyy-MM-dd').log"
    Add-Content -Path $logFile -Value $logEntry
    
    # Update pipeline state
    if ($Level -eq "ERROR") {
        $PipelineState.Errors += @{
            "timestamp" = $timestamp
            "stage" = $Stage
            "step" = $Step
            "message" = $Message
        }
    }
}

function Show-Header {
    if ($Json) { return }
    
    Clear-Host
    Write-Host ""
    Write-ColorText "    🏗️🏗️🏗️ LOCAL PIPELINE EMULATOR 🏗️🏗️🏗️" "Cyan"
    Write-Host ""
    Write-ColorText "    🔄 Azure DevOps Pipeline Simulation" "Yellow"
    Write-ColorText "    🧪 Build • Test • Docker • Deploy Locally" "Gray"
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "Gray"
}

function Initialize-PipelineConfig {
    Write-PipelineLog "Initializing pipeline configuration..." "INFO"
    
    if (-not (Test-Path $PipelineConfigFile)) {
        $defaultConfig = @{
            "variables" = @{
                "buildConfiguration" = "Release"
                "dotNetFramework" = "net8.0"
                "dotNetVersion" = "8.0.x"
                "imageRepository" = "notify-service-api"
                "containerRegistry" = "localhost:5000"
                "tag" = "local-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
            }
            "stages" = @{
                "build" = @{
                    "displayName" = "Build and Test"
                    "enabled" = $true
                    "steps" = @(
                        @{ "name" = "restore"; "displayName" = "Restore NuGet packages"; "command" = "dotnet restore" }
                        @{ "name" = "build"; "displayName" = "Build solution"; "command" = "dotnet build --configuration `$(buildConfiguration) --no-restore" }
                        @{ "name" = "test"; "displayName" = "Run unit tests"; "command" = "dotnet test --configuration `$(buildConfiguration) --no-build --collect:`"XPlat Code Coverage`"" }
                        @{ "name" = "publish"; "displayName" = "Publish application"; "command" = "dotnet publish Presentations/WebApi/WebApi.csproj --configuration `$(buildConfiguration) --output artifacts/app --no-build" }
                    )
                }
                "docker" = @{
                    "displayName" = "Build Docker Image"
                    "enabled" = $true
                    "dependsOn" = @("build")
                    "steps" = @(
                        @{ "name" = "docker-build"; "displayName" = "Build Docker image"; "command" = "docker build -t `$(imageRepository):`$(tag) ." }
                        @{ "name" = "docker-tag"; "displayName" = "Tag Docker image"; "command" = "docker tag `$(imageRepository):`$(tag) `$(imageRepository):latest" }
                    )
                }
                "deploy" = @{
                    "displayName" = "Deploy to Local Environment"
                    "enabled" = $false
                    "dependsOn" = @("docker")
                    "steps" = @(
                        @{ "name" = "deploy-local"; "displayName" = "Deploy to local environment"; "command" = "docker-compose up -d" }
                        @{ "name" = "health-check"; "displayName" = "Health check"; "command" = "curl -f http://localhost:5000/health" }
                    )
                }
            }
            "artifacts" = @{
                "app" = @{
                    "path" = "artifacts/app"
                    "include" = @("**/*")
                }
                "tests" = @{
                    "path" = "TestResults"
                    "include" = @("**/*.xml", "**/*.json")
                }
            }
        }
        
        $defaultConfig | ConvertTo-Json -Depth 10 | Set-Content -Path $PipelineConfigFile -Encoding UTF8
        Write-PipelineLog "Default pipeline configuration created" "SUCCESS"
    }
    
    return Get-Content $PipelineConfigFile | ConvertFrom-Json
}

function Expand-Variables {
    param([string]$Text, [hashtable]$Variables)
    
    $result = $Text
    foreach ($key in $Variables.Keys) {
        $result = $result -replace "\`$\($key\)", $Variables[$key]
    }
    return $result
}

function Invoke-PipelineStep {
    param(
        [object]$Step,
        [hashtable]$Variables,
        [string]$StageName
    )
    
    $PipelineState.CurrentStep = $Step.name
    Write-PipelineLog "Starting step: $($Step.displayName)" "STEP" $StageName $Step.name
    
    if ($DryRun) {
        Write-PipelineLog "DRY RUN: Would execute: $($Step.command)" "INFO" $StageName $Step.name
        $PipelineState.StepResults[$Step.name] = @{
            "status" = "skipped"
            "duration" = 0
            "output" = "Dry run - not executed"
        }
        return $true
    }
    
    $stepStartTime = Get-Date
    
    try {
        # Expand variables in command
        $expandedCommand = Expand-Variables $Step.command $Variables
        
        if ($Verbose) {
            Write-PipelineLog "Executing: $expandedCommand" "INFO" $StageName $Step.name
        }
        
        # Execute command
        $output = ""
        if ($Interactive) {
            Write-ColorText "About to execute: $expandedCommand" "Yellow"
            $continue = Read-Host "Continue? (Y/n)"
            if ($continue -eq 'n' -or $continue -eq 'N') {
                Write-PipelineLog "Step skipped by user" "WARN" $StageName $Step.name
                return $false
            }
        }
        
        # Change to project root for execution
        Push-Location $ProjectRoot
        
        try {
            $output = Invoke-Expression $expandedCommand 2>&1
            $exitCode = $LASTEXITCODE
        } finally {
            Pop-Location
        }
        
        $duration = (Get-Date) - $stepStartTime
        
        if ($exitCode -eq 0) {
            Write-PipelineLog "Step completed successfully" "SUCCESS" $StageName $Step.name
            $PipelineState.StepResults[$Step.name] = @{
                "status" = "succeeded"
                "duration" = $duration.TotalSeconds
                "output" = $output -join "`n"
                "exitCode" = $exitCode
            }
            return $true
        } else {
            Write-PipelineLog "Step failed with exit code: $exitCode" "ERROR" $StageName $Step.name
            if ($Verbose -and $output) {
                Write-PipelineLog "Output: $($output -join "`n")" "ERROR" $StageName $Step.name
            }
            $PipelineState.StepResults[$Step.name] = @{
                "status" = "failed"
                "duration" = $duration.TotalSeconds
                "output" = $output -join "`n"
                "exitCode" = $exitCode
            }
            return $false
        }
    } catch {
        $duration = (Get-Date) - $stepStartTime
        Write-PipelineLog "Step failed with exception: $($_.Exception.Message)" "ERROR" $StageName $Step.name
        $PipelineState.StepResults[$Step.name] = @{
            "status" = "failed"
            "duration" = $duration.TotalSeconds
            "output" = $_.Exception.Message
            "exception" = $_.Exception.ToString()
        }
        return $false
    }
}

function Invoke-PipelineStage {
    param(
        [string]$StageName,
        [object]$StageConfig,
        [hashtable]$Variables
    )
    
    $PipelineState.CurrentStage = $StageName
    Write-PipelineLog "Starting stage: $($StageConfig.displayName)" "STAGE" $StageName
    
    if (-not $StageConfig.enabled) {
        Write-PipelineLog "Stage is disabled, skipping" "WARN" $StageName
        return $true
    }
    
    # Check dependencies
    if ($StageConfig.dependsOn) {
        foreach ($dependency in $StageConfig.dependsOn) {
            if ($dependency -notin $PipelineState.StagesCompleted) {
                Write-PipelineLog "Dependency '$dependency' not completed, skipping stage" "ERROR" $StageName
                return $false
            }
        }
    }
    
    $stageStartTime = Get-Date
    $allStepsSucceeded = $true
    
    foreach ($step in $StageConfig.steps) {
        $stepResult = Invoke-PipelineStep -Step $step -Variables $Variables -StageName $StageName
        
        if (-not $stepResult) {
            $allStepsSucceeded = $false
            Write-PipelineLog "Stage failed due to step failure" "ERROR" $StageName
            break
        }
    }
    
    $stageDuration = (Get-Date) - $stageStartTime
    
    if ($allStepsSucceeded) {
        Write-PipelineLog "Stage completed successfully in $($stageDuration.TotalSeconds) seconds" "SUCCESS" $StageName
        $PipelineState.StagesCompleted += $StageName
        return $true
    } else {
        Write-PipelineLog "Stage failed after $($stageDuration.TotalSeconds) seconds" "ERROR" $StageName
        return $false
    }
}

function Start-LocalPipeline {
    param(
        [object]$Config,
        [string]$TargetStage = "all"
    )
    
    Write-PipelineLog "Starting local pipeline emulation..." "INFO"
    
    # Prepare variables
    $variables = @{}
    foreach ($key in $Config.variables.PSObject.Properties.Name) {
        $variables[$key] = $Config.variables.$key
    }
    
    # Add runtime variables
    $variables["buildId"] = Get-Date -Format "yyyyMMdd-HHmmss"
    $variables["buildNumber"] = "local-$($variables.buildId)"
    $variables["sourceBranch"] = try { git rev-parse --abbrev-ref HEAD } catch { "unknown" }
    $variables["sourceVersion"] = try { git rev-parse HEAD } catch { "unknown" }
    
    $PipelineState.Variables = $variables
    
    # Determine stages to run
    $stagesToRun = if ($TargetStage -eq "all") {
        $Config.stages.PSObject.Properties.Name
    } else {
        @($TargetStage)
    }
    
    Write-PipelineLog "Stages to run: $($stagesToRun -join ', ')" "INFO"
    
    $overallSuccess = $true
    
    foreach ($stageName in $stagesToRun) {
        $stageConfig = $Config.stages.$stageName
        
        if (-not $stageConfig) {
            Write-PipelineLog "Stage '$stageName' not found in configuration" "ERROR"
            $overallSuccess = $false
            continue
        }
        
        # Skip tests if requested
        if ($SkipTests -and $stageName -eq "build") {
            # Remove test step
            $stageConfig.steps = $stageConfig.steps | Where-Object { $_.name -ne "test" }
        }
        
        # Skip Docker if requested
        if ($SkipDocker -and $stageName -eq "docker") {
            Write-PipelineLog "Skipping Docker stage as requested" "WARN"
            continue
        }
        
        $stageResult = Invoke-PipelineStage -StageName $stageName -StageConfig $stageConfig -Variables $variables
        
        if (-not $stageResult) {
            $overallSuccess = $false
            Write-PipelineLog "Pipeline failed at stage: $stageName" "ERROR"
            break
        }
    }
    
    return $overallSuccess
}

function Show-PipelineResults {
    $duration = (Get-Date) - $PipelineState.StartTime
    
    if ($Json) {
        $result = @{
            "status" = if ($PipelineState.Errors.Count -eq 0) { "succeeded" } else { "failed" }
            "duration" = $duration.TotalSeconds
            "stagesCompleted" = $PipelineState.StagesCompleted
            "stepResults" = $PipelineState.StepResults
            "errors" = $PipelineState.Errors
            "variables" = $PipelineState.Variables
        }
        $result | ConvertTo-Json -Depth 10
        return
    }
    
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "Gray"
    Write-ColorText "📊 Pipeline Execution Summary" "White"
    Write-Host ""
    
    $successCount = ($PipelineState.StepResults.Values | Where-Object { $_.status -eq "succeeded" }).Count
    $failedCount = ($PipelineState.StepResults.Values | Where-Object { $_.status -eq "failed" }).Count
    $skippedCount = ($PipelineState.StepResults.Values | Where-Object { $_.status -eq "skipped" }).Count
    
    Write-ColorText "✅ Succeeded: $successCount" "Green"
    Write-ColorText "❌ Failed: $failedCount" "Red"
    Write-ColorText "⏭️ Skipped: $skippedCount" "Yellow"
    Write-ColorText "⏱️ Total Duration: $($duration.ToString('hh\:mm\:ss'))" "Cyan"
    
    if ($PipelineState.Errors.Count -gt 0) {
        Write-Host ""
        Write-ColorText "❌ Errors:" "Red"
        foreach ($error in $PipelineState.Errors) {
            Write-ColorText "   • [$($error.stage)] $($error.message)" "Red"
        }
    }
    
    Write-Host ""
    if ($PipelineState.Errors.Count -eq 0) {
        Write-ColorText "🎉 Pipeline completed successfully!" "Green"
    } else {
        Write-ColorText "💥 Pipeline failed with errors." "Red"
    }
    Write-Host ""
}

# Main execution
Show-Header

$config = Initialize-PipelineConfig

Write-PipelineLog "Local Pipeline Emulator started" "INFO"
Write-PipelineLog "Target Stage: $Stage" "INFO"
Write-PipelineLog "Environment: $Environment" "INFO"
Write-PipelineLog "Configuration: $Configuration" "INFO"

$success = Start-LocalPipeline -Config $config -TargetStage $Stage

Show-PipelineResults

if (-not $success) {
    exit 1
}
