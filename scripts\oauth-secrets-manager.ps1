# ═══════════════════════════════════════════════════════════════════════════════
# 🔐 Notify Service API - OAuth & Secrets Management System
# ═══════════════════════════════════════════════════════════════════════════════
# Secure OAuth integration with Azure DevOps, Azure Key Vault, and other services

param(
    [string]$Action = "menu",           # menu, setup, get, set, list, rotate, validate
    [string]$Provider = "",             # azuredevops, azurekeyvault, github, slack
    [string]$SecretName = "",           # Name of the secret
    [string]$SecretValue = "",          # Value of the secret
    [string]$Environment = "dev",       # dev, staging, prod
    [switch]$Force,                     # Force overwrite
    [switch]$Rotate,                    # Rotate existing secret
    [switch]$Json                       # JSON output
)

# Configuration
$ProjectRoot = Split-Path -Parent (Split-Path -Parent $MyInvocation.MyCommand.Path)
$SecretsConfigFile = Join-Path $ProjectRoot "oauth-config.json"
$LocalSecretsFile = Join-Path $ProjectRoot "local-secrets.json"
$LogsDir = Join-Path $ProjectRoot "logs"

# Ensure logs directory exists
if (-not (Test-Path $LogsDir)) {
    New-Item -ItemType Directory -Path $LogsDir | Out-Null
}

# Colors
$Colors = @{
    Red = "Red"; Green = "Green"; Yellow = "Yellow"; Blue = "Blue"
    Cyan = "Cyan"; Magenta = "Magenta"; Gray = "Gray"; White = "White"
}

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    if (-not $Json) { Write-Host $Text -ForegroundColor $Colors[$Color] }
}

function Write-SecureLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    # Sanitize message to avoid logging secrets
    $sanitizedMessage = $Message -replace '(token|password|secret|key)=[^&\s]+', '$1=***'
    $logEntry = "[$timestamp] [$Level] $sanitizedMessage"
    
    $logFile = Join-Path $LogsDir "oauth-$(Get-Date -Format 'yyyy-MM-dd').log"
    Add-Content -Path $logFile -Value $logEntry
    
    if (-not $Json) {
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            default { "White" }
        }
        Write-ColorText $logEntry $color
    }
}

function Show-Header {
    if ($Json) { return }
    
    Clear-Host
    Write-Host ""
    Write-ColorText "    🔐🔐🔐 OAUTH & SECRETS MANAGEMENT SYSTEM 🔐🔐🔐" "Cyan"
    Write-Host ""
    Write-ColorText "    🛡️ Azure DevOps • Key Vault • GitHub • Secure Storage" "Yellow"
    Write-ColorText "    🔑 OAuth • Tokens • Certificates • Rotation" "Gray"
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "Gray"
}

function Initialize-OAuthConfiguration {
    Write-SecureLog "Initializing OAuth configuration..." "INFO"
    
    if (-not (Test-Path $SecretsConfigFile)) {
        $defaultConfig = @{
            "providers" = @{
                "azuredevops" = @{
                    "name" = "Azure DevOps"
                    "authUrl" = "https://app.vssps.visualstudio.com/oauth2/authorize"
                    "tokenUrl" = "https://app.vssps.visualstudio.com/oauth2/token"
                    "scope" = "vso.build vso.release vso.code"
                    "clientId" = ""
                    "clientSecret" = ""
                    "redirectUri" = "http://localhost:8080/callback"
                }
                "azurekeyvault" = @{
                    "name" = "Azure Key Vault"
                    "tenantId" = ""
                    "clientId" = ""
                    "clientSecret" = ""
                    "vaultUrl" = ""
                }
                "github" = @{
                    "name" = "GitHub"
                    "authUrl" = "https://github.com/login/oauth/authorize"
                    "tokenUrl" = "https://github.com/login/oauth/access_token"
                    "scope" = "repo workflow"
                    "clientId" = ""
                    "clientSecret" = ""
                }
                "slack" = @{
                    "name" = "Slack"
                    "webhookUrl" = ""
                    "botToken" = ""
                    "appToken" = ""
                }
            }
            "storage" = @{
                "type" = "local"  # local, azurekeyvault, hashicorpvault
                "encryption" = @{
                    "enabled" = $true
                    "algorithm" = "AES256"
                }
            }
            "security" = @{
                "tokenRotationDays" = 90
                "requireMFA" = $false
                "auditLogging" = $true
            }
        }
        
        $defaultConfig | ConvertTo-Json -Depth 10 | Set-Content -Path $SecretsConfigFile -Encoding UTF8
        Write-SecureLog "Default OAuth configuration created" "SUCCESS"
    }
    
    return Get-Content $SecretsConfigFile | ConvertFrom-Json
}

function Get-EncryptionKey {
    $keyFile = Join-Path $ProjectRoot ".encryption-key"
    
    if (-not (Test-Path $keyFile)) {
        # Generate new encryption key
        $key = New-Object byte[] 32
        [System.Security.Cryptography.RNGCryptoServiceProvider]::Create().GetBytes($key)
        $keyBase64 = [Convert]::ToBase64String($key)
        
        # Save encrypted key (in real implementation, use Windows DPAPI or similar)
        $keyBase64 | ConvertTo-SecureString -AsPlainText -Force | ConvertFrom-SecureString | Set-Content $keyFile
        Write-SecureLog "New encryption key generated" "INFO"
    }
    
    $encryptedKey = Get-Content $keyFile | ConvertTo-SecureString
    $keyPtr = [Runtime.InteropServices.Marshal]::SecureStringToBSTR($encryptedKey)
    $keyBase64 = [Runtime.InteropServices.Marshal]::PtrToStringAuto($keyPtr)
    
    return [Convert]::FromBase64String($keyBase64)
}

function Protect-Secret {
    param([string]$PlainText)
    
    try {
        $key = Get-EncryptionKey
        $bytes = [Text.Encoding]::UTF8.GetBytes($PlainText)
        
        $aes = [System.Security.Cryptography.Aes]::Create()
        $aes.Key = $key
        $aes.GenerateIV()
        
        $encryptor = $aes.CreateEncryptor()
        $encryptedBytes = $encryptor.TransformFinalBlock($bytes, 0, $bytes.Length)
        
        $result = @{
            "data" = [Convert]::ToBase64String($encryptedBytes)
            "iv" = [Convert]::ToBase64String($aes.IV)
        }
        
        return $result | ConvertTo-Json -Compress
    } catch {
        Write-SecureLog "Failed to encrypt secret: $($_.Exception.Message)" "ERROR"
        throw
    }
}

function Unprotect-Secret {
    param([string]$EncryptedData)
    
    try {
        $key = Get-EncryptionKey
        $data = $EncryptedData | ConvertFrom-Json
        
        $encryptedBytes = [Convert]::FromBase64String($data.data)
        $iv = [Convert]::FromBase64String($data.iv)
        
        $aes = [System.Security.Cryptography.Aes]::Create()
        $aes.Key = $key
        $aes.IV = $iv
        
        $decryptor = $aes.CreateDecryptor()
        $decryptedBytes = $decryptor.TransformFinalBlock($encryptedBytes, 0, $encryptedBytes.Length)
        
        return [Text.Encoding]::UTF8.GetString($decryptedBytes)
    } catch {
        Write-SecureLog "Failed to decrypt secret: $($_.Exception.Message)" "ERROR"
        throw
    }
}

function Set-LocalSecret {
    param(
        [string]$Name,
        [string]$Value,
        [string]$Provider = "local",
        [string]$Environment = "dev"
    )
    
    Write-SecureLog "Setting secret: $Name for provider: $Provider" "INFO"
    
    $secrets = @{}
    if (Test-Path $LocalSecretsFile) {
        $secrets = Get-Content $LocalSecretsFile | ConvertFrom-Json -AsHashtable
    }
    
    if (-not $secrets[$Environment]) {
        $secrets[$Environment] = @{}
    }
    
    if (-not $secrets[$Environment][$Provider]) {
        $secrets[$Environment][$Provider] = @{}
    }
    
    # Encrypt the secret value
    $encryptedValue = Protect-Secret $Value
    $secrets[$Environment][$Provider][$Name] = @{
        "value" = $encryptedValue
        "created" = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
        "lastModified" = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
    }
    
    $secrets | ConvertTo-Json -Depth 10 | Set-Content -Path $LocalSecretsFile -Encoding UTF8
    Write-SecureLog "Secret '$Name' stored successfully" "SUCCESS"
}

function Get-LocalSecret {
    param(
        [string]$Name,
        [string]$Provider = "local",
        [string]$Environment = "dev"
    )
    
    if (-not (Test-Path $LocalSecretsFile)) {
        Write-SecureLog "No secrets file found" "WARN"
        return $null
    }
    
    $secrets = Get-Content $LocalSecretsFile | ConvertFrom-Json -AsHashtable
    
    if ($secrets[$Environment] -and $secrets[$Environment][$Provider] -and $secrets[$Environment][$Provider][$Name]) {
        $secretData = $secrets[$Environment][$Provider][$Name]
        $decryptedValue = Unprotect-Secret $secretData.value
        
        return @{
            "name" = $Name
            "value" = $decryptedValue
            "created" = $secretData.created
            "lastModified" = $secretData.lastModified
        }
    }
    
    return $null
}

function Start-OAuthFlow {
    param(
        [string]$Provider,
        [object]$Config
    )
    
    $providerConfig = $Config.providers[$Provider]
    if (-not $providerConfig) {
        Write-SecureLog "Provider '$Provider' not found in configuration" "ERROR"
        return
    }
    
    Write-SecureLog "Starting OAuth flow for $($providerConfig.name)..." "INFO"
    
    # Generate state parameter for security
    $state = [System.Web.Security.Membership]::GeneratePassword(32, 0)
    
    # Build authorization URL
    $authParams = @(
        "client_id=$($providerConfig.clientId)"
        "response_type=code"
        "scope=$($providerConfig.scope)"
        "state=$state"
        "redirect_uri=$([System.Web.HttpUtility]::UrlEncode($providerConfig.redirectUri))"
    )
    
    $authUrl = "$($providerConfig.authUrl)?$($authParams -join '&')"
    
    Write-ColorText "Opening browser for OAuth authorization..." "INFO"
    Write-ColorText "Authorization URL: $authUrl" "CYAN"
    
    # Open browser
    Start-Process $authUrl
    
    # Start local callback server
    Start-CallbackServer -Provider $Provider -Config $Config -State $state
}

function Start-CallbackServer {
    param(
        [string]$Provider,
        [object]$Config,
        [string]$State
    )
    
    Write-SecureLog "Starting local callback server on port 8080..." "INFO"
    
    $listener = New-Object System.Net.HttpListener
    $listener.Prefixes.Add("http://localhost:8080/")
    $listener.Start()
    
    Write-ColorText "Waiting for OAuth callback..." "INFO"
    
    try {
        $context = $listener.GetContext()
        $request = $context.Request
        $response = $context.Response
        
        # Parse query parameters
        $query = [System.Web.HttpUtility]::ParseQueryString($request.Url.Query)
        $code = $query["code"]
        $returnedState = $query["state"]
        
        if ($returnedState -ne $State) {
            Write-SecureLog "Invalid state parameter - possible CSRF attack" "ERROR"
            return
        }
        
        if ($code) {
            Write-SecureLog "Authorization code received, exchanging for token..." "INFO"
            
            # Exchange code for token
            $token = Get-AccessTokenFromCode -Provider $Provider -Config $Config -Code $code
            
            if ($token) {
                # Store the token securely
                Set-LocalSecret -Name "access_token" -Value $token.access_token -Provider $Provider -Environment "dev"
                if ($token.refresh_token) {
                    Set-LocalSecret -Name "refresh_token" -Value $token.refresh_token -Provider $Provider -Environment "dev"
                }
                
                $responseText = "<html><body><h1>✅ OAuth Success!</h1><p>You can close this window.</p></body></html>"
                Write-SecureLog "OAuth flow completed successfully" "SUCCESS"
            } else {
                $responseText = "<html><body><h1>❌ OAuth Failed!</h1><p>Failed to exchange code for token.</p></body></html>"
                Write-SecureLog "Failed to exchange authorization code for token" "ERROR"
            }
        } else {
            $error = $query["error"]
            $responseText = "<html><body><h1>❌ OAuth Error!</h1><p>Error: $error</p></body></html>"
            Write-SecureLog "OAuth error: $error" "ERROR"
        }
        
        # Send response
        $buffer = [Text.Encoding]::UTF8.GetBytes($responseText)
        $response.ContentLength64 = $buffer.Length
        $response.OutputStream.Write($buffer, 0, $buffer.Length)
        $response.Close()
        
    } finally {
        $listener.Stop()
    }
}

function Get-AccessTokenFromCode {
    param(
        [string]$Provider,
        [object]$Config,
        [string]$Code
    )
    
    $providerConfig = $Config.providers[$Provider]
    
    $tokenParams = @{
        "client_id" = $providerConfig.clientId
        "client_secret" = $providerConfig.clientSecret
        "code" = $Code
        "grant_type" = "authorization_code"
        "redirect_uri" = $providerConfig.redirectUri
    }
    
    try {
        $response = Invoke-RestMethod -Uri $providerConfig.tokenUrl -Method POST -Body $tokenParams -ContentType "application/x-www-form-urlencoded"
        return $response
    } catch {
        Write-SecureLog "Token exchange failed: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

function Show-SecretsMenu {
    Write-Host ""
    Write-ColorText "🔐 OAuth & Secrets Management Menu" "Cyan"
    Write-Host ""
    Write-ColorText "    [1] 🔑 Setup OAuth Provider" "Green"
    Write-ColorText "        └─ Configure OAuth for Azure DevOps, GitHub, etc."
    Write-Host ""
    Write-ColorText "    [2] 📝 Set Secret" "Blue"
    Write-ColorText "        └─ Store a secret securely"
    Write-Host ""
    Write-ColorText "    [3] 👁️ Get Secret" "Yellow"
    Write-ColorText "        └─ Retrieve a stored secret"
    Write-Host ""
    Write-ColorText "    [4] 📋 List Secrets" "Magenta"
    Write-ColorText "        └─ Show all stored secrets (names only)"
    Write-Host ""
    Write-ColorText "    [5] 🔄 Rotate Secrets" "Cyan"
    Write-ColorText "        └─ Rotate expiring tokens"
    Write-Host ""
    Write-ColorText "    [6] ✅ Validate Tokens" "Gray"
    Write-ColorText "        └─ Check token validity"
    Write-Host ""
    Write-ColorText "    [0] 🚪 Exit" "Gray"
    Write-Host ""
}

# Main execution
Show-Header

$config = Initialize-OAuthConfiguration

if ($Action -eq "menu") {
    do {
        Show-SecretsMenu
        $choice = Read-Host "Enter your choice (0-6)"
        
        switch ($choice) {
            "1" {
                Write-ColorText "Available providers: azuredevops, github, slack" "INFO"
                $provider = Read-Host "Enter provider name"
                if ($provider -and $config.providers[$provider]) {
                    Start-OAuthFlow -Provider $provider -Config $config
                } else {
                    Write-SecureLog "Invalid provider: $provider" "ERROR"
                }
                Read-Host "Press Enter to continue"
            }
            "2" {
                $provider = Read-Host "Enter provider name"
                $name = Read-Host "Enter secret name"
                $value = Read-Host "Enter secret value" -AsSecureString
                $valueText = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($value))
                
                Set-LocalSecret -Name $name -Value $valueText -Provider $provider -Environment $Environment
                Read-Host "Press Enter to continue"
            }
            "3" {
                $provider = Read-Host "Enter provider name"
                $name = Read-Host "Enter secret name"
                
                $secret = Get-LocalSecret -Name $name -Provider $provider -Environment $Environment
                if ($secret) {
                    Write-ColorText "Secret found (value hidden for security)" "SUCCESS"
                    Write-ColorText "Created: $($secret.created)" "INFO"
                    Write-ColorText "Last Modified: $($secret.lastModified)" "INFO"
                } else {
                    Write-SecureLog "Secret not found" "WARN"
                }
                Read-Host "Press Enter to continue"
            }
            "0" {
                Write-SecureLog "Goodbye!" "INFO"
                break
            }
            default {
                Write-SecureLog "Invalid choice. Please try again." "WARN"
                Read-Host "Press Enter to continue"
            }
        }
    } while ($choice -ne "0")
}
