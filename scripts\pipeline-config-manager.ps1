# ═══════════════════════════════════════════════════════════════════════════════
# ⚙️ Notify Service API - Pipeline Configuration Management
# ═══════════════════════════════════════════════════════════════════════════════
# Manage pipeline variables, environments, and configurations across different stages

param(
    [string]$Action = "menu",           # menu, set, get, list, export, import, sync
    [string]$Environment = "",          # dev, staging, prod, all
    [string]$VariableName = "",         # Variable name
    [string]$VariableValue = "",        # Variable value
    [string]$ConfigFile = "",           # Configuration file path
    [string]$ExportPath = "",           # Export destination
    [switch]$Secret,                    # Mark as secret variable
    [switch]$Force,                     # Force overwrite
    [switch]$Sync,                      # Sync with Azure DevOps
    [switch]$Json                       # JSON output
)

# Configuration
$ProjectRoot = Split-Path -Parent (Split-Path -Parent $MyInvocation.MyCommand.Path)
$PipelineVarsFile = Join-Path $ProjectRoot "pipeline-variables.json"
$EnvironmentConfigFile = Join-Path $ProjectRoot "environment-configs.json"
$LogsDir = Join-Path $ProjectRoot "logs"

# Ensure logs directory exists
if (-not (Test-Path $LogsDir)) {
    New-Item -ItemType Directory -Path $LogsDir | Out-Null
}

# Colors
$Colors = @{
    Red = "Red"; Green = "Green"; Yellow = "Yellow"; Blue = "Blue"
    Cyan = "Cyan"; Magenta = "Magenta"; Gray = "Gray"; White = "White"
}

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    if (-not $Json) { Write-Host $Text -ForegroundColor $Colors[$Color] }
}

function Write-ConfigLog {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    # Write to console
    if (-not $Json) {
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            default { "Cyan" }
        }
        Write-ColorText $logEntry $color
    }
    
    # Write to log file
    $logFile = Join-Path $LogsDir "pipeline-config-$(Get-Date -Format 'yyyy-MM-dd').log"
    Add-Content -Path $logFile -Value $logEntry
}

function Show-Header {
    if ($Json) { return }
    
    Clear-Host
    Write-Host ""
    Write-ColorText "    ⚙️⚙️⚙️ PIPELINE CONFIGURATION MANAGER ⚙️⚙️⚙️" "Cyan"
    Write-Host ""
    Write-ColorText "    🔧 Variables • Environments • Sync • Export" "Yellow"
    Write-ColorText "    📊 Azure DevOps Integration • Multi-Environment" "Gray"
    Write-Host ""
    Write-ColorText "═══════════════════════════════════════════════════════════════════════════════" "Gray"
}

function Initialize-ConfigurationFiles {
    Write-ConfigLog "Initializing configuration files..." "INFO"
    
    # Initialize pipeline variables file
    if (-not (Test-Path $PipelineVarsFile)) {
        $defaultVars = @{
            "global" = @{
                "buildConfiguration" = @{
                    "value" = "Release"
                    "description" = "Build configuration (Debug/Release)"
                    "isSecret" = $false
                    "environments" = @("dev", "staging", "prod")
                }
                "dotNetVersion" = @{
                    "value" = "8.0.x"
                    "description" = ".NET SDK version"
                    "isSecret" = $false
                    "environments" = @("dev", "staging", "prod")
                }
                "containerRegistry" = @{
                    "value" = "notifyregistry.azurecr.io"
                    "description" = "Docker container registry"
                    "isSecret" = $false
                    "environments" = @("staging", "prod")
                }
            }
            "environments" = @{
                "dev" = @{
                    "webAppName" = @{
                        "value" = "notify-service-api-dev"
                        "description" = "Azure Web App name for development"
                        "isSecret" = $false
                    }
                    "resourceGroup" = @{
                        "value" = "notify-service-rg-dev"
                        "description" = "Azure resource group for development"
                        "isSecret" = $false
                    }
                    "databaseConnectionString" = @{
                        "value" = ""
                        "description" = "Database connection string"
                        "isSecret" = $true
                    }
                }
                "staging" = @{
                    "webAppName" = @{
                        "value" = "notify-service-api-staging"
                        "description" = "Azure Web App name for staging"
                        "isSecret" = $false
                    }
                    "resourceGroup" = @{
                        "value" = "notify-service-rg-staging"
                        "description" = "Azure resource group for staging"
                        "isSecret" = $false
                    }
                    "databaseConnectionString" = @{
                        "value" = ""
                        "description" = "Database connection string"
                        "isSecret" = $true
                    }
                }
                "prod" = @{
                    "webAppName" = @{
                        "value" = "notify-service-api-prod"
                        "description" = "Azure Web App name for production"
                        "isSecret" = $false
                    }
                    "resourceGroup" = @{
                        "value" = "notify-service-rg-prod"
                        "description" = "Azure resource group for production"
                        "isSecret" = $false
                    }
                    "databaseConnectionString" = @{
                        "value" = ""
                        "description" = "Database connection string"
                        "isSecret" = $true
                    }
                }
            }
        }
        
        $defaultVars | ConvertTo-Json -Depth 10 | Set-Content -Path $PipelineVarsFile -Encoding UTF8
        Write-ConfigLog "Default pipeline variables created" "SUCCESS"
    }
    
    # Initialize environment configurations
    if (-not (Test-Path $EnvironmentConfigFile)) {
        $defaultEnvConfig = @{
            "environments" = @{
                "dev" = @{
                    "name" = "Development"
                    "azureSubscription" = "dev-subscription"
                    "approvalRequired" = $false
                    "deploymentSlots" = $false
                    "monitoring" = @{
                        "enabled" = $true
                        "alertsEnabled" = $false
                    }
                    "backup" = @{
                        "enabled" = $false
                    }
                }
                "staging" = @{
                    "name" = "Staging"
                    "azureSubscription" = "staging-subscription"
                    "approvalRequired" = $true
                    "deploymentSlots" = $true
                    "monitoring" = @{
                        "enabled" = $true
                        "alertsEnabled" = $true
                    }
                    "backup" = @{
                        "enabled" = $true
                        "schedule" = "daily"
                    }
                }
                "prod" = @{
                    "name" = "Production"
                    "azureSubscription" = "prod-subscription"
                    "approvalRequired" = $true
                    "deploymentSlots" = $true
                    "monitoring" = @{
                        "enabled" = $true
                        "alertsEnabled" = $true
                    }
                    "backup" = @{
                        "enabled" = $true
                        "schedule" = "daily"
                        "retention" = "30days"
                    }
                }
            }
        }
        
        $defaultEnvConfig | ConvertTo-Json -Depth 10 | Set-Content -Path $EnvironmentConfigFile -Encoding UTF8
        Write-ConfigLog "Default environment configurations created" "SUCCESS"
    }
    
    return @{
        "variables" = Get-Content $PipelineVarsFile | ConvertFrom-Json
        "environments" = Get-Content $EnvironmentConfigFile | ConvertFrom-Json
    }
}

function Set-PipelineVariable {
    param(
        [string]$Name,
        [string]$Value,
        [string]$Environment = "global",
        [string]$Description = "",
        [bool]$IsSecret = $false
    )
    
    Write-ConfigLog "Setting variable '$Name' for environment '$Environment'" "INFO"
    
    $config = Get-Content $PipelineVarsFile | ConvertFrom-Json
    
    # Ensure environment exists
    if ($Environment -eq "global") {
        if (-not $config.global) {
            $config | Add-Member -NotePropertyName "global" -NotePropertyValue @{}
        }
        $targetConfig = $config.global
    } else {
        if (-not $config.environments) {
            $config | Add-Member -NotePropertyName "environments" -NotePropertyValue @{}
        }
        if (-not $config.environments.$Environment) {
            $config.environments | Add-Member -NotePropertyName $Environment -NotePropertyValue @{}
        }
        $targetConfig = $config.environments.$Environment
    }
    
    # Set the variable
    $variableData = @{
        "value" = $Value
        "description" = $Description
        "isSecret" = $IsSecret
        "lastModified" = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
    }
    
    if ($Environment -eq "global") {
        $variableData.environments = @("dev", "staging", "prod")
    }
    
    $targetConfig | Add-Member -NotePropertyName $Name -NotePropertyValue $variableData -Force
    
    # Save configuration
    $config | ConvertTo-Json -Depth 10 | Set-Content -Path $PipelineVarsFile -Encoding UTF8
    
    Write-ConfigLog "Variable '$Name' set successfully" "SUCCESS"
}

function Get-PipelineVariable {
    param(
        [string]$Name,
        [string]$Environment = "global"
    )
    
    $config = Get-Content $PipelineVarsFile | ConvertFrom-Json
    
    if ($Environment -eq "global" -and $config.global.$Name) {
        return $config.global.$Name
    } elseif ($config.environments.$Environment.$Name) {
        return $config.environments.$Environment.$Name
    }
    
    return $null
}

function Export-PipelineConfiguration {
    param(
        [string]$ExportPath,
        [string]$Environment = "all"
    )
    
    Write-ConfigLog "Exporting pipeline configuration..." "INFO"
    
    $config = Get-Content $PipelineVarsFile | ConvertFrom-Json
    $envConfig = Get-Content $EnvironmentConfigFile | ConvertFrom-Json
    
    $exportData = @{
        "exportDate" = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
        "environment" = $Environment
        "variables" = @{}
        "environmentConfig" = @{}
    }
    
    if ($Environment -eq "all") {
        $exportData.variables = $config
        $exportData.environmentConfig = $envConfig
    } else {
        # Export specific environment
        $exportData.variables.global = $config.global
        if ($config.environments.$Environment) {
            $exportData.variables.environments = @{}
            $exportData.variables.environments | Add-Member -NotePropertyName $Environment -NotePropertyValue $config.environments.$Environment
        }
        if ($envConfig.environments.$Environment) {
            $exportData.environmentConfig.environments = @{}
            $exportData.environmentConfig.environments | Add-Member -NotePropertyName $Environment -NotePropertyValue $envConfig.environments.$Environment
        }
    }
    
    # Mask secret values in export
    function Mask-Secrets($obj) {
        if ($obj -is [PSCustomObject]) {
            $obj.PSObject.Properties | ForEach-Object {
                if ($_.Name -eq "value" -and $obj.isSecret -eq $true) {
                    $_.Value = "***MASKED***"
                } elseif ($_.Value -is [PSCustomObject] -or $_.Value -is [Array]) {
                    Mask-Secrets $_.Value
                }
            }
        } elseif ($obj -is [Array]) {
            $obj | ForEach-Object { Mask-Secrets $_ }
        }
    }
    
    Mask-Secrets $exportData
    
    $exportData | ConvertTo-Json -Depth 10 | Set-Content -Path $ExportPath -Encoding UTF8
    
    Write-ConfigLog "Configuration exported to: $ExportPath" "SUCCESS"
}

function Show-PipelineVariables {
    param([string]$Environment = "all")
    
    $config = Get-Content $PipelineVarsFile | ConvertFrom-Json
    
    if ($Json) {
        if ($Environment -eq "all") {
            $config | ConvertTo-Json -Depth 10
        } else {
            $result = @{}
            if ($Environment -eq "global" -and $config.global) {
                $result = $config.global
            } elseif ($config.environments.$Environment) {
                $result = $config.environments.$Environment
            }
            $result | ConvertTo-Json -Depth 10
        }
        return
    }
    
    Write-Host ""
    Write-ColorText "📊 Pipeline Variables" "Cyan"
    Write-Host ""
    
    if ($Environment -eq "all" -or $Environment -eq "global") {
        Write-ColorText "🌐 Global Variables:" "Yellow"
        if ($config.global) {
            $config.global.PSObject.Properties | ForEach-Object {
                $var = $_.Value
                $valueDisplay = if ($var.isSecret) { "***SECRET***" } else { $var.value }
                Write-ColorText "  • $($_.Name): $valueDisplay" "White"
                if ($var.description) {
                    Write-ColorText "    └─ $($var.description)" "Gray"
                }
            }
        }
        Write-Host ""
    }
    
    if ($Environment -eq "all") {
        $environments = $config.environments.PSObject.Properties.Name
    } else {
        $environments = @($Environment)
    }
    
    foreach ($env in $environments) {
        if ($config.environments.$env) {
            Write-ColorText "🏷️ Environment: $env" "Yellow"
            $config.environments.$env.PSObject.Properties | ForEach-Object {
                $var = $_.Value
                $valueDisplay = if ($var.isSecret) { "***SECRET***" } else { $var.value }
                Write-ColorText "  • $($_.Name): $valueDisplay" "White"
                if ($var.description) {
                    Write-ColorText "    └─ $($var.description)" "Gray"
                }
            }
            Write-Host ""
        }
    }
}

function Show-ConfigMenu {
    Write-Host ""
    Write-ColorText "⚙️ Pipeline Configuration Management Menu" "Cyan"
    Write-Host ""
    Write-ColorText "    [1] 📝 Set Variable" "Green"
    Write-ColorText "        └─ Add or update a pipeline variable"
    Write-Host ""
    Write-ColorText "    [2] 👁️ View Variables" "Blue"
    Write-ColorText "        └─ Display pipeline variables"
    Write-Host ""
    Write-ColorText "    [3] 📤 Export Configuration" "Yellow"
    Write-ColorText "        └─ Export configuration to file"
    Write-Host ""
    Write-ColorText "    [4] 📥 Import Configuration" "Magenta"
    Write-ColorText "        └─ Import configuration from file"
    Write-Host ""
    Write-ColorText "    [5] 🔄 Sync with Azure DevOps" "Cyan"
    Write-ColorText "        └─ Synchronize with Azure DevOps variables"
    Write-Host ""
    Write-ColorText "    [6] 🏷️ Manage Environments" "Gray"
    Write-ColorText "        └─ Configure environment settings"
    Write-Host ""
    Write-ColorText "    [0] 🚪 Exit" "Gray"
    Write-Host ""
}

# Main execution
Show-Header

$config = Initialize-ConfigurationFiles

if ($Action -eq "menu") {
    do {
        Show-ConfigMenu
        $choice = Read-Host "Enter your choice (0-6)"
        
        switch ($choice) {
            "1" {
                $name = Read-Host "Enter variable name"
                $value = Read-Host "Enter variable value"
                $env = Read-Host "Enter environment (global/dev/staging/prod)"
                $description = Read-Host "Enter description (optional)"
                $isSecret = (Read-Host "Is this a secret? (y/N)") -eq 'y'
                
                if ($name -and $value) {
                    Set-PipelineVariable -Name $name -Value $value -Environment $env -Description $description -IsSecret $isSecret
                } else {
                    Write-ConfigLog "Name and value are required" "ERROR"
                }
                
                Read-Host "Press Enter to continue"
            }
            "2" {
                $env = Read-Host "Enter environment (all/global/dev/staging/prod)"
                if (-not $env) { $env = "all" }
                Show-PipelineVariables -Environment $env
                Read-Host "Press Enter to continue"
            }
            "3" {
                $path = Read-Host "Enter export path"
                $env = Read-Host "Enter environment (all/dev/staging/prod)"
                if (-not $env) { $env = "all" }
                if (-not $path) { $path = "pipeline-config-export-$(Get-Date -Format 'yyyyMMdd-HHmmss').json" }
                
                Export-PipelineConfiguration -ExportPath $path -Environment $env
                Read-Host "Press Enter to continue"
            }
            "4" {
                Write-ConfigLog "Import functionality coming soon..." "INFO"
                Read-Host "Press Enter to continue"
            }
            "5" {
                Write-ConfigLog "Azure DevOps sync functionality coming soon..." "INFO"
                Read-Host "Press Enter to continue"
            }
            "6" {
                Write-ConfigLog "Environment management functionality coming soon..." "INFO"
                Read-Host "Press Enter to continue"
            }
            "0" {
                Write-ConfigLog "Goodbye!" "INFO"
                break
            }
            default {
                Write-ConfigLog "Invalid choice. Please try again." "WARN"
                Read-Host "Press Enter to continue"
            }
        }
    } while ($choice -ne "0")
}
