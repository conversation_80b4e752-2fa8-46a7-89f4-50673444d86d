using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Models.DbEntities;

namespace Data.Mapping;

public class LoginLogMap : MappingEntityTypeConfiguration<LoginLog>
{
    public override void Configure(EntityTypeBuilder<LoginLog> builder)
    {
        builder.ToTable("LoginLogs");
        builder.HasKey(p => p.Id);
        builder.Property(p => p.UserEmail).HasMaxLength(255).IsRequired();
        builder.Property(p => p.LoginTime).HasColumnType("timestamp").HasDefaultValueSql("NOW()");
        builder.Property(p => p.CreateUTC).HasColumnType("timestamp").HasDefaultValueSql("NOW()");
        
        // Add index on UserEmail for better query performance
        builder.HasIndex(p => p.UserEmail);
        
        base.Configure(builder);
    }
}
