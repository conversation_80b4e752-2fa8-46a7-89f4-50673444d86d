# 🏗️ Universal Pipeline Builder Guide

The Universal Pipeline Builder is a comprehensive tool that automatically analyzes your repository structure and generates optimized CI/CD pipelines for both Azure DevOps and GitHub Actions.

## 🚀 Features

### ✨ Smart Repository Analysis
- **Automatic Project Type Detection**: Detects .NET, Node.js, Python, Docker, and more
- **Language Recognition**: Identifies programming languages and frameworks
- **Test Framework Detection**: Finds and configures test runners
- **Docker Support**: Automatically includes Docker build steps when Dockerfile is present
- **Deployment Hints**: Analyzes existing configuration files for deployment targets

### 🎯 Multi-Platform Support
- **Azure DevOps Pipelines**: Generates complete YAML pipelines with stages, jobs, and steps
- **GitHub Actions**: Creates workflows with jobs, matrix builds, and environments
- **Cross-Platform**: Works on Windows (PowerShell) and Unix/Linux/macOS (Bash)

### 🔧 Advanced Configuration
- **Interactive Mode**: Guided setup with user preferences
- **Command Line Interface**: Scriptable automation support
- **Template System**: Reusable pipeline templates
- **Conditional Logic**: Smart branching and environment-based conditions
- **Security Integration**: Built-in security scanning and code quality checks

### 🌐 Deployment Targets
- **Azure**: Web Apps, Container Instances, Kubernetes, Functions
- **AWS**: EC2, ECS, Lambda, Elastic Beanstalk
- **Google Cloud**: Compute Engine, Cloud Run, Functions, GKE
- **Kubernetes**: Any Kubernetes cluster
- **Docker**: Docker Hub, Azure Container Registry, custom registries

## 📋 Quick Start

### Windows (PowerShell)

```powershell
# Interactive mode with guided setup
.\scripts\pipeline-builder.ps1

# Quick build with auto-detection
.\scripts\pipeline-builder.ps1 -Action build -Platform both

# Analyze repository only
.\scripts\pipeline-builder.ps1 -Action analyze -Json
```

### Unix/Linux/macOS (Bash)

```bash
# Make script executable
chmod +x scripts/unix/pipeline-builder.sh

# Interactive mode
./scripts/unix/pipeline-builder.sh

# Build for specific platform
./scripts/unix/pipeline-builder.sh --action build --platform azuredevops

# Analyze repository
./scripts/unix/pipeline-builder.sh --action analyze --json
```

## 🎛️ Interactive Mode

The interactive mode provides a user-friendly wizard that guides you through:

1. **Platform Selection**: Choose Azure DevOps, GitHub Actions, or both
2. **Environment Configuration**: Set up dev, staging, and production environments
3. **Deployment Targets**: Select cloud providers and services
4. **Pipeline Triggers**: Configure when pipelines should run
5. **Branch Patterns**: Define which branches trigger builds
6. **Additional Features**: Enable security scanning, notifications, etc.

### Example Interactive Session

```
🎯 Pipeline Configuration Wizard

Select target platforms:
  [1] Azure DevOps only
  [2] GitHub Actions only
  [3] Both platforms
Enter choice (1-3): 3

Select deployment environments (comma-separated):
Available: dev, staging, prod
Environments: dev,staging,prod

Select deployment targets:
  [1] Azure (Web Apps, Container Instances)
  [2] AWS (EC2, ECS, Lambda)
  [3] Docker Registry
  [4] Kubernetes
  [5] Multiple targets
Enter choice (1-5): 1

Select additional features:
  [1] Code quality checks (SonarQube, CodeQL)
  [2] Security scanning
  [3] Performance testing
  [4] Deployment approvals
  [5] Notifications (Slack, Teams)
Features (comma-separated numbers): 1,2,5
```

## 📊 Repository Analysis

The builder automatically analyzes your repository to detect:

### Project Types
- **.NET Applications**: Detects `*.csproj`, `*.sln`, `global.json`
- **Node.js Applications**: Finds `package.json`, `yarn.lock`, `package-lock.json`
- **Python Applications**: Looks for `requirements.txt`, `setup.py`, `pyproject.toml`
- **Docker Applications**: Identifies `Dockerfile`, `docker-compose.yml`

### Framework Detection
- **Test Frameworks**: xUnit, NUnit, MSTest, Jest, Mocha, PyTest
- **Build Tools**: MSBuild, npm, yarn, pip, Maven, Gradle
- **Languages**: C#, JavaScript, TypeScript, Python, Java, Go, Rust

### Deployment Hints
- **Azure**: `azure-pipelines.yml`, `azuredeploy.json`, `*.bicep`
- **AWS**: `cloudformation.yml`, `serverless.yml`, `sam.yml`
- **Kubernetes**: YAML files with `apiVersion` and `kind` properties

## 🔧 Generated Pipeline Features

### Azure DevOps Pipelines

```yaml
# Example generated pipeline structure
trigger:
  branches:
    include:
      - main
      - develop
      - release/*
      - feature/*

variables:
  buildConfiguration: 'Release'
  vmImageName: 'ubuntu-latest'

stages:
- stage: Build
  displayName: 'Build and Test'
  jobs:
  - job: Build
    steps:
    - task: UseDotNet@2
    - task: DotNetCoreCLI@2  # Restore
    - task: DotNetCoreCLI@2  # Build
    - task: DotNetCoreCLI@2  # Test
    - task: DotNetCoreCLI@2  # Publish

- stage: DeployDev
  displayName: 'Deploy to DEV'
  dependsOn: Build
  jobs:
  - deployment: DeployDev
    environment: 'dev'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebApp@1
```

### GitHub Actions Workflows

```yaml
# Example generated workflow structure
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-dotnet@v4
    - run: dotnet restore
    - run: dotnet build
    - run: dotnet test
    - uses: actions/upload-artifact@v4

  deploy-dev:
    runs-on: ubuntu-latest
    needs: build
    environment: dev
    steps:
    - uses: actions/download-artifact@v4
    - uses: azure/webapps-deploy@v2
```

## 🎨 Customization Options

### Project-Specific Configuration

Create a `pipeline-builder-config.json` file to customize:

```json
{
  "projectTypes": {
    "custom": {
      "name": "Custom Application",
      "detectionFiles": ["custom.config"],
      "buildCommands": ["custom-build", "custom-test"],
      "dockerSupport": true
    }
  },
  "deploymentTargets": {
    "custom-cloud": {
      "name": "Custom Cloud Provider",
      "services": ["custom-service"],
      "authMethods": ["api-key"]
    }
  }
}
```

### Environment Variables

The builder supports environment-specific variables:

```yaml
# Azure DevOps
variables:
- group: 'Dev-Variables'
- name: 'environment'
  value: 'development'

# GitHub Actions
env:
  ENVIRONMENT: development
  API_URL: ${{ secrets.DEV_API_URL }}
```

## 🧪 Acceptance Testing & Quality Gates

### Advanced Testing Features

- **Code Coverage Requirements**: Configurable minimum coverage thresholds with failure conditions
- **Benchmark Testing**: Performance regression detection with baseline comparison
- **Integration Testing**: Test containers, database tests, API validation
- **End-to-End Testing**: Playwright, Selenium, or Cypress support
- **Load Testing**: k6 integration for performance validation
- **Quality Gates**: SonarQube, CodeClimate integration with custom thresholds

### Code Coverage Configuration

```json
{
  "codeCoverage": {
    "minimum": 80,
    "target": 90,
    "failBelowThreshold": true,
    "branchCoverage": {
      "minimum": 75,
      "enabled": true
    },
    "excludePatterns": [
      "**/bin/**",
      "**/obj/**",
      "**/*Test*.cs"
    ]
  }
}
```

### Benchmark Testing

```json
{
  "benchmarkTesting": {
    "enabled": true,
    "framework": "BenchmarkDotNet",
    "thresholds": {
      "maxExecutionTime": 5000,
      "maxMemoryUsage": 100,
      "performanceRegression": 10
    },
    "compareBaseline": true,
    "baselineFile": "performance-baseline.json"
  }
}
```

### Integration Testing with Test Containers

```yaml
# Azure DevOps
- task: DockerCompose@0
  displayName: 'Start test dependencies'
  inputs:
    action: 'Run services'
    dockerComposeFile: 'docker-compose.test.yml'

- task: DotNetCoreCLI@2
  displayName: 'Run integration tests'
  inputs:
    command: 'test'
    projects: '**/*IntegrationTest*.csproj'

# GitHub Actions
- name: Start test dependencies
  run: docker-compose -f docker-compose.test.yml up -d

- name: Run integration tests
  run: dotnet test **/*IntegrationTest*.csproj
```

### Performance Validation Script

The builder includes a Python script for validating performance results:

```bash
# Validate performance against thresholds
python scripts/validate-performance.py \
  --results ./TestResults \
  --threshold 10 \
  --max-time 5000 \
  --max-memory 100 \
  --baseline performance-baseline.json
```

## 🔐 Security Features

### Built-in Security Scanning

- **CodeQL Analysis**: Automatic code security scanning
- **Dependency Scanning**: Vulnerability detection in packages
- **Secret Detection**: Prevents secrets from being committed
- **Container Scanning**: Docker image vulnerability assessment
- **SAST/DAST Integration**: Static and dynamic security testing

### Secrets Management

```yaml
# Azure DevOps
- task: AzureKeyVault@2
  inputs:
    azureSubscription: 'Azure-Subscription'
    KeyVaultName: 'my-keyvault'

# GitHub Actions
- name: Get secrets
  env:
    API_KEY: ${{ secrets.API_KEY }}
    DB_CONNECTION: ${{ secrets.DB_CONNECTION }}
```

## 📈 Advanced Features

### Matrix Builds

```yaml
# GitHub Actions matrix example
strategy:
  matrix:
    os: [ubuntu-latest, windows-latest, macos-latest]
    dotnet-version: ['6.0.x', '8.0.x']
```

### Conditional Deployments

```yaml
# Deploy only on main branch
condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))

# GitHub Actions
if: github.ref == 'refs/heads/main' && github.event_name == 'push'
```

### Notifications

```yaml
# Slack notifications
- name: Notify Slack
  uses: 8398a7/action-slack@v3
  with:
    status: ${{ job.status }}
    text: "Deployment completed"
  env:
    SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
```

## 🛠️ Command Line Reference

### PowerShell Parameters

```powershell
.\scripts\pipeline-builder.ps1 [OPTIONS]

-Action <string>        # menu, analyze, build, validate, export
-Platform <string>      # azuredevops, github, both
-ProjectType <string>   # auto, dotnet, node, python, docker
-OutputPath <string>    # Output directory for generated files
-Interactive           # Interactive mode
-Force                 # Overwrite existing files
-Validate              # Validate generated pipelines
-Json                  # JSON output
```

### Bash Parameters

```bash
./scripts/unix/pipeline-builder.sh [OPTIONS]

--action ACTION        # menu, analyze, build, export
--platform PLATFORM   # azuredevops, github, both
--project-type TYPE    # auto, dotnet, node, python, docker
--output PATH          # Output directory
--interactive          # Interactive mode
--force                # Overwrite existing files
--validate             # Validate pipelines
--json                 # JSON output
```

## 📁 Output Structure

Generated files are organized as follows:

```
generated-pipelines/
├── azure-pipelines.yml           # Azure DevOps pipeline
├── .github/
│   └── workflows/
│       └── ci-cd.yml             # GitHub Actions workflow
└── templates/                    # Reusable templates
    ├── azure-devops/
    │   ├── build-template.yml
    │   └── deploy-template.yml
    └── github-actions/
        ├── build-workflow.yml
        └── deploy-workflow.yml
```

## 🔍 Troubleshooting

### Common Issues

1. **Project Type Not Detected**
   - Ensure project files are in the repository root
   - Check file naming conventions
   - Use `--project-type` parameter to override

2. **Missing Dependencies**
   - Verify required tools are installed (.NET SDK, Node.js, etc.)
   - Check PATH environment variable
   - Use Docker-based builds for consistency

3. **Permission Issues**
   - Ensure script execution permissions on Unix systems
   - Check file write permissions in output directory
   - Verify Azure/GitHub service connections

### Debug Mode

Enable verbose logging:

```powershell
# PowerShell
.\scripts\pipeline-builder.ps1 -Verbose

# Bash
./scripts/unix/pipeline-builder.sh --verbose
```

## 🤝 Contributing

To extend the pipeline builder:

1. **Add New Project Types**: Update `projectTypes` in configuration
2. **Add Deployment Targets**: Extend `deploymentTargets` configuration
3. **Create Templates**: Add new templates in the templates directory
4. **Enhance Detection**: Improve repository analysis logic

## 📚 Examples

See the `examples/` directory for complete pipeline examples:
- [.NET Web API](examples/dotnet-webapi/)
- [Node.js Express App](examples/nodejs-express/)
- [Python Flask App](examples/python-flask/)
- [Docker Multi-stage](examples/docker-multistage/)

## 🆘 Support

For issues and questions:
- Check the troubleshooting section above
- Review generated pipeline syntax
- Validate against platform documentation
- Test with minimal examples first
