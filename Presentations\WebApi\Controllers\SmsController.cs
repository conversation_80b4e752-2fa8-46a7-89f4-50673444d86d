using Core.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using Models.DTOs.SMS;
using System.Threading;
using System.Threading.Tasks;

namespace WebApi.Controllers;

[Route("api/[controller]")]
[ApiController]
public class SmsController : ControllerBase, IMessageSender<SmsPayload, SmsResult>
{
    private readonly IMessageSender<SmsPayload, SmsResult> _smsSender;

    public SmsController(IMessageSender<SmsPayload, SmsResult> smsSender)
    {
        _smsSender = smsSender;
    }

    public Task<SmsResult> SendAsync(SmsPayload payload, CancellationToken cancellationToken = default)
    {
        return _smsSender.SendAsync(payload, cancellationToken);
    }

    [HttpPost("send")]
    public Task<IReadOnlyList<SmsResult>> SendBulkAsync(IEnumerable<SmsPayload> payloads, CancellationToken cancellationToken = default)
    {
        var results =  _smsSender.SendBulkAsync(payloads, cancellationToken);
        return results;
    }
}
