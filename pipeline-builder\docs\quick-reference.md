# ⚡ Universal Pipeline Builder - Quick Reference

## 🚀 Quick Commands

### Basic Pipeline Generation
```powershell
# Interactive mode (recommended for beginners)
.\pipeline-builder.ps1 -Interactive

# Quick generation for specific project type
.\pipeline-builder.ps1 -Action build -Platform both -ProjectType dotnet

# Analyze repository
.\pipeline-builder.ps1 -Action analyze

# Generate with validation
.\pipeline-builder.ps1 -Action build -Platform azuredevops -Validate
```

### FTP/SFTP Deployment
```powershell
# Launch FTP/SFTP module
.\pipeline-builder.ps1 -Action sftp

# Test SFTP connection
.\core\ftp-sftp-deployment.ps1 -Action test -Protocol sftp -Environment prod

# Generate SSH key
.\core\ftp-sftp-deployment.ps1 -Action generate-key -KeyName "prod_rsa" -KeyType "rsa"
```

### Advanced Features
```powershell
# Advanced deployment strategies
.\scripts\advanced-pipeline-features.ps1 -Feature canary

# Chaos engineering
.\scripts\advanced-pipeline-features.ps1 -Feature chaos

# Deploy observability stack
docker-compose -f pipeline-templates/observability-stack.yml up -d
```

### Templates & Validation
```powershell
# List available templates
.\core\template-system.ps1 -Action list

# Apply template
.\core\template-system.ps1 -Action apply -TemplateName "dotnet-webapi" -Platform "azuredevops"

# Validate pipeline
.\core\pipeline-validator.ps1 -Action validate -PipelineFile "azure-pipelines.yml"
```

## 📋 Command Reference

### Main Pipeline Builder

| Parameter | Description | Values | Example |
|-----------|-------------|---------|---------|
| `-Action` | Action to perform | `menu`, `analyze`, `build`, `validate`, `export`, `ftp`, `sftp` | `-Action build` |
| `-Platform` | Target platform | `azuredevops`, `github`, `both` | `-Platform both` |
| `-ProjectType` | Project type | `auto`, `dotnet`, `node`, `python`, `docker` | `-ProjectType dotnet` |
| `-OutputPath` | Output directory | File path | `-OutputPath "C:\Pipelines"` |
| `-Interactive` | Interactive mode | Switch | `-Interactive` |
| `-Force` | Overwrite files | Switch | `-Force` |
| `-Validate` | Validate output | Switch | `-Validate` |
| `-Json` | JSON output | Switch | `-Json` |
| `-Verbose` | Verbose logging | Switch | `-Verbose` |

### FTP/SFTP Module

| Parameter | Description | Values | Example |
|-----------|-------------|---------|---------|
| `-Action` | Action to perform | `menu`, `deploy`, `test`, `configure` | `-Action test` |
| `-Protocol` | Transfer protocol | `ftp`, `sftp`, `scp`, `rsync` | `-Protocol sftp` |
| `-Environment` | Target environment | `dev`, `staging`, `prod` | `-Environment prod` |
| `-TestConnection` | Test only | Switch | `-TestConnection` |
| `-DryRun` | Dry run mode | Switch | `-DryRun` |

### Template System

| Parameter | Description | Values | Example |
|-----------|-------------|---------|---------|
| `-Action` | Action to perform | `menu`, `create`, `list`, `apply`, `export` | `-Action apply` |
| `-TemplateName` | Template name | String | `-TemplateName "dotnet-webapi"` |
| `-Platform` | Target platform | `azuredevops`, `github` | `-Platform azuredevops` |
| `-OutputPath` | Output directory | File path | `-OutputPath ".\output"` |

### Pipeline Validator

| Parameter | Description | Values | Example |
|-----------|-------------|---------|---------|
| `-Action` | Action to perform | `menu`, `validate`, `test`, `lint`, `fix` | `-Action validate` |
| `-PipelineFile` | Pipeline file | File path | `-PipelineFile "azure-pipelines.yml"` |
| `-Platform` | Platform type | `azuredevops`, `github`, `auto` | `-Platform auto` |
| `-Fix` | Auto-fix issues | Switch | `-Fix` |

## 🎯 Common Workflows

### 1. New Project Setup
```powershell
# Step 1: Analyze project
.\pipeline-builder.ps1 -Action analyze

# Step 2: Generate pipelines
.\pipeline-builder.ps1 -Interactive

# Step 3: Validate output
.\core\pipeline-validator.ps1 -Action validate -PipelineFile "azure-pipelines.yml"

# Step 4: Commit to repository
git add azure-pipelines.yml .github/workflows/
git commit -m "Add CI/CD pipelines"
```

### 2. SFTP Deployment Setup
```powershell
# Step 1: Generate SSH keys
.\core\ftp-sftp-deployment.ps1 -Action menu
# Select: [1] SSH Key Management → [1] Generate new SSH key pair

# Step 2: Configure deployment targets
# Edit: pipeline-builder/config/ftp-sftp-config.json

# Step 3: Test connection
.\core\ftp-sftp-deployment.ps1 -Action test -Protocol sftp -Environment prod

# Step 4: Generate pipeline with SFTP deployment
.\pipeline-builder.ps1 -Action build -Platform azuredevops
# Select SFTP as deployment target
```

### 3. Advanced Deployment Strategy
```powershell
# Step 1: Generate base pipeline
.\pipeline-builder.ps1 -Action build -Platform both

# Step 2: Add canary deployment
.\scripts\advanced-pipeline-features.ps1 -Feature canary -Platform azuredevops

# Step 3: Configure monitoring
.\scripts\monitor-canary.py --duration 30m --error-threshold 0.01

# Step 4: Deploy observability
docker-compose -f pipeline-templates/observability-stack.yml up -d
```

## 🔧 Configuration Files

### Main Configuration
```json
// pipeline-builder/config/pipeline-builder-config.json
{
  "defaultPlatform": "azuredevops",
  "defaultProjectType": "auto",
  "outputDirectory": "./generated-pipelines",
  "enableValidation": true,
  "features": {
    "securityScanning": true,
    "codeQuality": true,
    "acceptanceTesting": true
  }
}
```

### FTP/SFTP Configuration
```json
// pipeline-builder/config/ftp-sftp-config.json
{
  "environments": {
    "prod": {
      "sftp": {
        "host": "prod-server.com",
        "port": 22,
        "username": "deploy",
        "keyFile": "prod_rsa",
        "remotePath": "/var/www/production"
      }
    }
  }
}
```

### Template Variables
```json
// Template variable substitution
{
  "PROJECT_NAME": "MyWebAPI",
  "BUILD_CONFIGURATION": "Release",
  "TARGET_FRAMEWORK": "net8.0",
  "DEPLOYMENT_ENVIRONMENTS": ["dev", "staging", "prod"],
  "ENABLE_UNIT_TESTS": true,
  "CODE_COVERAGE_THRESHOLD": 80
}
```

## 🚨 Troubleshooting Quick Fixes

### SSH Connection Issues
```bash
# Fix key permissions
chmod 600 ~/.ssh/private_key
chmod 644 ~/.ssh/private_key.pub

# Test SSH connection
ssh -vvv -i ~/.ssh/private_key <EMAIL>

# Add host to known_hosts
ssh-keyscan server.com >> ~/.ssh/known_hosts
```

### Pipeline Validation Errors
```powershell
# Common fixes for Azure DevOps
# 1. Fix YAML indentation
# 2. Update deprecated tasks (e.g., DotNetCoreCLI@1 → DotNetCoreCLI@2)
# 3. Add missing trigger definition
# 4. Replace hardcoded secrets with variables

# Common fixes for GitHub Actions
# 1. Add workflow name
# 2. Update deprecated actions (e.g., actions/checkout@v2 → actions/checkout@v4)
# 3. Fix conditional syntax (use == instead of eq())
# 4. Use proper secrets syntax: ${{ secrets.SECRET_NAME }}
```

### Template Issues
```powershell
# Regenerate default templates
.\core\template-system.ps1 -Action create

# Check template syntax
.\core\pipeline-validator.ps1 -Action validate -PipelineFile "template-output.yml"

# Debug template variables
.\core\template-system.ps1 -Action apply -TemplateName "template-name" -Verbose
```

## 📊 Feature Matrix

| Feature | Azure DevOps | GitHub Actions | Notes |
|---------|---------------|----------------|-------|
| Basic CI/CD | ✅ | ✅ | Full support |
| Multi-environment | ✅ | ✅ | Dev, staging, prod |
| FTP Deployment | ✅ | ✅ | Username/password auth |
| SFTP Deployment | ✅ | ✅ | SSH key auth |
| SCP Deployment | ✅ | ✅ | SSH key auth |
| Rsync Deployment | ✅ | ✅ | SSH key auth |
| Canary Deployment | ✅ | ✅ | Advanced strategy |
| Blue-Green Deployment | ✅ | ✅ | Advanced strategy |
| Chaos Engineering | ✅ | ✅ | Kubernetes required |
| Observability | ✅ | ✅ | Docker required |
| SLI/SLO Monitoring | ✅ | ✅ | Prometheus required |
| Security Scanning | ✅ | ✅ | CodeQL, SAST, DAST |
| Code Quality | ✅ | ✅ | SonarQube integration |
| Acceptance Testing | ✅ | ✅ | Coverage, benchmarks |

## 🔗 Quick Links

- **Main Documentation**: [comprehensive-user-guide.md](comprehensive-user-guide.md)
- **SSH Key Management**: [ssh-key-management.md](ssh-key-management.md)
- **Advanced Features**: [advanced-pipeline-features.md](advanced-pipeline-features.md)
- **Security Guide**: [security-best-practices.md](security-best-practices.md)
- **Performance Guide**: [performance-optimization.md](performance-optimization.md)

## 💡 Tips & Tricks

### Performance Optimization
- Use parallel jobs for independent tasks
- Implement caching for dependencies
- Minimize artifact sizes
- Use matrix builds for multiple configurations

### Security Best Practices
- Never commit SSH private keys
- Use service connections for external services
- Implement secret scanning in pipelines
- Regular key rotation (90 days recommended)

### Debugging
- Use `-Verbose` flag for detailed logging
- Test SSH connections before deployment
- Validate pipelines before committing
- Use dry-run mode for testing deployments

### Automation
- Use JSON output for scripting: `-Json`
- Batch operations with configuration files
- Integrate with existing CI/CD tools
- Use templates for consistent pipelines

---

*For detailed information, see the [Comprehensive User Guide](comprehensive-user-guide.md)*
